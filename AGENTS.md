# AGENTS.md - Guidelines for AI Agents

This file provides essential guidelines for AI agents operating in this repository.

## Project Structure
- `api_service/`: FastAPI application.
- `core_utils/`: Shared utility functions.
- `deeplearning/`: Machine learning models and pipelines.
- `worker/`: Celery background tasks.
- `webui/`: Streamlit user interface.
- `tests/`: Contains tests for different modules.

## Build, Lint, and Test Commands
- **Install dependencies**: `pip install -r requirements.txt`
- **Run tests**: `pytest`
- **Run tests for a specific module**: `pytest deeplearning/`
- **Run a single test file**: `pytest deeplearning/test_cv.py`
- **Run a single test function**: `pytest deeplearning/test_cv.py::test_function_name`

## Code Style and Conventions
- **Formatting**: Adhere to PEP 8 standards with 4-space indentation.
- **Naming**:
  - `snake_case` for functions, methods, and variables.
  - `CamelCase` for classes.
  - `UPPER_SNAKE_CASE` for constants.
- **Type Hinting**: Use type hints for all function signatures.
- **Imports**: Use absolute imports (`from core_utils import db`).
- **Error Handling**: Use guard clauses and early returns. Raise `HTTPException` for API errors.
- **FastAPI**:
  - Use `async def` for I/O-bound operations.
  - Use Pydantic models for request/response validation.
  - Use dependency injection for managing resources.
