from fastapi import APIRouter, HTTPException, Query, Depends
from datetime import date
from typing import List, Optional, Union
from app.services.recommend_service import StockRecommendService
from app.services.pattern_service import PatternService
from app.services.risk_service import RiskService
from app.models.recommend_model import StockRecommend, ReturnStockRecommend
from app.models.pattern_model import BuyPattern, ReturnBuyPattern
from app.models.risk_model import RiskRating
from app.core.auth import get_api_key
from app.core.logging import get_logger
import logging

logger = get_logger(__name__)
router = APIRouter()

# Initialize services
logger.info("Initializing API services...")
recommend_service = StockRecommendService()
pattern_service = PatternService()
riskrating_service = RiskService()
logger.info("✅ All API services initialized successfully")


@router.get("/recommends", response_model=List[ReturnStockRecommend])
async def get_recommends(
        target_date: date = Query(..., description="Date to get data YYYY-MM-DD"),
        signal_type: Optional[str] = Query(None, description="buy/sell"),
        api_key: str = Depends(get_api_key)
):
    """
    Get list of recommendation by date
    """
    logger.info(f"📊 GET /recommends - date: {target_date}, signal_type: {signal_type}")
    try:
        stocks = await recommend_service.get_by_date(
            target_date=target_date,
            signal_type=signal_type,
        )
        if not stocks:
            logger.warning(f"No recommendations found for date {target_date} with signal_type {signal_type}")
            raise HTTPException(status_code=404, detail="Recommendation not found")

        logger.info(f"✅ Successfully returned {len(stocks)} recommendations for {target_date}")
        return stocks
    except HTTPException as e:
        logger.warning(f"HTTP error in get_recommends: {e.detail}")
        raise e
    except Exception as e:
        logger.error(f"❌ Unexpected error in get_recommends: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/recommends/{ticker}", response_model=List[StockRecommend])
async def get_recommend(
        ticker: str,
        target_date: Optional[date] = Query(None, description="Date to get data"),
        api_key: str = Depends(get_api_key)
):
    """
    Get list of recommendation by ticker
    """
    logger.info(f"📈 GET /recommends/{ticker} - target_date: {target_date}")
    try:
        stocks = await recommend_service.get_by_code(
            stock_code=ticker,
            target_date=target_date
        )
        if not stocks:
            logger.warning(f"No recommendations found for ticker {ticker} on date {target_date}")
            raise HTTPException(status_code=404, detail="Recommendation not found")

        logger.info(f"✅ Successfully returned {len(stocks)} recommendations for ticker {ticker}")
        return stocks
    except HTTPException as e:
        logger.warning(f"HTTP error in get_recommend: {e.detail}")
        raise e
    except Exception as e:
        logger.error(f"❌ Unexpected error in get_recommend for {ticker}: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/recommends", response_model=bool)
async def insert_recommends(
        recommend_data: Union[StockRecommend, List[StockRecommend]],
        api_key: str = Depends(get_api_key)
):
    """
    Insert new stock recommends
    """
    data_count = len(recommend_data) if isinstance(recommend_data, list) else 1
    logger.info(f"💾 POST /recommends - inserting {data_count} recommendation(s)")

    try:
        success = await recommend_service.insert_db(recommend_data)
        if not success:
            logger.error("Failed to insert recommendation data")
            raise HTTPException(status_code=500, detail="Can't insert datas")

        logger.info(f"✅ Successfully inserted {data_count} recommendation(s)")
        return success
    except HTTPException as e:
        logger.warning(f"HTTP error in insert_recommends: {e.detail}")
        raise e
    except Exception as e:
        logger.error(f"❌ Unexpected error in insert_recommends: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/patterns", response_model=List[BuyPattern])
async def get_patterns(
        api_key: str = Depends(get_api_key)
):
    """
    Get patterns
    """
    logger.info("🎯 GET /patterns - retrieving all patterns")
    try:
        buy_patterns = await pattern_service.get_patterns()
        if not buy_patterns:
            logger.warning("No patterns found in database")
            raise HTTPException(status_code=404, detail="Patterns not found")

        logger.info(f"✅ Successfully returned {len(buy_patterns)} patterns")
        return buy_patterns
    except HTTPException as e:
        logger.warning(f"HTTP error in get_patterns: {e.detail}")
        raise e
    except Exception as e:
        logger.error(f"❌ Unexpected error in get_patterns: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/patterns/stategy", response_model=List[ReturnBuyPattern])
async def get_patterns_by_strategy(
        strategy: str = Query("ranking_point", description="ranking_point/weight_score"),
        api_key: str = Depends(get_api_key)
):
    """
    Get patterns by strategy
    """
    logger.info(f"🎯 GET /patterns/strategy - strategy: {strategy}")
    try:
        buy_patterns = await pattern_service.get_patterns_by_strategy(strategy)
        if not buy_patterns:
            logger.warning(f"No patterns found for strategy: {strategy}")
            raise HTTPException(status_code=404, detail="Patterns not found")

        logger.info(f"✅ Successfully returned {len(buy_patterns)} patterns for strategy {strategy}")
        return buy_patterns
    except HTTPException as e:
        logger.warning(f"HTTP error in get_patterns_by_strategy: {e.detail}")
        raise e
    except Exception as e:
        logger.error(f"❌ Unexpected error in get_patterns_by_strategy: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/patterns/{pattern}", response_model=BuyPattern)
async def get_pattern(
        pattern: str,
        api_key: str = Depends(get_api_key)
):
    """
    Get pattern
    """
    try:
        buy_pattern = await pattern_service.get_pattern(
            pattern=pattern
        )
        if not buy_pattern:
            raise HTTPException(status_code=404, detail="Pattern not found")
        return buy_pattern
    except HTTPException as e:
        raise e
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/patterns/{pattern}", response_model=bool)
async def update_pattern(
        pattern: str,
        data: BuyPattern,
        api_key: str = Depends(get_api_key)
):
    """
    Update pattern
    """
    try:
        success = await pattern_service.update_pattern(
            pattern=pattern,
            new_data=data
        )
        if not success:
            raise HTTPException(status_code=500, detail=f"Can't update pattern {pattern}")
        return success
    except HTTPException as e:
        raise e
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/patterns", response_model=bool)
async def insert_pattern(
        pattern: BuyPattern,
        api_key: str = Depends(get_api_key)
):
    """
    Insert new pattern
    """
    try:
        success = await pattern_service.insert_pattern(
            pattern_data=pattern
        )
        if not success:
            raise HTTPException(status_code=500, detail=f"Can't insert data {pattern.buy_pattern}")
        return success
    except HTTPException as e:
        raise e
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


# riskrating
@router.get("/riskrating/{ticker}", response_model=RiskRating)
async def get_riskrating_by_ticker(
        ticker: str,
        api_key: str = Depends(get_api_key)
):
    """
    Get riskrating by ticker
    """
    logger.info(f"⚠️ GET /riskrating/{ticker}")
    try:
        riskrating = await riskrating_service.get_risk_by_ticker(
            ticker=ticker
        )
        if not riskrating:
            logger.warning(f"No risk rating found for ticker: {ticker}")
            raise HTTPException(status_code=404, detail="Riskrating not found")

        logger.info(f"✅ Successfully returned risk rating for ticker {ticker}")
        return riskrating
    except HTTPException as e:
        logger.warning(f"HTTP error in get_riskrating_by_ticker: {e.detail}")
        raise e
    except Exception as e:
        logger.error(f"❌ Unexpected error in get_riskrating_by_ticker for {ticker}: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/riskrating", response_model=List[RiskRating])
async def get_riskrating(
        api_key: str = Depends(get_api_key),
        quarter: Optional[str] = Query(default=None)
):
    """
    Get riskrating
    """
    logger.info(f"⚠️ GET /riskrating - quarter: {quarter}")
    try:
        riskrating = await riskrating_service.get_risks(quarter)
        if not riskrating:
            logger.warning(f"No risk ratings found for quarter: {quarter}")
            raise HTTPException(status_code=404, detail="Riskrating not found")

        logger.info(f"✅ Successfully returned {len(riskrating)} risk ratings for quarter {quarter}")
        return riskrating
    except HTTPException as e:
        logger.warning(f"HTTP error in get_riskrating: {e.detail}")
        raise e
    except Exception as e:
        logger.error(f"❌ Unexpected error in get_riskrating: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/riskrating", response_model=bool)
async def insert_riskrating(
        riskrating_data: Union[RiskRating, List[RiskRating]],
        api_key: str = Depends(get_api_key)
):
    """
    Insert new riskrating
    """
    data_count = len(riskrating_data) if isinstance(riskrating_data, list) else 1
    logger.info(f"💾 POST /riskrating - inserting {data_count} risk rating(s)")

    try:
        success = await riskrating_service.insert_db(
            risk_data=riskrating_data
        )
        if not success:
            logger.error("Failed to insert risk rating data")
            raise HTTPException(status_code=500, detail=f"Can't insert data {riskrating_data}")

        logger.info(f"✅ Successfully inserted {data_count} risk rating(s)")
        return success
    except HTTPException as e:
        logger.warning(f"HTTP error in insert_riskrating: {e.detail}")
        raise e
    except Exception as e:
        logger.error(f"❌ Unexpected error in insert_riskrating: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))
