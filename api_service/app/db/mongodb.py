from motor.motor_asyncio import AsyncIOMotorClient
from app.core.config import settings
import logging

logger = logging.getLogger(__name__)


class MongoDB:
    client: AsyncIOMotorClient = None
    db = None


async def connect_to_mongo():
    logger.info(f"🔌 Connecting to MongoDB at {settings.MONGO_URL}")
    logger.debug(f"Database name: {settings.MONGO_DB_NAME}")

    try:
        MongoDB.client = AsyncIOMotorClient(settings.MONGO_URL)
        MongoDB.db = MongoDB.client[settings.MONGO_DB_NAME]

        # Test the connection
        await MongoDB.client.admin.command('ping')
        logger.info("✅ Successfully connected to MongoDB")
        logger.debug(f"Connected to database: {settings.MONGO_DB_NAME}")

    except Exception as e:
        logger.error(f"❌ Failed to connect to MongoDB: {str(e)}", exc_info=True)
        raise


async def close_mongo_connection():
    logger.info("🔌 Closing MongoDB connection...")
    try:
        if MongoDB.client:
            MongoDB.client.close()
            logger.info("✅ MongoDB connection closed successfully")
        else:
            logger.warning("⚠️ No MongoDB client to close")
    except Exception as e:
        logger.error(f"❌ Error closing MongoDB connection: {str(e)}", exc_info=True)


def get_database():
    if MongoDB.db is None:
        logger.warning("⚠️ Database not initialized. Call connect_to_mongo() first.")
    return MongoDB.db
