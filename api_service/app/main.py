from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
from app.core.config import settings
from app.api.routes import router as api_router
from app.core.logging import setup_logging, get_logger
from app.core.middleware import LoggingMiddleware
from app.db.mongodb import connect_to_mongo, close_mongo_connection
import logging

# Setup enhanced logging
setup_logging()
logger = get_logger(__name__)

app = FastAPI(
    title=settings.PROJECT_NAME,
    openapi_url=f"{settings.API_V1_PREFIX}/openapi.json",
    description="Stock Data API with enhanced logging for debugging",
    version="1.0.0"
)

# Add logging middleware (before CORS)
app.add_middleware(
    LoggingMiddleware,
    log_requests=True,
    log_responses=True
)

# CORS middleware configuration
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include API router
app.include_router(api_router, prefix=settings.API_V1_PREFIX)


@app.on_event("startup")
async def startup_event():
    logger.info("🚀 Starting up Stock Data API...")
    try:
        await connect_to_mongo()
        logger.info("✅ Successfully connected to MongoDB")
        logger.info("🎯 API is ready to serve requests")
    except Exception as e:
        logger.error(f"❌ Failed to start up API: {str(e)}", exc_info=True)
        raise


@app.on_event("shutdown")
async def shutdown_event():
    logger.info("🛑 Shutting down Stock Data API...")
    try:
        await close_mongo_connection()
        logger.info("✅ Successfully disconnected from MongoDB")
        logger.info("👋 API shutdown completed")
    except Exception as e:
        logger.error(f"❌ Error during shutdown: {str(e)}", exc_info=True)


@app.get("/")
async def root():
    logger.debug("Root endpoint accessed")
    return {
        "message": "Welcome to Stock Data API",
        "version": "1.0.0",
        "status": "running",
        "docs": f"{settings.API_V1_PREFIX}/docs"
    }
