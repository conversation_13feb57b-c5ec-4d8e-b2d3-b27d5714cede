#!/usr/bin/env python3
"""
Demo script để test toàn bộ logging system
"""

import asyncio
import logging
import sys
import os
from datetime import datetime, date

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def demo_basic_logging():
    """Demo basic logging features"""
    print("🧪 Demo: Basic Logging Features")
    print("-" * 40)
    
    # Setup basic logging
    logging.basicConfig(
        level=logging.DEBUG,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler('logs/demo.log')
        ]
    )
    
    logger = logging.getLogger("demo")
    
    # Test different log levels
    logger.debug("🔍 DEBUG: Detailed information for debugging")
    logger.info("ℹ️ INFO: General information about application flow")
    logger.warning("⚠️ WARNING: Something unexpected happened")
    logger.error("❌ ERROR: An error occurred")
    
    # Test structured logging
    logger.info("📊 API Request processed", extra={
        'endpoint': '/api/v1/recommends',
        'method': 'GET',
        'status_code': 200,
        'response_time': 0.123,
        'user_id': 'demo_user'
    })
    
    print("✅ Basic logging demo completed\n")

def demo_service_logging():
    """Demo service-level logging patterns"""
    print("🧪 Demo: Service-Level Logging")
    print("-" * 40)
    
    logger = logging.getLogger("demo.service")
    
    # Simulate service operations
    logger.info("🚀 Starting recommendation service operation")
    
    # Simulate database query
    logger.debug("🗄️ Executing database query")
    logger.debug("Query: {'hit_date': '2024-01-15', 'signal': 'buy'}")
    
    # Simulate processing
    logger.debug("📊 Processing 25 records from database")
    logger.debug("Applying filters and transformations")
    
    # Simulate success
    logger.info("✅ Service operation completed successfully")
    logger.info("Returned 25 recommendations to client")
    
    print("✅ Service logging demo completed\n")

def demo_api_logging():
    """Demo API endpoint logging patterns"""
    print("🧪 Demo: API Endpoint Logging")
    print("-" * 40)
    
    logger = logging.getLogger("demo.api")
    
    # Simulate API requests
    endpoints = [
        ("GET", "/api/v1/recommends", {"date": "2024-01-15", "signal": "buy"}),
        ("POST", "/api/v1/patterns", {"pattern": "bullish_engulfing"}),
        ("GET", "/api/v1/riskrating/VIC", {}),
    ]
    
    for method, endpoint, params in endpoints:
        # Log request
        logger.info(f"📡 {method} {endpoint}")
        if params:
            logger.debug(f"Parameters: {params}")
        
        # Simulate processing
        logger.debug("Processing request...")
        
        # Log response
        if method == "GET":
            logger.info(f"✅ Response: 200 OK, returned data")
        else:
            logger.info(f"✅ Response: 201 Created")
    
    print("✅ API logging demo completed\n")

def demo_error_logging():
    """Demo error logging and exception handling"""
    print("🧪 Demo: Error Logging")
    print("-" * 40)
    
    logger = logging.getLogger("demo.error")
    
    # Simulate different types of errors
    try:
        # Simulate validation error
        raise ValueError("Invalid date format: expected YYYY-MM-DD")
    except ValueError as e:
        logger.warning(f"⚠️ Validation error: {str(e)}")
    
    try:
        # Simulate database error
        raise ConnectionError("Failed to connect to MongoDB")
    except ConnectionError as e:
        logger.error(f"❌ Database connection error: {str(e)}", exc_info=True)
    
    try:
        # Simulate unexpected error
        result = 10 / 0
    except Exception as e:
        logger.error(f"❌ Unexpected error in calculation: {str(e)}", exc_info=True)
    
    print("✅ Error logging demo completed\n")

def demo_performance_logging():
    """Demo performance and timing logging"""
    print("🧪 Demo: Performance Logging")
    print("-" * 40)
    
    logger = logging.getLogger("demo.performance")
    
    import time
    
    # Simulate slow operations
    operations = [
        ("Database query", 0.1),
        ("Data processing", 0.05),
        ("Response serialization", 0.02),
    ]
    
    total_start = time.time()
    
    for operation, duration in operations:
        logger.debug(f"⏱️ Starting: {operation}")
        start_time = time.time()
        
        # Simulate work
        time.sleep(duration)
        
        end_time = time.time()
        actual_duration = end_time - start_time
        logger.debug(f"✅ Completed: {operation} in {actual_duration:.3f}s")
    
    total_duration = time.time() - total_start
    logger.info(f"📊 Total request processing time: {total_duration:.3f}s")
    
    print("✅ Performance logging demo completed\n")

def demo_business_logic_logging():
    """Demo business logic and data flow logging"""
    print("🧪 Demo: Business Logic Logging")
    print("-" * 40)
    
    logger = logging.getLogger("demo.business")
    
    # Simulate recommendation algorithm
    logger.info("🎯 Starting stock recommendation algorithm")
    
    # Input data
    logger.debug("📥 Input: 500 stocks, 20 technical indicators")
    logger.debug("Filters: market_cap > 1B, volume > 1M")
    
    # Processing steps
    logger.debug("🔍 Step 1: Filtering stocks by criteria")
    logger.debug("Result: 150 stocks passed initial filters")
    
    logger.debug("🔍 Step 2: Calculating technical scores")
    logger.debug("Applied 20 technical indicators")
    
    logger.debug("🔍 Step 3: Risk assessment")
    logger.debug("Applied risk scoring model")
    
    logger.debug("🔍 Step 4: Final ranking")
    logger.debug("Sorted by composite score")
    
    # Output
    logger.info("📤 Output: Top 25 stock recommendations")
    logger.debug("Score range: 85-95 (out of 100)")
    
    print("✅ Business logic logging demo completed\n")

def demo_security_logging():
    """Demo security and audit logging"""
    print("🧪 Demo: Security & Audit Logging")
    print("-" * 40)
    
    logger = logging.getLogger("demo.security")
    
    # Simulate authentication
    logger.info("🔐 Authentication attempt")
    logger.debug("API key validation: SUCCESS")
    logger.info("✅ User authenticated successfully")
    
    # Simulate authorization
    logger.debug("🛡️ Checking user permissions for endpoint: /api/v1/patterns")
    logger.info("✅ User authorized for requested operation")
    
    # Simulate data access
    logger.info("📊 Data access: patterns collection")
    logger.debug("Query: user requested all patterns")
    logger.info("✅ Data access granted, returned 15 patterns")
    
    # Simulate suspicious activity
    logger.warning("⚠️ Rate limit warning: 90 requests in last minute")
    logger.warning("⚠️ Unusual access pattern detected for user")
    
    print("✅ Security logging demo completed\n")

def main():
    """Run all logging demos"""
    print("🚀 Stock Data API - Logging System Demo")
    print("=" * 60)
    print(f"📅 Demo started at: {datetime.now()}")
    print("=" * 60)
    print()
    
    # Ensure logs directory exists
    os.makedirs('logs', exist_ok=True)
    
    try:
        demo_basic_logging()
        demo_service_logging()
        demo_api_logging()
        demo_error_logging()
        demo_performance_logging()
        demo_business_logic_logging()
        demo_security_logging()
        
        print("=" * 60)
        print("🎉 All logging demos completed successfully!")
        print()
        print("📁 Log files created:")
        print("   📄 logs/demo.log - All demo logs")
        print("   📄 Console output - Real-time logs")
        print()
        print("💡 Key logging features demonstrated:")
        print("   ✅ Multiple log levels (DEBUG, INFO, WARNING, ERROR)")
        print("   ✅ Structured logging with extra data")
        print("   ✅ Exception logging with stack traces")
        print("   ✅ Performance timing")
        print("   ✅ Business logic flow")
        print("   ✅ Security and audit trails")
        print("   ✅ Service operation tracking")
        print("   ✅ API request/response logging")
        print()
        print("🔍 To view logs:")
        print("   tail -f logs/demo.log")
        print("   grep 'ERROR' logs/demo.log")
        print("   grep 'Performance' logs/demo.log")
        
    except Exception as e:
        print(f"❌ Demo failed with error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
