2025-03-30 12:11:37,956 - app.core.logging - INFO - Logging setup completed
2025-03-30 12:11:37,972 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-03-30 12:11:37,984 - app.db.mongodb - INFO - Connected to MongoDB
2025-03-30 12:11:45,207 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-03-30 12:11:45,207 - app.db.mongodb - INFO - MongoDB connection closed
2025-03-30 12:11:49,359 - app.core.logging - INFO - Logging setup completed
2025-03-30 12:11:49,375 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-03-30 12:11:49,380 - app.db.mongodb - INFO - Connected to MongoDB
2025-03-30 12:13:22,392 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-03-30 12:13:22,392 - app.db.mongodb - INFO - MongoDB connection closed
2025-03-30 12:13:23,469 - app.core.logging - INFO - Logging setup completed
2025-03-30 12:13:23,489 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-03-30 12:13:23,497 - app.db.mongodb - INFO - Connected to MongoDB
2025-03-30 12:13:50,777 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-03-30 12:13:50,777 - app.db.mongodb - INFO - MongoDB connection closed
2025-03-30 12:13:51,807 - app.core.logging - INFO - Logging setup completed
2025-03-30 12:13:51,830 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-03-30 12:13:51,840 - app.db.mongodb - INFO - Connected to MongoDB
2025-03-30 12:13:52,142 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-03-30 12:13:52,145 - app.db.mongodb - INFO - MongoDB connection closed
2025-03-30 12:13:55,895 - app.core.logging - INFO - Logging setup completed
2025-03-30 12:13:55,908 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-03-30 12:13:55,915 - app.db.mongodb - INFO - Connected to MongoDB
2025-03-30 12:14:02,943 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-03-30 12:14:02,944 - app.db.mongodb - INFO - MongoDB connection closed
2025-03-30 12:14:17,865 - app.core.logging - INFO - Logging setup completed
2025-03-30 12:14:17,883 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-03-30 12:14:17,889 - app.db.mongodb - INFO - Connected to MongoDB
2025-03-30 12:15:15,688 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-03-30 12:15:15,688 - app.db.mongodb - INFO - MongoDB connection closed
2025-03-30 12:15:16,475 - app.core.logging - INFO - Logging setup completed
2025-03-30 12:15:16,489 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-03-30 12:15:16,494 - app.db.mongodb - INFO - Connected to MongoDB
2025-03-30 12:25:19,856 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-03-30 12:25:19,856 - app.db.mongodb - INFO - MongoDB connection closed
2025-03-30 12:25:20,711 - app.core.logging - INFO - Logging setup completed
2025-03-30 12:25:20,728 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-03-30 12:25:20,736 - app.db.mongodb - INFO - Connected to MongoDB
2025-03-30 12:29:32,531 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-03-30 12:29:32,532 - app.db.mongodb - INFO - MongoDB connection closed
2025-03-30 12:29:33,362 - app.core.logging - INFO - Logging setup completed
2025-03-30 12:29:33,376 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-03-30 12:29:33,381 - app.db.mongodb - INFO - Connected to MongoDB
2025-03-30 12:30:36,226 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-03-30 12:30:36,227 - app.db.mongodb - INFO - MongoDB connection closed
2025-03-30 12:30:37,124 - app.core.logging - INFO - Logging setup completed
2025-03-30 12:30:37,138 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-03-30 12:30:37,144 - app.db.mongodb - INFO - Connected to MongoDB
2025-03-30 14:37:59,830 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-03-30 14:37:59,830 - app.db.mongodb - INFO - MongoDB connection closed
2025-03-30 14:38:00,729 - app.core.logging - INFO - Logging setup completed
2025-03-30 14:38:00,747 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-03-30 14:38:00,753 - app.db.mongodb - INFO - Connected to MongoDB
2025-03-30 14:41:54,799 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-03-30 14:41:54,800 - app.db.mongodb - INFO - MongoDB connection closed
2025-03-30 14:42:00,529 - app.core.logging - INFO - Logging setup completed
2025-03-30 14:42:00,552 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-03-30 14:42:00,560 - app.db.mongodb - INFO - Connected to MongoDB
2025-03-30 14:44:05,840 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-03-30 14:44:05,841 - app.db.mongodb - INFO - MongoDB connection closed
2025-03-30 14:44:06,725 - app.core.logging - INFO - Logging setup completed
2025-03-30 14:44:06,747 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-03-30 14:44:06,756 - app.db.mongodb - INFO - Connected to MongoDB
2025-03-30 14:44:12,776 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-03-30 14:44:12,777 - app.db.mongodb - INFO - MongoDB connection closed
2025-03-30 14:44:13,734 - app.core.logging - INFO - Logging setup completed
2025-03-30 14:44:13,749 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-03-30 14:44:13,755 - app.db.mongodb - INFO - Connected to MongoDB
2025-03-30 14:45:29,135 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-03-30 14:45:29,137 - app.db.mongodb - INFO - MongoDB connection closed
2025-03-30 14:45:29,968 - app.core.logging - INFO - Logging setup completed
2025-03-30 14:45:29,985 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-03-30 14:45:29,991 - app.db.mongodb - INFO - Connected to MongoDB
2025-03-30 14:59:41,153 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-03-30 14:59:41,157 - app.db.mongodb - INFO - MongoDB connection closed
2025-03-30 14:59:42,548 - app.core.logging - INFO - Logging setup completed
2025-03-30 14:59:42,575 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-03-30 14:59:42,588 - app.db.mongodb - INFO - Connected to MongoDB
2025-03-30 14:59:45,603 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-03-30 14:59:45,603 - app.db.mongodb - INFO - MongoDB connection closed
2025-03-30 14:59:58,639 - app.core.logging - INFO - Logging setup completed
2025-03-30 14:59:58,656 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-03-30 14:59:58,665 - app.db.mongodb - INFO - Connected to MongoDB
2025-03-30 15:02:32,042 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-03-30 15:02:32,042 - app.db.mongodb - INFO - MongoDB connection closed
2025-03-30 15:02:32,912 - app.core.logging - INFO - Logging setup completed
2025-03-30 15:02:32,929 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-03-30 15:02:32,940 - app.db.mongodb - INFO - Connected to MongoDB
2025-03-30 15:02:50,891 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-03-30 15:02:50,891 - app.db.mongodb - INFO - MongoDB connection closed
2025-03-30 15:02:51,846 - app.core.logging - INFO - Logging setup completed
2025-03-30 15:02:51,861 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-03-30 15:02:51,868 - app.db.mongodb - INFO - Connected to MongoDB
2025-03-30 15:05:07,468 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-03-30 15:05:07,468 - app.db.mongodb - INFO - MongoDB connection closed
2025-03-30 15:05:08,413 - app.core.logging - INFO - Logging setup completed
2025-03-30 15:05:08,435 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-03-30 15:05:08,443 - app.db.mongodb - INFO - Connected to MongoDB
2025-03-30 15:05:47,074 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-03-30 15:05:47,075 - app.db.mongodb - INFO - MongoDB connection closed
2025-03-30 15:05:47,990 - app.core.logging - INFO - Logging setup completed
2025-03-30 15:05:48,014 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-03-30 15:05:48,022 - app.db.mongodb - INFO - Connected to MongoDB
2025-03-30 15:05:50,530 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-03-30 15:05:50,531 - app.db.mongodb - INFO - MongoDB connection closed
2025-03-30 15:05:51,514 - app.core.logging - INFO - Logging setup completed
2025-03-30 15:05:51,536 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-03-30 15:05:51,544 - app.db.mongodb - INFO - Connected to MongoDB
2025-03-30 15:08:17,180 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-03-30 15:08:17,181 - app.db.mongodb - INFO - MongoDB connection closed
2025-03-30 15:08:18,210 - app.core.logging - INFO - Logging setup completed
2025-03-30 15:08:18,233 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-03-30 15:08:18,242 - app.db.mongodb - INFO - Connected to MongoDB
2025-03-30 15:08:55,771 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-03-30 15:08:55,771 - app.db.mongodb - INFO - MongoDB connection closed
2025-03-30 15:08:56,708 - app.core.logging - INFO - Logging setup completed
2025-03-30 15:08:56,723 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-03-30 15:08:56,729 - app.db.mongodb - INFO - Connected to MongoDB
2025-03-30 15:11:28,388 - app.core.auth - WARNING - Invalid API key attempt: 123...
2025-03-30 15:14:35,046 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-03-30 15:14:35,047 - app.db.mongodb - INFO - MongoDB connection closed
2025-03-30 15:14:35,849 - app.core.logging - INFO - Logging setup completed
2025-03-30 15:14:35,865 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-03-30 15:14:35,871 - app.db.mongodb - INFO - Connected to MongoDB
2025-03-30 15:16:03,905 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-03-30 15:16:03,905 - app.db.mongodb - INFO - MongoDB connection closed
2025-03-30 15:16:04,826 - app.core.logging - INFO - Logging setup completed
2025-03-30 15:16:04,844 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-03-30 15:16:04,851 - app.db.mongodb - INFO - Connected to MongoDB
2025-03-30 15:16:15,687 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-03-30 15:16:15,688 - app.db.mongodb - INFO - MongoDB connection closed
2025-03-30 15:16:16,762 - app.core.logging - INFO - Logging setup completed
2025-03-30 15:16:16,782 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-03-30 15:16:16,790 - app.db.mongodb - INFO - Connected to MongoDB
2025-03-30 15:16:47,684 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-03-30 15:16:47,685 - app.db.mongodb - INFO - MongoDB connection closed
2025-03-30 15:17:00,385 - app.core.logging - INFO - Logging setup completed
2025-03-30 15:17:00,403 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-03-30 15:17:00,415 - app.db.mongodb - INFO - Connected to MongoDB
2025-03-30 15:26:08,789 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-03-30 15:26:08,789 - app.db.mongodb - INFO - MongoDB connection closed
2025-03-30 15:26:09,668 - app.core.logging - INFO - Logging setup completed
2025-03-30 15:26:09,684 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-03-30 15:26:09,692 - app.db.mongodb - INFO - Connected to MongoDB
2025-03-30 15:26:17,819 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-03-30 15:26:17,819 - app.db.mongodb - INFO - MongoDB connection closed
2025-03-30 15:26:18,655 - app.core.logging - INFO - Logging setup completed
2025-03-30 15:26:18,669 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-03-30 15:26:18,676 - app.db.mongodb - INFO - Connected to MongoDB
2025-03-30 15:27:56,163 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-03-30 15:27:56,164 - app.db.mongodb - INFO - MongoDB connection closed
2025-03-30 15:27:57,085 - app.core.logging - INFO - Logging setup completed
2025-03-30 15:27:57,102 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-03-30 15:27:57,109 - app.db.mongodb - INFO - Connected to MongoDB
2025-03-30 15:28:29,420 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-03-30 15:28:29,421 - app.db.mongodb - INFO - MongoDB connection closed
2025-03-30 15:29:04,560 - app.core.logging - INFO - Logging setup completed
2025-03-30 15:29:04,581 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-03-30 15:29:04,589 - app.db.mongodb - INFO - Connected to MongoDB
2025-03-30 15:29:06,098 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-03-30 15:29:06,099 - app.db.mongodb - INFO - MongoDB connection closed
2025-03-30 15:29:07,008 - app.core.logging - INFO - Logging setup completed
2025-03-30 15:29:07,023 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-03-30 15:29:07,029 - app.db.mongodb - INFO - Connected to MongoDB
2025-03-30 16:58:56,207 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-03-30 16:58:56,219 - app.db.mongodb - INFO - MongoDB connection closed
2025-03-31 07:13:15,111 - app.core.logging - INFO - Logging setup completed
2025-03-31 07:13:15,131 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-03-31 07:13:15,139 - app.db.mongodb - INFO - Connected to MongoDB
2025-03-31 07:28:20,766 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-03-31 07:28:20,776 - app.db.mongodb - INFO - MongoDB connection closed
2025-03-31 07:30:30,186 - app.core.logging - INFO - Logging setup completed
2025-03-31 07:30:30,200 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-03-31 07:30:30,208 - app.db.mongodb - INFO - Connected to MongoDB
2025-03-31 07:31:04,661 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-03-31 07:31:04,661 - app.db.mongodb - INFO - MongoDB connection closed
2025-03-31 07:31:05,424 - app.core.logging - INFO - Logging setup completed
2025-03-31 07:31:05,447 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-03-31 07:31:05,456 - app.db.mongodb - INFO - Connected to MongoDB
2025-03-31 07:31:09,172 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-03-31 07:31:09,173 - app.db.mongodb - INFO - MongoDB connection closed
2025-03-31 07:31:09,871 - app.core.logging - INFO - Logging setup completed
2025-03-31 07:31:09,884 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-03-31 07:31:09,889 - app.db.mongodb - INFO - Connected to MongoDB
2025-03-31 07:31:10,293 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-03-31 07:31:10,293 - app.db.mongodb - INFO - MongoDB connection closed
2025-03-31 07:31:13,500 - app.core.logging - INFO - Logging setup completed
2025-03-31 07:31:13,514 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-03-31 07:31:13,520 - app.db.mongodb - INFO - Connected to MongoDB
2025-03-31 07:34:23,076 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-03-31 07:34:23,077 - app.db.mongodb - INFO - MongoDB connection closed
2025-03-31 07:34:23,805 - app.core.logging - INFO - Logging setup completed
2025-03-31 07:34:23,820 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-03-31 07:34:23,825 - app.db.mongodb - INFO - Connected to MongoDB
2025-03-31 07:34:34,185 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-03-31 07:34:34,185 - app.db.mongodb - INFO - MongoDB connection closed
2025-03-31 07:34:34,955 - app.core.logging - INFO - Logging setup completed
2025-03-31 07:34:34,971 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-03-31 07:34:34,977 - app.db.mongodb - INFO - Connected to MongoDB
2025-03-31 07:35:56,538 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-03-31 07:35:56,539 - app.db.mongodb - INFO - MongoDB connection closed
2025-03-31 07:35:57,282 - app.core.logging - INFO - Logging setup completed
2025-03-31 07:35:57,296 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-03-31 07:35:57,302 - app.db.mongodb - INFO - Connected to MongoDB
2025-03-31 07:37:38,554 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-03-31 07:37:38,557 - app.db.mongodb - INFO - MongoDB connection closed
2025-03-31 07:37:39,329 - app.core.logging - INFO - Logging setup completed
2025-03-31 07:37:39,352 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-03-31 07:37:39,358 - app.db.mongodb - INFO - Connected to MongoDB
2025-03-31 07:38:00,957 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-03-31 07:38:00,960 - app.db.mongodb - INFO - MongoDB connection closed
2025-03-31 07:38:06,845 - app.core.logging - INFO - Logging setup completed
2025-03-31 07:38:06,859 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-03-31 07:38:06,867 - app.db.mongodb - INFO - Connected to MongoDB
2025-03-31 07:38:52,072 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-03-31 07:38:52,074 - app.db.mongodb - INFO - MongoDB connection closed
2025-03-31 07:38:52,844 - app.core.logging - INFO - Logging setup completed
2025-03-31 07:38:52,857 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-03-31 07:38:52,865 - app.db.mongodb - INFO - Connected to MongoDB
2025-03-31 07:39:20,690 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-03-31 07:39:20,691 - app.db.mongodb - INFO - MongoDB connection closed
2025-03-31 07:39:21,363 - app.core.logging - INFO - Logging setup completed
2025-03-31 07:39:21,377 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-03-31 07:39:21,383 - app.db.mongodb - INFO - Connected to MongoDB
2025-03-31 07:39:29,720 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-03-31 07:39:29,721 - app.db.mongodb - INFO - MongoDB connection closed
2025-03-31 07:39:30,447 - app.core.logging - INFO - Logging setup completed
2025-03-31 07:39:30,460 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-03-31 07:39:30,467 - app.db.mongodb - INFO - Connected to MongoDB
2025-03-31 07:43:37,594 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-03-31 07:43:37,597 - app.db.mongodb - INFO - MongoDB connection closed
2025-03-31 07:43:38,360 - app.core.logging - INFO - Logging setup completed
2025-03-31 07:43:38,373 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-03-31 07:43:38,378 - app.db.mongodb - INFO - Connected to MongoDB
2025-03-31 07:45:28,877 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-03-31 07:45:28,880 - app.db.mongodb - INFO - MongoDB connection closed
2025-03-31 07:45:29,650 - app.core.logging - INFO - Logging setup completed
2025-03-31 07:45:29,663 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-03-31 07:45:29,669 - app.db.mongodb - INFO - Connected to MongoDB
2025-03-31 07:45:45,541 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-03-31 07:45:45,542 - app.db.mongodb - INFO - MongoDB connection closed
2025-03-31 07:45:46,292 - app.core.logging - INFO - Logging setup completed
2025-03-31 07:45:46,305 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-03-31 07:45:46,310 - app.db.mongodb - INFO - Connected to MongoDB
2025-03-31 07:47:46,552 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-03-31 07:47:46,555 - app.db.mongodb - INFO - MongoDB connection closed
2025-03-31 07:47:47,403 - app.core.logging - INFO - Logging setup completed
2025-03-31 07:47:47,416 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-03-31 07:47:47,422 - app.db.mongodb - INFO - Connected to MongoDB
2025-03-31 07:49:32,077 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-03-31 07:49:32,078 - app.db.mongodb - INFO - MongoDB connection closed
2025-03-31 07:49:32,837 - app.core.logging - INFO - Logging setup completed
2025-03-31 07:49:32,850 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-03-31 07:49:32,856 - app.db.mongodb - INFO - Connected to MongoDB
2025-03-31 07:49:34,127 - app.core.auth - WARNING - Invalid API key attempt: your...
2025-03-31 07:50:07,104 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-03-31 07:50:07,104 - app.db.mongodb - INFO - MongoDB connection closed
2025-03-31 07:50:07,828 - app.core.logging - INFO - Logging setup completed
2025-03-31 07:50:07,842 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-03-31 07:50:07,848 - app.db.mongodb - INFO - Connected to MongoDB
2025-03-31 07:53:52,135 - app.services.recommend_service - INFO - Successfully inserted ticker data for VNM on 2024-02-01
2025-03-31 07:54:44,287 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-03-31 07:54:44,288 - app.db.mongodb - INFO - MongoDB connection closed
2025-03-31 07:54:45,000 - app.core.logging - INFO - Logging setup completed
2025-03-31 07:54:45,013 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-03-31 07:54:45,019 - app.db.mongodb - INFO - Connected to MongoDB
2025-03-31 07:57:00,098 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-03-31 07:57:00,101 - app.db.mongodb - INFO - MongoDB connection closed
2025-03-31 07:57:13,124 - app.core.logging - INFO - Logging setup completed
2025-03-31 07:57:13,137 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-03-31 07:57:13,144 - app.db.mongodb - INFO - Connected to MongoDB
2025-03-31 07:58:17,724 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-03-31 07:58:17,724 - app.db.mongodb - INFO - MongoDB connection closed
2025-03-31 07:58:18,579 - app.core.logging - INFO - Logging setup completed
2025-03-31 07:58:18,591 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-03-31 07:58:18,597 - app.db.mongodb - INFO - Connected to MongoDB
2025-03-31 08:00:19,478 - app.services.recommend_service - INFO - Successfully inserted ticker data for VNM on 2024-03-01
2025-03-31 08:02:59,331 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-03-31 08:02:59,334 - app.db.mongodb - INFO - MongoDB connection closed
2025-03-31 08:03:00,067 - app.core.logging - INFO - Logging setup completed
2025-03-31 08:03:00,079 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-03-31 08:03:00,085 - app.db.mongodb - INFO - Connected to MongoDB
2025-03-31 08:03:41,771 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-03-31 08:03:41,772 - app.db.mongodb - INFO - MongoDB connection closed
2025-03-31 08:03:42,529 - app.core.logging - INFO - Logging setup completed
2025-03-31 08:03:42,541 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-03-31 08:03:42,547 - app.db.mongodb - INFO - Connected to MongoDB
2025-03-31 08:04:26,942 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-03-31 08:04:26,943 - app.db.mongodb - INFO - MongoDB connection closed
2025-03-31 08:04:27,783 - app.core.logging - INFO - Logging setup completed
2025-03-31 08:04:27,796 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-03-31 08:04:27,802 - app.db.mongodb - INFO - Connected to MongoDB
2025-03-31 08:04:34,978 - app.services.recommend_service - INFO - Successfully inserted ticker data for NHH on 2024-02-01
2025-03-31 08:10:43,211 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-03-31 08:10:43,212 - app.db.mongodb - INFO - MongoDB connection closed
2025-03-31 08:10:43,903 - app.core.logging - INFO - Logging setup completed
2025-03-31 08:10:43,915 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-03-31 08:10:43,920 - app.db.mongodb - INFO - Connected to MongoDB
2025-03-31 08:10:52,256 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-03-31 08:10:52,256 - app.db.mongodb - INFO - MongoDB connection closed
2025-03-31 08:10:52,995 - app.core.logging - INFO - Logging setup completed
2025-03-31 08:10:53,009 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-03-31 08:10:53,014 - app.db.mongodb - INFO - Connected to MongoDB
2025-03-31 08:11:09,987 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-03-31 08:11:09,987 - app.db.mongodb - INFO - MongoDB connection closed
2025-03-31 08:11:10,766 - app.core.logging - INFO - Logging setup completed
2025-03-31 08:11:10,780 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-03-31 08:11:10,785 - app.db.mongodb - INFO - Connected to MongoDB
2025-03-31 08:12:45,799 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-03-31 08:12:45,800 - app.db.mongodb - INFO - MongoDB connection closed
2025-03-31 08:12:46,593 - app.core.logging - INFO - Logging setup completed
2025-03-31 08:12:46,612 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-03-31 08:12:46,620 - app.db.mongodb - INFO - Connected to MongoDB
2025-03-31 08:13:56,035 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-03-31 08:13:56,040 - app.db.mongodb - INFO - MongoDB connection closed
2025-03-31 08:13:56,796 - app.core.logging - INFO - Logging setup completed
2025-03-31 08:13:56,809 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-03-31 08:13:56,815 - app.db.mongodb - INFO - Connected to MongoDB
2025-03-31 08:14:39,079 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-03-31 08:14:39,081 - app.db.mongodb - INFO - MongoDB connection closed
2025-03-31 08:14:39,872 - app.core.logging - INFO - Logging setup completed
2025-03-31 08:14:39,886 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-03-31 08:14:39,895 - app.db.mongodb - INFO - Connected to MongoDB
2025-03-31 08:14:53,246 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-03-31 08:14:53,247 - app.db.mongodb - INFO - MongoDB connection closed
2025-03-31 08:14:53,997 - app.core.logging - INFO - Logging setup completed
2025-03-31 08:14:54,012 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-03-31 08:14:54,018 - app.db.mongodb - INFO - Connected to MongoDB
2025-03-31 08:16:13,556 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-03-31 08:16:13,556 - app.db.mongodb - INFO - MongoDB connection closed
2025-03-31 08:16:14,360 - app.core.logging - INFO - Logging setup completed
2025-03-31 08:16:14,373 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-03-31 08:16:14,380 - app.db.mongodb - INFO - Connected to MongoDB
2025-03-31 08:16:21,609 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-03-31 08:16:21,609 - app.db.mongodb - INFO - MongoDB connection closed
2025-03-31 08:16:22,318 - app.core.logging - INFO - Logging setup completed
2025-03-31 08:16:22,334 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-03-31 08:16:22,339 - app.db.mongodb - INFO - Connected to MongoDB
2025-03-31 08:16:25,357 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-03-31 08:16:25,358 - app.db.mongodb - INFO - MongoDB connection closed
2025-03-31 08:16:26,050 - app.core.logging - INFO - Logging setup completed
2025-03-31 08:16:26,064 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-03-31 08:16:26,070 - app.db.mongodb - INFO - Connected to MongoDB
2025-03-31 08:16:46,155 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-03-31 08:16:46,156 - app.db.mongodb - INFO - MongoDB connection closed
2025-03-31 08:16:46,947 - app.core.logging - INFO - Logging setup completed
2025-03-31 08:16:46,964 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-03-31 08:16:46,970 - app.db.mongodb - INFO - Connected to MongoDB
2025-03-31 08:17:07,654 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-03-31 08:17:07,655 - app.db.mongodb - INFO - MongoDB connection closed
2025-03-31 08:17:08,444 - app.core.logging - INFO - Logging setup completed
2025-03-31 08:17:08,464 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-03-31 08:17:08,471 - app.db.mongodb - INFO - Connected to MongoDB
2025-03-31 08:17:18,013 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-03-31 08:17:18,014 - app.db.mongodb - INFO - MongoDB connection closed
2025-03-31 08:17:18,793 - app.core.logging - INFO - Logging setup completed
2025-03-31 08:17:18,807 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-03-31 08:17:18,812 - app.db.mongodb - INFO - Connected to MongoDB
2025-03-31 08:17:33,976 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-03-31 08:17:33,976 - app.db.mongodb - INFO - MongoDB connection closed
2025-03-31 08:17:34,767 - app.core.logging - INFO - Logging setup completed
2025-03-31 08:17:34,781 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-03-31 08:17:34,787 - app.db.mongodb - INFO - Connected to MongoDB
2025-03-31 08:17:41,517 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-03-31 08:17:41,518 - app.db.mongodb - INFO - MongoDB connection closed
2025-03-31 08:17:42,288 - app.core.logging - INFO - Logging setup completed
2025-03-31 08:17:42,300 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-03-31 08:17:42,306 - app.db.mongodb - INFO - Connected to MongoDB
2025-03-31 08:18:14,739 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-03-31 08:18:14,740 - app.db.mongodb - INFO - MongoDB connection closed
2025-03-31 08:18:15,510 - app.core.logging - INFO - Logging setup completed
2025-03-31 08:18:15,523 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-03-31 08:18:15,529 - app.db.mongodb - INFO - Connected to MongoDB
2025-03-31 08:18:36,218 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-03-31 08:18:36,221 - app.db.mongodb - INFO - MongoDB connection closed
2025-03-31 08:18:36,978 - app.core.logging - INFO - Logging setup completed
2025-03-31 08:18:36,993 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-03-31 08:18:36,999 - app.db.mongodb - INFO - Connected to MongoDB
2025-03-31 08:18:51,362 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-03-31 08:18:51,363 - app.db.mongodb - INFO - MongoDB connection closed
2025-03-31 08:18:52,106 - app.core.logging - INFO - Logging setup completed
2025-03-31 08:18:52,118 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-03-31 08:18:52,123 - app.db.mongodb - INFO - Connected to MongoDB
2025-03-31 08:19:01,264 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-03-31 08:19:01,265 - app.db.mongodb - INFO - MongoDB connection closed
2025-03-31 08:19:04,291 - app.core.logging - INFO - Logging setup completed
2025-03-31 08:19:04,305 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-03-31 08:19:04,310 - app.db.mongodb - INFO - Connected to MongoDB
2025-03-31 08:19:27,109 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-03-31 08:19:27,112 - app.db.mongodb - INFO - MongoDB connection closed
2025-03-31 08:19:27,834 - app.core.logging - INFO - Logging setup completed
2025-03-31 08:19:27,847 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-03-31 08:19:27,854 - app.db.mongodb - INFO - Connected to MongoDB
2025-03-31 08:22:03,316 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-03-31 08:22:03,317 - app.db.mongodb - INFO - MongoDB connection closed
2025-03-31 08:22:04,044 - app.core.logging - INFO - Logging setup completed
2025-03-31 08:22:04,056 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-03-31 08:22:04,061 - app.db.mongodb - INFO - Connected to MongoDB
2025-03-31 08:22:14,206 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-03-31 08:22:14,207 - app.db.mongodb - INFO - MongoDB connection closed
2025-03-31 08:22:14,967 - app.core.logging - INFO - Logging setup completed
2025-03-31 08:22:14,980 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-03-31 08:22:14,986 - app.db.mongodb - INFO - Connected to MongoDB
2025-03-31 08:23:04,192 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-03-31 08:23:04,193 - app.db.mongodb - INFO - MongoDB connection closed
2025-03-31 08:23:04,909 - app.core.logging - INFO - Logging setup completed
2025-03-31 08:23:04,922 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-03-31 08:23:04,927 - app.db.mongodb - INFO - Connected to MongoDB
2025-03-31 08:23:53,430 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-03-31 08:23:53,431 - app.db.mongodb - INFO - MongoDB connection closed
2025-03-31 08:23:54,214 - app.core.logging - INFO - Logging setup completed
2025-03-31 08:23:54,231 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-03-31 08:23:54,237 - app.db.mongodb - INFO - Connected to MongoDB
2025-03-31 08:26:28,602 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-03-31 08:26:28,603 - app.db.mongodb - INFO - MongoDB connection closed
2025-03-31 08:26:29,316 - app.core.logging - INFO - Logging setup completed
2025-03-31 08:26:29,330 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-03-31 08:26:29,336 - app.db.mongodb - INFO - Connected to MongoDB
2025-03-31 08:28:28,854 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-03-31 08:28:28,855 - app.db.mongodb - INFO - MongoDB connection closed
2025-03-31 08:28:29,640 - app.core.logging - INFO - Logging setup completed
2025-03-31 08:28:29,654 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-03-31 08:28:29,659 - app.db.mongodb - INFO - Connected to MongoDB
2025-03-31 08:28:41,209 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-03-31 08:28:41,209 - app.db.mongodb - INFO - MongoDB connection closed
2025-03-31 08:28:41,975 - app.core.logging - INFO - Logging setup completed
2025-03-31 08:28:41,990 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-03-31 08:28:41,996 - app.db.mongodb - INFO - Connected to MongoDB
2025-03-31 08:29:32,988 - asyncio - ERROR - Future exception was never retrieved
future: <Future finished exception=InvalidDocument("cannot encode object: datetime.date(2024, 2, 1), of type: <class 'datetime.date'>")>
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/motor/core.py", line 1696, in _to_list
    result = get_more_result.result()
  File "/usr/local/lib/python3.8/concurrent/futures/thread.py", line 57, in run
    result = self.fn(*self.args, **self.kwargs)
  File "/usr/local/lib/python3.8/site-packages/pymongo/synchronous/cursor.py", line 1205, in _refresh
    self._send_message(q)
  File "/usr/local/lib/python3.8/site-packages/pymongo/synchronous/cursor.py", line 1100, in _send_message
    response = client._run_operation(
  File "/usr/local/lib/python3.8/site-packages/pymongo/_csot.py", line 120, in csot_wrapper
    return func(self, *args, **kwargs)
  File "/usr/local/lib/python3.8/site-packages/pymongo/synchronous/mongo_client.py", line 1754, in _run_operation
    return self._retryable_read(
  File "/usr/local/lib/python3.8/site-packages/pymongo/synchronous/mongo_client.py", line 1863, in _retryable_read
    return self._retry_internal(
  File "/usr/local/lib/python3.8/site-packages/pymongo/_csot.py", line 120, in csot_wrapper
    return func(self, *args, **kwargs)
  File "/usr/local/lib/python3.8/site-packages/pymongo/synchronous/mongo_client.py", line 1819, in _retry_internal
    return _ClientConnectionRetryable(
  File "/usr/local/lib/python3.8/site-packages/pymongo/synchronous/mongo_client.py", line 2554, in run
    return self._read() if self._is_read else self._write()
  File "/usr/local/lib/python3.8/site-packages/pymongo/synchronous/mongo_client.py", line 2697, in _read
    return self._func(self._session, self._server, conn, read_pref)  # type: ignore
  File "/usr/local/lib/python3.8/site-packages/pymongo/synchronous/mongo_client.py", line 1745, in _cmd
    return server.run_operation(
  File "/usr/local/lib/python3.8/site-packages/pymongo/synchronous/helpers.py", line 45, in inner
    return func(*args, **kwargs)
  File "/usr/local/lib/python3.8/site-packages/pymongo/synchronous/server.py", line 170, in run_operation
    message = operation.get_message(read_preference, conn, use_cmd)
  File "/usr/local/lib/python3.8/site-packages/pymongo/message.py", line 1693, in get_message
    request_id, msg, size, _ = _op_msg(
  File "/usr/local/lib/python3.8/site-packages/pymongo/message.py", line 415, in _op_msg
    return _op_msg_uncompressed(flags, command, identifier, docs, opts)
bson.errors.InvalidDocument: cannot encode object: datetime.date(2024, 2, 1), of type: <class 'datetime.date'>
2025-03-31 08:29:32,999 - asyncio - ERROR - Future exception was never retrieved
future: <Future finished exception=InvalidDocument("cannot encode object: datetime.date(2024, 2, 1), of type: <class 'datetime.date'>")>
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/motor/core.py", line 1696, in _to_list
    result = get_more_result.result()
  File "/usr/local/lib/python3.8/concurrent/futures/thread.py", line 57, in run
    result = self.fn(*self.args, **self.kwargs)
  File "/usr/local/lib/python3.8/site-packages/pymongo/synchronous/cursor.py", line 1205, in _refresh
    self._send_message(q)
  File "/usr/local/lib/python3.8/site-packages/pymongo/synchronous/cursor.py", line 1100, in _send_message
    response = client._run_operation(
  File "/usr/local/lib/python3.8/site-packages/pymongo/_csot.py", line 120, in csot_wrapper
    return func(self, *args, **kwargs)
  File "/usr/local/lib/python3.8/site-packages/pymongo/synchronous/mongo_client.py", line 1754, in _run_operation
    return self._retryable_read(
  File "/usr/local/lib/python3.8/site-packages/pymongo/synchronous/mongo_client.py", line 1863, in _retryable_read
    return self._retry_internal(
  File "/usr/local/lib/python3.8/site-packages/pymongo/_csot.py", line 120, in csot_wrapper
    return func(self, *args, **kwargs)
  File "/usr/local/lib/python3.8/site-packages/pymongo/synchronous/mongo_client.py", line 1819, in _retry_internal
    return _ClientConnectionRetryable(
  File "/usr/local/lib/python3.8/site-packages/pymongo/synchronous/mongo_client.py", line 2554, in run
    return self._read() if self._is_read else self._write()
  File "/usr/local/lib/python3.8/site-packages/pymongo/synchronous/mongo_client.py", line 2697, in _read
    return self._func(self._session, self._server, conn, read_pref)  # type: ignore
  File "/usr/local/lib/python3.8/site-packages/pymongo/synchronous/mongo_client.py", line 1745, in _cmd
    return server.run_operation(
  File "/usr/local/lib/python3.8/site-packages/pymongo/synchronous/helpers.py", line 45, in inner
    return func(*args, **kwargs)
  File "/usr/local/lib/python3.8/site-packages/pymongo/synchronous/server.py", line 170, in run_operation
    message = operation.get_message(read_preference, conn, use_cmd)
  File "/usr/local/lib/python3.8/site-packages/pymongo/message.py", line 1693, in get_message
    request_id, msg, size, _ = _op_msg(
  File "/usr/local/lib/python3.8/site-packages/pymongo/message.py", line 415, in _op_msg
    return _op_msg_uncompressed(flags, command, identifier, docs, opts)
bson.errors.InvalidDocument: cannot encode object: datetime.date(2024, 2, 1), of type: <class 'datetime.date'>
2025-03-31 08:31:38,241 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-03-31 08:31:38,242 - app.db.mongodb - INFO - MongoDB connection closed
2025-03-31 08:31:38,977 - app.core.logging - INFO - Logging setup completed
2025-03-31 08:31:38,991 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-03-31 08:31:38,996 - app.db.mongodb - INFO - Connected to MongoDB
2025-03-31 08:34:56,328 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-03-31 08:34:56,329 - app.db.mongodb - INFO - MongoDB connection closed
2025-03-31 08:34:57,050 - app.core.logging - INFO - Logging setup completed
2025-03-31 08:34:57,063 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-03-31 08:34:57,069 - app.db.mongodb - INFO - Connected to MongoDB
2025-03-31 08:35:07,918 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-03-31 08:35:07,919 - app.db.mongodb - INFO - MongoDB connection closed
2025-03-31 08:35:08,698 - app.core.logging - INFO - Logging setup completed
2025-03-31 08:35:08,711 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-03-31 08:35:08,718 - app.db.mongodb - INFO - Connected to MongoDB
2025-03-31 08:36:40,701 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-03-31 08:36:40,702 - app.db.mongodb - INFO - MongoDB connection closed
2025-03-31 08:36:41,578 - app.core.logging - INFO - Logging setup completed
2025-03-31 08:36:41,599 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-03-31 08:36:41,608 - app.db.mongodb - INFO - Connected to MongoDB
2025-03-31 08:36:49,240 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-03-31 08:36:49,240 - app.db.mongodb - INFO - MongoDB connection closed
2025-03-31 08:36:49,974 - app.core.logging - INFO - Logging setup completed
2025-03-31 08:36:49,987 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-03-31 08:36:49,993 - app.db.mongodb - INFO - Connected to MongoDB
2025-03-31 08:38:48,417 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-03-31 08:38:48,418 - app.db.mongodb - INFO - MongoDB connection closed
2025-03-31 08:38:49,346 - app.core.logging - INFO - Logging setup completed
2025-03-31 08:38:49,365 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-03-31 08:38:49,371 - app.db.mongodb - INFO - Connected to MongoDB
2025-03-31 08:38:59,908 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-03-31 08:38:59,909 - app.db.mongodb - INFO - MongoDB connection closed
2025-03-31 08:39:00,684 - app.core.logging - INFO - Logging setup completed
2025-03-31 08:39:00,700 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-03-31 08:39:00,706 - app.db.mongodb - INFO - Connected to MongoDB
2025-03-31 10:01:40,378 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-03-31 10:01:40,379 - app.db.mongodb - INFO - MongoDB connection closed
2025-03-31 10:01:41,805 - app.core.logging - INFO - Logging setup completed
2025-03-31 10:01:41,829 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-03-31 10:01:41,849 - app.db.mongodb - INFO - Connected to MongoDB
2025-03-31 10:01:43,856 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-03-31 10:01:43,856 - app.db.mongodb - INFO - MongoDB connection closed
2025-03-31 10:01:44,908 - app.core.logging - INFO - Logging setup completed
2025-03-31 10:01:44,929 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-03-31 10:01:44,937 - app.db.mongodb - INFO - Connected to MongoDB
2025-03-31 10:01:45,139 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-03-31 10:01:45,139 - app.db.mongodb - INFO - MongoDB connection closed
2025-03-31 10:04:53,035 - app.core.logging - INFO - Logging setup completed
2025-03-31 10:04:53,060 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-03-31 10:04:53,078 - app.db.mongodb - INFO - Connected to MongoDB
2025-03-31 10:06:31,433 - app.services.recommend_service - ERROR - Error inserting stock data: cannot encode object: datetime.date(2024, 3, 1), of type: <class 'datetime.date'>
2025-03-31 10:06:55,133 - app.services.recommend_service - ERROR - Error inserting stock data: cannot encode object: datetime.date(2025, 3, 1), of type: <class 'datetime.date'>
2025-03-31 10:10:18,393 - app.services.recommend_service - ERROR - Error inserting stock data: cannot encode object: datetime.date(2025, 3, 1), of type: <class 'datetime.date'>
2025-03-31 10:10:48,725 - app.services.recommend_service - ERROR - Error inserting stock data: cannot encode object: datetime.date(2025, 3, 1), of type: <class 'datetime.date'>
2025-03-31 10:11:47,908 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-03-31 10:11:47,911 - app.db.mongodb - INFO - MongoDB connection closed
2025-03-31 10:11:48,698 - app.core.logging - INFO - Logging setup completed
2025-03-31 10:11:48,712 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-03-31 10:11:48,719 - app.db.mongodb - INFO - Connected to MongoDB
2025-03-31 10:16:58,640 - app.services.recommend_service - INFO - Successfully inserted ticker data for VNM on 2025-03-01
2025-03-31 10:17:03,392 - app.services.recommend_service - INFO - Successfully inserted ticker data for VNM on 2025-03-01
2025-03-31 10:17:04,326 - app.services.recommend_service - INFO - Successfully inserted ticker data for VNM on 2025-03-01
2025-03-31 10:17:05,052 - app.services.recommend_service - INFO - Successfully inserted ticker data for VNM on 2025-03-01
2025-03-31 10:24:12,879 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-03-31 10:24:12,881 - app.db.mongodb - INFO - MongoDB connection closed
2025-03-31 10:24:13,677 - app.core.logging - INFO - Logging setup completed
2025-03-31 10:24:13,694 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-03-31 10:24:13,702 - app.db.mongodb - INFO - Connected to MongoDB
2025-03-31 10:24:58,593 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-03-31 10:24:58,594 - app.db.mongodb - INFO - MongoDB connection closed
2025-03-31 10:25:02,471 - app.core.logging - INFO - Logging setup completed
2025-03-31 10:25:02,485 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-03-31 10:25:02,491 - app.db.mongodb - INFO - Connected to MongoDB
2025-03-31 10:25:28,332 - app.services.recommend_service - INFO - Successfully inserted 1 stock data records
2025-03-31 10:27:43,471 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-03-31 10:27:43,473 - app.db.mongodb - INFO - MongoDB connection closed
2025-03-31 10:27:44,402 - app.core.logging - INFO - Logging setup completed
2025-03-31 10:27:44,418 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-03-31 10:27:44,425 - app.db.mongodb - INFO - Connected to MongoDB
2025-03-31 10:37:19,673 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-03-31 10:37:19,674 - app.db.mongodb - INFO - MongoDB connection closed
2025-03-31 10:37:20,511 - app.core.logging - INFO - Logging setup completed
2025-03-31 10:37:20,524 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-03-31 10:37:20,534 - app.db.mongodb - INFO - Connected to MongoDB
2025-03-31 10:37:27,464 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-03-31 10:37:27,465 - app.db.mongodb - INFO - MongoDB connection closed
2025-03-31 10:37:28,287 - app.core.logging - INFO - Logging setup completed
2025-03-31 10:37:28,306 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-03-31 10:37:28,313 - app.db.mongodb - INFO - Connected to MongoDB
2025-03-31 10:43:23,086 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-03-31 10:43:23,089 - app.db.mongodb - INFO - MongoDB connection closed
2025-03-31 10:43:23,861 - app.core.logging - INFO - Logging setup completed
2025-03-31 10:43:23,875 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-03-31 10:43:23,881 - app.db.mongodb - INFO - Connected to MongoDB
2025-03-31 10:43:25,790 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-03-31 10:43:25,790 - app.db.mongodb - INFO - MongoDB connection closed
2025-03-31 10:43:26,553 - app.core.logging - INFO - Logging setup completed
2025-03-31 10:43:26,567 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-03-31 10:43:26,572 - app.db.mongodb - INFO - Connected to MongoDB
2025-03-31 10:44:36,555 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-03-31 10:44:36,556 - app.db.mongodb - INFO - MongoDB connection closed
2025-03-31 10:44:37,260 - app.core.logging - INFO - Logging setup completed
2025-03-31 10:44:37,274 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-03-31 10:44:37,280 - app.db.mongodb - INFO - Connected to MongoDB
2025-03-31 10:46:51,753 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-03-31 10:46:51,755 - app.db.mongodb - INFO - MongoDB connection closed
2025-03-31 10:46:52,559 - app.core.logging - INFO - Logging setup completed
2025-03-31 10:46:52,572 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-03-31 10:46:52,578 - app.db.mongodb - INFO - Connected to MongoDB
2025-03-31 10:47:56,753 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-03-31 10:47:56,755 - app.db.mongodb - INFO - MongoDB connection closed
2025-03-31 10:47:57,570 - app.core.logging - INFO - Logging setup completed
2025-03-31 10:47:57,587 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-03-31 10:47:57,594 - app.db.mongodb - INFO - Connected to MongoDB
2025-03-31 10:51:58,706 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-03-31 10:51:58,710 - app.db.mongodb - INFO - MongoDB connection closed
2025-03-31 10:52:02,061 - app.core.logging - INFO - Logging setup completed
2025-03-31 10:52:02,079 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-03-31 10:52:02,085 - app.db.mongodb - INFO - Connected to MongoDB
2025-03-31 11:20:40,447 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-03-31 11:20:40,449 - app.db.mongodb - INFO - MongoDB connection closed
2025-03-31 11:20:41,278 - app.core.logging - INFO - Logging setup completed
2025-03-31 11:20:41,293 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-03-31 11:20:41,303 - app.db.mongodb - INFO - Connected to MongoDB
2025-03-31 11:24:46,351 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-03-31 11:24:46,353 - app.db.mongodb - INFO - MongoDB connection closed
2025-03-31 11:24:47,218 - app.core.logging - INFO - Logging setup completed
2025-03-31 11:24:47,237 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-03-31 11:24:47,244 - app.db.mongodb - INFO - Connected to MongoDB
2025-03-31 11:26:34,106 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-03-31 11:26:34,107 - app.db.mongodb - INFO - MongoDB connection closed
2025-03-31 11:26:34,913 - app.core.logging - INFO - Logging setup completed
2025-03-31 11:26:34,928 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-03-31 11:26:34,935 - app.db.mongodb - INFO - Connected to MongoDB
2025-03-31 11:26:38,349 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-03-31 11:26:38,350 - app.db.mongodb - INFO - MongoDB connection closed
2025-03-31 11:26:39,121 - app.core.logging - INFO - Logging setup completed
2025-03-31 11:26:39,137 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-03-31 11:26:39,142 - app.db.mongodb - INFO - Connected to MongoDB
2025-03-31 11:28:24,282 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-03-31 11:28:24,284 - app.db.mongodb - INFO - MongoDB connection closed
2025-03-31 11:28:25,027 - app.core.logging - INFO - Logging setup completed
2025-03-31 11:28:25,040 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-03-31 11:28:25,045 - app.db.mongodb - INFO - Connected to MongoDB
2025-03-31 11:28:25,750 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-03-31 11:28:25,751 - app.db.mongodb - INFO - MongoDB connection closed
2025-03-31 11:28:26,485 - app.core.logging - INFO - Logging setup completed
2025-03-31 11:28:26,500 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-03-31 11:28:26,506 - app.db.mongodb - INFO - Connected to MongoDB
2025-03-31 11:32:49,155 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-03-31 11:32:49,157 - app.db.mongodb - INFO - MongoDB connection closed
2025-03-31 11:32:49,923 - app.core.logging - INFO - Logging setup completed
2025-03-31 11:32:49,939 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-03-31 11:32:49,944 - app.db.mongodb - INFO - Connected to MongoDB
2025-03-31 11:35:43,283 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-03-31 11:35:43,284 - app.db.mongodb - INFO - MongoDB connection closed
2025-03-31 11:35:44,031 - app.core.logging - INFO - Logging setup completed
2025-03-31 11:35:44,045 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-03-31 11:35:44,051 - app.db.mongodb - INFO - Connected to MongoDB
2025-03-31 11:45:21,347 - app.services.pattern_service - INFO - Pattern TrendingGrowth updated successfully
2025-03-31 11:59:29,539 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-03-31 11:59:29,542 - app.db.mongodb - INFO - MongoDB connection closed
2025-04-01 03:36:42,597 - app.core.logging - INFO - Logging setup completed
2025-04-01 03:36:42,611 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-04-01 03:36:42,620 - app.db.mongodb - INFO - Connected to MongoDB
2025-04-01 03:56:59,861 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-04-01 03:56:59,863 - app.db.mongodb - INFO - MongoDB connection closed
2025-04-01 03:57:22,567 - app.core.logging - INFO - Logging setup completed
2025-04-01 03:57:22,579 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-04-01 03:57:22,588 - app.db.mongodb - INFO - Connected to MongoDB
2025-04-01 04:01:07,884 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-04-01 04:01:07,884 - app.db.mongodb - INFO - MongoDB connection closed
2025-04-01 04:01:16,507 - app.core.logging - INFO - Logging setup completed
2025-04-01 04:01:16,523 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-04-01 04:01:16,528 - app.db.mongodb - INFO - Connected to MongoDB
2025-04-01 04:17:25,249 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-04-01 04:17:25,259 - app.db.mongodb - INFO - MongoDB connection closed
2025-04-01 04:17:26,081 - app.core.logging - INFO - Logging setup completed
2025-04-01 04:17:26,096 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-04-01 04:17:26,105 - app.db.mongodb - INFO - Connected to MongoDB
2025-04-01 04:21:55,651 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-04-01 04:21:55,654 - app.db.mongodb - INFO - MongoDB connection closed
2025-04-01 04:21:56,439 - app.core.logging - INFO - Logging setup completed
2025-04-01 04:21:56,452 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-04-01 04:21:56,459 - app.db.mongodb - INFO - Connected to MongoDB
2025-04-01 04:34:53,887 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-04-01 04:34:53,888 - app.db.mongodb - INFO - MongoDB connection closed
2025-04-01 04:34:54,688 - app.core.logging - INFO - Logging setup completed
2025-04-01 04:34:54,704 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-04-01 04:34:54,713 - app.db.mongodb - INFO - Connected to MongoDB
2025-04-01 04:35:31,972 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-04-01 04:35:31,973 - app.db.mongodb - INFO - MongoDB connection closed
2025-04-01 04:35:32,791 - app.core.logging - INFO - Logging setup completed
2025-04-01 04:35:32,806 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-04-01 04:35:32,813 - app.db.mongodb - INFO - Connected to MongoDB
2025-04-01 04:36:21,842 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-04-01 04:36:21,843 - app.db.mongodb - INFO - MongoDB connection closed
2025-04-01 04:36:22,624 - app.core.logging - INFO - Logging setup completed
2025-04-01 04:36:22,642 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-04-01 04:36:22,649 - app.db.mongodb - INFO - Connected to MongoDB
2025-04-01 04:36:28,875 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-04-01 04:36:28,876 - app.db.mongodb - INFO - MongoDB connection closed
2025-04-01 04:36:29,660 - app.core.logging - INFO - Logging setup completed
2025-04-01 04:36:29,673 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-04-01 04:36:29,679 - app.db.mongodb - INFO - Connected to MongoDB
2025-04-01 04:38:40,254 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-04-01 04:38:40,255 - app.db.mongodb - INFO - MongoDB connection closed
2025-04-01 04:38:41,006 - app.core.logging - INFO - Logging setup completed
2025-04-01 04:38:41,019 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-04-01 04:38:41,026 - app.db.mongodb - INFO - Connected to MongoDB
2025-04-01 04:38:42,734 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-04-01 04:38:42,734 - app.db.mongodb - INFO - MongoDB connection closed
2025-04-01 04:38:43,459 - app.core.logging - INFO - Logging setup completed
2025-04-01 04:38:43,473 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-04-01 04:38:43,479 - app.db.mongodb - INFO - Connected to MongoDB
2025-04-01 04:38:44,384 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-04-01 04:38:44,384 - app.db.mongodb - INFO - MongoDB connection closed
2025-04-01 04:38:45,213 - app.core.logging - INFO - Logging setup completed
2025-04-01 04:38:45,230 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-04-01 04:38:45,237 - app.db.mongodb - INFO - Connected to MongoDB
2025-04-01 04:39:44,205 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-04-01 04:39:44,206 - app.db.mongodb - INFO - MongoDB connection closed
2025-04-01 04:39:45,091 - app.core.logging - INFO - Logging setup completed
2025-04-01 04:39:45,107 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-04-01 04:39:45,113 - app.db.mongodb - INFO - Connected to MongoDB
2025-04-01 04:39:50,735 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-04-01 04:39:50,736 - app.db.mongodb - INFO - MongoDB connection closed
2025-04-01 04:39:51,568 - app.core.logging - INFO - Logging setup completed
2025-04-01 04:39:51,591 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-04-01 04:39:51,600 - app.db.mongodb - INFO - Connected to MongoDB
2025-04-01 04:41:20,395 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-04-01 04:41:20,396 - app.db.mongodb - INFO - MongoDB connection closed
2025-04-01 04:41:21,186 - app.core.logging - INFO - Logging setup completed
2025-04-01 04:41:21,201 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-04-01 04:41:21,207 - app.db.mongodb - INFO - Connected to MongoDB
2025-04-01 04:46:29,147 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-04-01 04:46:29,150 - app.db.mongodb - INFO - MongoDB connection closed
2025-04-01 04:46:29,881 - app.core.logging - INFO - Logging setup completed
2025-04-01 04:46:29,896 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-04-01 04:46:29,902 - app.db.mongodb - INFO - Connected to MongoDB
2025-04-01 04:46:50,187 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-04-01 04:46:50,188 - app.db.mongodb - INFO - MongoDB connection closed
2025-04-01 04:46:50,907 - app.core.logging - INFO - Logging setup completed
2025-04-01 04:46:50,922 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-04-01 04:46:50,928 - app.db.mongodb - INFO - Connected to MongoDB
2025-04-01 04:48:54,081 - app.services.recommend_service - INFO - Successfully inserted 3 stock data records
2025-04-01 05:21:13,410 - app.services.recommend_service - INFO - Successfully inserted 3 stock data records
2025-04-01 05:22:36,174 - app.services.recommend_service - INFO - Successfully inserted 3 stock data records
2025-04-01 05:23:09,674 - app.services.recommend_service - INFO - Successfully inserted 3 stock data records
2025-04-01 05:24:27,605 - app.services.recommend_service - INFO - Successfully inserted 3 stock data records
2025-04-01 05:25:09,235 - app.services.recommend_service - INFO - Successfully inserted 3 stock data records
2025-04-01 06:04:57,276 - app.services.recommend_service - ERROR - Error inserting multiple stock data: documents must be a non-empty list
2025-04-01 06:04:57,283 - app.services.recommend_service - ERROR - Error inserting multiple stock data: documents must be a non-empty list
2025-04-01 06:04:57,288 - app.services.recommend_service - ERROR - Error inserting multiple stock data: documents must be a non-empty list
2025-04-01 06:28:17,331 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-04-01 06:28:17,337 - app.db.mongodb - INFO - MongoDB connection closed
2025-04-01 06:28:20,774 - app.core.logging - INFO - Logging setup completed
2025-04-01 06:28:20,791 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-04-01 06:28:20,800 - app.db.mongodb - INFO - Connected to MongoDB
2025-04-01 06:53:14,072 - app.services.recommend_service - INFO - Successfully inserted 66 stock data records
2025-04-01 07:11:29,650 - app.services.recommend_service - ERROR - Error inserting multiple stock data: documents must be a non-empty list
2025-04-01 07:11:29,654 - app.services.recommend_service - ERROR - Error inserting multiple stock data: documents must be a non-empty list
2025-04-01 07:11:29,658 - app.services.recommend_service - ERROR - Error inserting multiple stock data: documents must be a non-empty list
2025-04-01 07:13:02,850 - app.services.recommend_service - ERROR - Error inserting multiple stock data: documents must be a non-empty list
2025-04-01 07:13:02,854 - app.services.recommend_service - ERROR - Error inserting multiple stock data: documents must be a non-empty list
2025-04-01 07:13:02,859 - app.services.recommend_service - ERROR - Error inserting multiple stock data: documents must be a non-empty list
2025-04-01 07:21:41,878 - app.services.recommend_service - ERROR - Error inserting multiple stock data: documents must be a non-empty list
2025-04-01 07:21:41,882 - app.services.recommend_service - ERROR - Error inserting multiple stock data: documents must be a non-empty list
2025-04-01 07:21:41,886 - app.services.recommend_service - ERROR - Error inserting multiple stock data: documents must be a non-empty list
2025-04-01 07:23:58,838 - app.services.recommend_service - ERROR - Error inserting multiple stock data: documents must be a non-empty list
2025-04-01 07:23:58,842 - app.services.recommend_service - ERROR - Error inserting multiple stock data: documents must be a non-empty list
2025-04-01 07:23:58,846 - app.services.recommend_service - ERROR - Error inserting multiple stock data: documents must be a non-empty list
2025-04-01 07:24:56,076 - app.services.recommend_service - INFO - Successfully inserted 66 stock data records
2025-04-01 08:13:10,467 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-04-02 03:24:12,266 - app.core.logging - INFO - Logging setup completed
2025-04-02 03:24:12,281 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-04-02 03:24:12,290 - app.db.mongodb - INFO - Connected to MongoDB
2025-04-02 03:25:22,775 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-04-02 03:25:22,775 - app.db.mongodb - INFO - MongoDB connection closed
2025-04-02 03:25:23,548 - app.core.logging - INFO - Logging setup completed
2025-04-02 03:25:23,562 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-04-02 03:25:23,568 - app.db.mongodb - INFO - Connected to MongoDB
2025-04-02 03:26:38,874 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-04-02 03:26:38,875 - app.db.mongodb - INFO - MongoDB connection closed
2025-04-02 03:26:39,678 - app.core.logging - INFO - Logging setup completed
2025-04-02 03:26:39,694 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-04-02 03:26:39,702 - app.db.mongodb - INFO - Connected to MongoDB
2025-04-02 03:30:11,686 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-04-02 03:30:11,686 - app.db.mongodb - INFO - MongoDB connection closed
2025-04-02 03:30:12,451 - app.core.logging - INFO - Logging setup completed
2025-04-02 03:30:12,466 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-04-02 03:30:12,471 - app.db.mongodb - INFO - Connected to MongoDB
2025-04-02 07:07:53,461 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-04-02 07:07:53,464 - app.db.mongodb - INFO - MongoDB connection closed
2025-04-03 10:28:39,737 - app.core.logging - INFO - Logging setup completed
2025-04-03 10:28:39,757 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-04-03 10:28:39,767 - app.db.mongodb - INFO - Connected to MongoDB
2025-04-03 10:28:56,432 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-04-03 10:28:56,432 - app.db.mongodb - INFO - MongoDB connection closed
2025-04-11 03:52:11,118 - app.core.logging - INFO - Logging setup completed
2025-04-11 03:52:11,131 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-04-11 03:52:11,140 - app.db.mongodb - INFO - Connected to MongoDB
2025-04-11 03:53:41,097 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-04-11 03:53:41,098 - app.db.mongodb - INFO - MongoDB connection closed
2025-04-11 03:53:46,251 - app.core.logging - INFO - Logging setup completed
2025-04-11 03:53:46,264 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-04-11 03:53:46,272 - app.db.mongodb - INFO - Connected to MongoDB
2025-04-11 03:55:25,431 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-04-11 03:55:25,431 - app.db.mongodb - INFO - MongoDB connection closed
2025-04-11 03:55:26,340 - app.core.logging - INFO - Logging setup completed
2025-04-11 03:55:26,354 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-04-11 03:55:26,361 - app.db.mongodb - INFO - Connected to MongoDB
2025-04-11 03:56:13,301 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-04-11 03:56:13,302 - app.db.mongodb - INFO - MongoDB connection closed
2025-04-11 03:56:14,212 - app.core.logging - INFO - Logging setup completed
2025-04-11 03:56:14,227 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-04-11 03:56:14,237 - app.db.mongodb - INFO - Connected to MongoDB
2025-04-11 03:57:31,927 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-04-11 03:57:31,928 - app.db.mongodb - INFO - MongoDB connection closed
2025-04-11 03:57:32,800 - app.core.logging - INFO - Logging setup completed
2025-04-11 03:57:32,820 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-04-11 03:57:32,825 - app.db.mongodb - INFO - Connected to MongoDB
2025-04-11 04:14:08,531 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-04-11 04:14:08,540 - app.db.mongodb - INFO - MongoDB connection closed
2025-04-11 04:14:09,321 - app.core.logging - INFO - Logging setup completed
2025-04-11 04:14:09,338 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-04-11 04:14:09,345 - app.db.mongodb - INFO - Connected to MongoDB
2025-04-11 04:19:54,208 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-04-11 04:19:54,209 - app.db.mongodb - INFO - MongoDB connection closed
2025-04-11 04:19:55,050 - app.core.logging - INFO - Logging setup completed
2025-04-11 04:19:55,065 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-04-11 04:19:55,070 - app.db.mongodb - INFO - Connected to MongoDB
2025-04-11 04:20:33,403 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-04-11 04:20:33,404 - app.db.mongodb - INFO - MongoDB connection closed
2025-04-11 04:20:34,163 - app.core.logging - INFO - Logging setup completed
2025-04-11 04:20:34,178 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-04-11 04:20:34,184 - app.db.mongodb - INFO - Connected to MongoDB
2025-04-11 04:23:14,127 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-04-11 04:23:14,128 - app.db.mongodb - INFO - MongoDB connection closed
2025-04-11 04:23:14,984 - app.core.logging - INFO - Logging setup completed
2025-04-11 04:23:14,998 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-04-11 04:23:15,006 - app.db.mongodb - INFO - Connected to MongoDB
2025-04-11 04:37:12,263 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-04-11 04:37:12,265 - app.db.mongodb - INFO - MongoDB connection closed
2025-04-11 04:37:13,460 - app.core.logging - INFO - Logging setup completed
2025-04-11 04:37:13,477 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-04-11 04:37:13,488 - app.db.mongodb - INFO - Connected to MongoDB
2025-04-11 05:23:47,852 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-04-11 05:23:47,853 - app.db.mongodb - INFO - MongoDB connection closed
2025-04-11 06:05:26,290 - app.core.logging - INFO - Logging setup completed
2025-04-11 06:05:26,310 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-04-11 06:05:26,320 - app.db.mongodb - INFO - Connected to MongoDB
2025-04-11 06:09:30,376 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-04-11 06:09:30,377 - app.db.mongodb - INFO - MongoDB connection closed
2025-04-11 06:09:34,190 - app.core.logging - INFO - Logging setup completed
2025-04-11 06:09:34,218 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-04-11 06:09:34,227 - app.db.mongodb - INFO - Connected to MongoDB
2025-04-11 06:10:31,007 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-04-11 06:10:31,008 - app.db.mongodb - INFO - MongoDB connection closed
2025-04-11 06:10:31,803 - app.core.logging - INFO - Logging setup completed
2025-04-11 06:10:31,819 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-04-11 06:10:31,826 - app.db.mongodb - INFO - Connected to MongoDB
2025-04-11 06:11:17,177 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-04-11 06:11:17,178 - app.db.mongodb - INFO - MongoDB connection closed
2025-04-11 06:11:18,046 - app.core.logging - INFO - Logging setup completed
2025-04-11 06:11:18,069 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-04-11 06:11:18,075 - app.db.mongodb - INFO - Connected to MongoDB
2025-04-11 06:12:02,557 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-04-11 06:12:02,558 - app.db.mongodb - INFO - MongoDB connection closed
2025-04-11 06:12:03,368 - app.core.logging - INFO - Logging setup completed
2025-04-11 06:12:03,391 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-04-11 06:12:03,398 - app.db.mongodb - INFO - Connected to MongoDB
2025-04-11 06:13:59,811 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-04-11 06:13:59,812 - app.db.mongodb - INFO - MongoDB connection closed
2025-04-11 06:14:00,650 - app.core.logging - INFO - Logging setup completed
2025-04-11 06:14:00,673 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-04-11 06:14:00,682 - app.db.mongodb - INFO - Connected to MongoDB
2025-04-11 06:14:02,989 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-04-11 06:14:02,989 - app.db.mongodb - INFO - MongoDB connection closed
2025-04-11 06:14:03,809 - app.core.logging - INFO - Logging setup completed
2025-04-11 06:14:03,828 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-04-11 06:14:03,834 - app.db.mongodb - INFO - Connected to MongoDB
2025-04-11 06:15:30,660 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-04-11 06:15:30,661 - app.db.mongodb - INFO - MongoDB connection closed
2025-04-11 06:15:51,912 - app.core.logging - INFO - Logging setup completed
2025-04-11 06:15:51,929 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-04-11 06:15:51,937 - app.db.mongodb - INFO - Connected to MongoDB
2025-04-11 06:16:45,930 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-04-11 06:16:45,930 - app.db.mongodb - INFO - MongoDB connection closed
2025-04-11 06:16:46,817 - app.core.logging - INFO - Logging setup completed
2025-04-11 06:16:46,845 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-04-11 06:16:46,851 - app.db.mongodb - INFO - Connected to MongoDB
2025-04-11 06:17:34,542 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-04-11 06:17:34,543 - app.db.mongodb - INFO - MongoDB connection closed
2025-04-11 06:17:35,455 - app.core.logging - INFO - Logging setup completed
2025-04-11 06:17:35,474 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-04-11 06:17:35,480 - app.db.mongodb - INFO - Connected to MongoDB
2025-04-11 06:17:39,294 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-04-11 06:17:39,294 - app.db.mongodb - INFO - MongoDB connection closed
2025-04-11 06:17:40,105 - app.core.logging - INFO - Logging setup completed
2025-04-11 06:17:40,120 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-04-11 06:17:40,126 - app.db.mongodb - INFO - Connected to MongoDB
2025-04-11 06:18:58,952 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-04-11 06:18:58,953 - app.db.mongodb - INFO - MongoDB connection closed
2025-04-11 06:18:59,765 - app.core.logging - INFO - Logging setup completed
2025-04-11 06:18:59,785 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-04-11 06:18:59,791 - app.db.mongodb - INFO - Connected to MongoDB
2025-04-11 06:19:07,413 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-04-11 06:19:07,413 - app.db.mongodb - INFO - MongoDB connection closed
2025-04-11 06:19:08,312 - app.core.logging - INFO - Logging setup completed
2025-04-11 06:19:08,328 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-04-11 06:19:08,336 - app.db.mongodb - INFO - Connected to MongoDB
2025-04-11 06:19:31,921 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-04-11 06:19:31,921 - app.db.mongodb - INFO - MongoDB connection closed
2025-04-11 06:23:20,430 - app.core.logging - INFO - Logging setup completed
2025-04-11 06:23:20,449 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-04-11 06:23:20,455 - app.db.mongodb - INFO - Connected to MongoDB
2025-04-11 06:36:14,567 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-04-11 06:36:14,570 - app.db.mongodb - INFO - MongoDB connection closed
2025-04-11 06:36:15,569 - app.core.logging - INFO - Logging setup completed
2025-04-11 06:36:15,589 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-04-11 06:36:15,599 - app.db.mongodb - INFO - Connected to MongoDB
2025-04-11 06:38:01,715 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-04-11 06:38:01,715 - app.db.mongodb - INFO - MongoDB connection closed
2025-04-11 06:38:02,582 - app.core.logging - INFO - Logging setup completed
2025-04-11 06:38:02,600 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-04-11 06:38:02,606 - app.db.mongodb - INFO - Connected to MongoDB
2025-04-11 07:21:15,330 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-04-11 07:21:15,330 - app.db.mongodb - INFO - MongoDB connection closed
2025-04-15 07:14:42,765 - app.core.logging - INFO - Logging setup completed
2025-04-15 07:14:42,781 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-04-15 07:14:42,791 - app.db.mongodb - INFO - Connected to MongoDB
2025-04-15 07:18:21,695 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-04-15 07:18:21,695 - app.db.mongodb - INFO - MongoDB connection closed
2025-04-15 07:18:22,482 - app.core.logging - INFO - Logging setup completed
2025-04-15 07:18:22,499 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-04-15 07:18:22,505 - app.db.mongodb - INFO - Connected to MongoDB
2025-04-15 07:19:27,125 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-04-15 07:19:27,126 - app.db.mongodb - INFO - MongoDB connection closed
2025-04-15 07:19:27,959 - app.core.logging - INFO - Logging setup completed
2025-04-15 07:19:27,976 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-04-15 07:19:27,982 - app.db.mongodb - INFO - Connected to MongoDB
2025-04-15 07:19:49,552 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-04-15 07:19:49,552 - app.db.mongodb - INFO - MongoDB connection closed
2025-04-15 07:19:50,678 - app.core.logging - INFO - Logging setup completed
2025-04-15 07:19:50,695 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-04-15 07:19:50,703 - app.db.mongodb - INFO - Connected to MongoDB
2025-04-15 07:22:03,298 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-04-15 07:22:03,299 - app.db.mongodb - INFO - MongoDB connection closed
2025-04-15 07:22:04,114 - app.core.logging - INFO - Logging setup completed
2025-04-15 07:22:04,135 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-04-15 07:22:04,142 - app.db.mongodb - INFO - Connected to MongoDB
2025-04-15 07:22:45,295 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-04-15 07:22:45,295 - app.db.mongodb - INFO - MongoDB connection closed
2025-04-15 07:22:46,206 - app.core.logging - INFO - Logging setup completed
2025-04-15 07:22:46,224 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-04-15 07:22:46,231 - app.db.mongodb - INFO - Connected to MongoDB
2025-04-15 07:25:34,780 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-04-15 07:25:34,780 - app.db.mongodb - INFO - MongoDB connection closed
2025-04-15 07:25:35,788 - app.core.logging - INFO - Logging setup completed
2025-04-15 07:25:35,807 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-04-15 07:25:35,813 - app.db.mongodb - INFO - Connected to MongoDB
2025-04-15 07:29:01,352 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-04-15 07:29:01,353 - app.db.mongodb - INFO - MongoDB connection closed
2025-04-15 07:29:02,333 - app.core.logging - INFO - Logging setup completed
2025-04-15 07:29:02,351 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-04-15 07:29:02,360 - app.db.mongodb - INFO - Connected to MongoDB
2025-04-15 07:38:58,021 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-04-15 07:38:58,021 - app.db.mongodb - INFO - MongoDB connection closed
2025-04-15 07:38:58,868 - app.core.logging - INFO - Logging setup completed
2025-04-15 07:38:58,885 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-04-15 07:38:58,891 - app.db.mongodb - INFO - Connected to MongoDB
2025-04-15 07:39:08,725 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-04-15 07:39:08,725 - app.db.mongodb - INFO - MongoDB connection closed
2025-04-15 07:39:09,616 - app.core.logging - INFO - Logging setup completed
2025-04-15 07:39:09,631 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-04-15 07:39:09,636 - app.db.mongodb - INFO - Connected to MongoDB
2025-04-15 07:39:22,283 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-04-15 07:39:22,283 - app.db.mongodb - INFO - MongoDB connection closed
2025-04-15 07:39:23,068 - app.core.logging - INFO - Logging setup completed
2025-04-15 07:39:23,084 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-04-15 07:39:23,089 - app.db.mongodb - INFO - Connected to MongoDB
2025-04-15 07:40:47,211 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-04-15 07:40:47,212 - app.db.mongodb - INFO - MongoDB connection closed
2025-04-15 07:40:48,125 - app.core.logging - INFO - Logging setup completed
2025-04-15 07:40:48,144 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-04-15 07:40:48,152 - app.db.mongodb - INFO - Connected to MongoDB
2025-04-15 07:41:59,839 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-04-15 07:41:59,840 - app.db.mongodb - INFO - MongoDB connection closed
2025-04-15 07:42:00,843 - app.core.logging - INFO - Logging setup completed
2025-04-15 07:42:00,860 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-04-15 07:42:00,866 - app.db.mongodb - INFO - Connected to MongoDB
2025-04-15 07:48:55,411 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-04-15 07:48:55,412 - app.db.mongodb - INFO - MongoDB connection closed
2025-04-15 07:48:56,237 - app.core.logging - INFO - Logging setup completed
2025-04-15 07:48:56,252 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-04-15 07:48:56,258 - app.db.mongodb - INFO - Connected to MongoDB
2025-04-15 08:06:11,186 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-04-15 08:06:11,186 - app.db.mongodb - INFO - MongoDB connection closed
2025-04-15 08:06:12,182 - app.core.logging - INFO - Logging setup completed
2025-04-15 08:06:12,198 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-04-15 08:06:12,203 - app.db.mongodb - INFO - Connected to MongoDB
2025-04-15 08:07:00,603 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-04-15 08:07:00,604 - app.db.mongodb - INFO - MongoDB connection closed
2025-04-15 08:07:01,583 - app.core.logging - INFO - Logging setup completed
2025-04-15 08:07:01,598 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-04-15 08:07:01,604 - app.db.mongodb - INFO - Connected to MongoDB
2025-04-15 08:09:19,262 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-04-15 08:09:19,262 - app.db.mongodb - INFO - MongoDB connection closed
2025-04-15 08:09:20,040 - app.core.logging - INFO - Logging setup completed
2025-04-15 08:09:20,057 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-04-15 08:09:20,063 - app.db.mongodb - INFO - Connected to MongoDB
2025-04-15 08:12:49,027 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-04-15 08:12:49,028 - app.db.mongodb - INFO - MongoDB connection closed
2025-04-15 08:12:49,753 - app.core.logging - INFO - Logging setup completed
2025-04-15 08:12:49,771 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-04-15 08:12:49,777 - app.db.mongodb - INFO - Connected to MongoDB
2025-04-15 08:18:38,622 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-04-15 08:18:38,623 - app.db.mongodb - INFO - MongoDB connection closed
2025-04-15 08:18:39,416 - app.core.logging - INFO - Logging setup completed
2025-04-15 08:18:39,432 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-04-15 08:18:39,440 - app.db.mongodb - INFO - Connected to MongoDB
2025-04-15 08:19:24,105 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-04-15 08:19:24,105 - app.db.mongodb - INFO - MongoDB connection closed
2025-04-15 08:19:24,839 - app.core.logging - INFO - Logging setup completed
2025-04-15 08:19:24,854 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-04-15 08:19:24,862 - app.db.mongodb - INFO - Connected to MongoDB
2025-04-15 08:19:32,393 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-04-15 08:19:32,394 - app.db.mongodb - INFO - MongoDB connection closed
2025-04-15 08:19:33,159 - app.core.logging - INFO - Logging setup completed
2025-04-15 08:19:33,175 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-04-15 08:19:33,181 - app.db.mongodb - INFO - Connected to MongoDB
2025-04-15 08:19:36,092 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-04-15 08:19:36,092 - app.db.mongodb - INFO - MongoDB connection closed
2025-04-15 08:19:36,809 - app.core.logging - INFO - Logging setup completed
2025-04-15 08:19:36,823 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-04-15 08:19:36,828 - app.db.mongodb - INFO - Connected to MongoDB
2025-04-15 08:19:58,616 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-04-15 08:19:58,616 - app.db.mongodb - INFO - MongoDB connection closed
2025-04-15 08:19:59,359 - app.core.logging - INFO - Logging setup completed
2025-04-15 08:19:59,374 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-04-15 08:19:59,381 - app.db.mongodb - INFO - Connected to MongoDB
2025-04-15 08:20:11,625 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-04-15 08:20:11,625 - app.db.mongodb - INFO - MongoDB connection closed
2025-04-15 08:20:12,472 - app.core.logging - INFO - Logging setup completed
2025-04-15 08:20:12,486 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-04-15 08:20:12,492 - app.db.mongodb - INFO - Connected to MongoDB
2025-04-15 08:20:21,425 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-04-15 08:20:21,425 - app.db.mongodb - INFO - MongoDB connection closed
2025-04-15 08:20:22,157 - app.core.logging - INFO - Logging setup completed
2025-04-15 08:20:22,173 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-04-15 08:20:22,178 - app.db.mongodb - INFO - Connected to MongoDB
2025-04-15 08:21:32,929 - app.services.recommend_service - INFO - Successfully inserted 406 stock data records
2025-04-15 08:24:25,303 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-04-15 08:24:25,304 - app.db.mongodb - INFO - MongoDB connection closed
2025-04-15 08:24:26,306 - app.core.logging - INFO - Logging setup completed
2025-04-15 08:24:26,352 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-04-15 08:24:26,363 - app.db.mongodb - INFO - Connected to MongoDB
2025-04-15 08:25:03,201 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-04-15 08:25:03,202 - app.db.mongodb - INFO - MongoDB connection closed
2025-04-15 08:25:04,102 - app.core.logging - INFO - Logging setup completed
2025-04-15 08:25:04,133 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-04-15 08:25:04,139 - app.db.mongodb - INFO - Connected to MongoDB
2025-04-15 08:25:25,652 - app.services.recommend_service - INFO - Successfully inserted 406 stock data records
2025-04-15 08:25:53,328 - app.services.recommend_service - INFO - Successfully inserted 237 stock data records
2025-04-15 08:26:47,900 - app.services.recommend_service - INFO - Successfully inserted 406 stock data records
2025-04-15 08:27:21,989 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-04-15 08:27:21,990 - app.db.mongodb - INFO - MongoDB connection closed
2025-04-15 08:27:23,001 - app.core.logging - INFO - Logging setup completed
2025-04-15 08:27:23,023 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-04-15 08:27:23,029 - app.db.mongodb - INFO - Connected to MongoDB
2025-04-15 08:28:12,205 - app.services.recommend_service - INFO - Successfully inserted 406 stock data records
2025-04-15 08:28:18,165 - app.services.recommend_service - INFO - Successfully inserted 237 stock data records
2025-04-15 08:30:18,609 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-04-15 08:30:18,611 - app.db.mongodb - INFO - MongoDB connection closed
2025-04-15 08:30:19,518 - app.core.logging - INFO - Logging setup completed
2025-04-15 08:30:19,534 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-04-15 08:30:19,542 - app.db.mongodb - INFO - Connected to MongoDB
2025-04-15 08:32:00,467 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-04-15 08:32:00,467 - app.db.mongodb - INFO - MongoDB connection closed
2025-04-15 08:32:01,222 - app.core.logging - INFO - Logging setup completed
2025-04-15 08:32:01,237 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-04-15 08:32:01,245 - app.db.mongodb - INFO - Connected to MongoDB
2025-04-15 08:38:09,751 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-04-15 08:38:09,752 - app.db.mongodb - INFO - MongoDB connection closed
2025-04-15 08:38:10,753 - app.core.logging - INFO - Logging setup completed
2025-04-15 08:38:10,774 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-04-15 08:38:10,781 - app.db.mongodb - INFO - Connected to MongoDB
2025-04-15 08:39:00,157 - app.services.recommend_service - INFO - Successfully inserted 406 stock data records
2025-04-15 10:53:32,511 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-04-15 10:53:32,513 - app.db.mongodb - INFO - MongoDB connection closed
2025-04-23 02:00:47,449 - app.core.logging - INFO - Logging setup completed
2025-04-23 02:00:47,479 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-04-23 02:00:47,493 - app.db.mongodb - INFO - Connected to MongoDB
2025-04-23 03:07:06,640 - app.core.logging - INFO - Logging setup completed
2025-04-23 03:07:06,655 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-04-23 03:07:06,664 - app.db.mongodb - INFO - Connected to MongoDB
2025-05-07 08:48:49,852 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-05-07 08:48:49,878 - app.db.mongodb - INFO - MongoDB connection closed
2025-05-07 08:48:51,012 - app.core.logging - INFO - Logging setup completed
2025-05-07 08:48:51,031 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-05-07 08:48:51,043 - app.db.mongodb - INFO - Connected to MongoDB
2025-05-07 08:49:07,498 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-05-07 08:49:07,500 - app.db.mongodb - INFO - MongoDB connection closed
2025-05-07 08:49:08,519 - app.core.logging - INFO - Logging setup completed
2025-05-07 08:49:08,535 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-05-07 08:49:08,541 - app.db.mongodb - INFO - Connected to MongoDB
2025-05-14 05:00:55,742 - app.core.logging - INFO - Logging setup completed
2025-05-14 05:00:55,766 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-05-14 05:00:55,780 - app.db.mongodb - INFO - Connected to MongoDB
2025-05-22 16:06:03,496 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-05-22 16:06:03,537 - app.db.mongodb - INFO - MongoDB connection closed
2025-05-23 02:43:23,648 - app.core.logging - INFO - Logging setup completed
2025-05-23 02:43:23,662 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-05-23 02:43:23,674 - app.db.mongodb - INFO - Connected to MongoDB
2025-06-02 04:52:10,732 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-06-02 04:52:10,754 - app.db.mongodb - INFO - MongoDB connection closed
2025-06-02 04:52:11,935 - app.core.logging - INFO - Logging setup completed
2025-06-02 04:52:11,955 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-06-02 04:52:11,966 - app.db.mongodb - INFO - Connected to MongoDB
2025-06-02 04:52:13,036 - app.core.logging - INFO - Logging setup completed
2025-06-02 04:52:13,059 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-06-02 04:52:13,067 - app.db.mongodb - INFO - Connected to MongoDB
2025-06-03 02:19:26,631 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-06-03 02:19:26,723 - app.db.mongodb - INFO - MongoDB connection closed
2025-06-03 02:20:02,651 - app.core.logging - INFO - Logging setup completed
2025-06-03 02:20:02,666 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-06-03 02:20:02,675 - app.db.mongodb - INFO - Connected to MongoDB
2025-06-03 05:16:59,404 - app.core.logging - INFO - Logging setup completed
2025-06-03 05:16:59,419 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-06-03 05:16:59,428 - app.db.mongodb - INFO - Connected to MongoDB
2025-06-04 00:51:40,612 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-06-04 00:51:40,721 - app.db.mongodb - INFO - MongoDB connection closed
2025-06-04 00:52:22,758 - app.core.logging - INFO - Logging setup completed
2025-06-04 00:52:22,776 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-06-04 00:52:22,785 - app.db.mongodb - INFO - Connected to MongoDB
2025-06-07 15:15:47,425 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-06-07 15:15:47,452 - app.db.mongodb - INFO - MongoDB connection closed
2025-06-07 15:15:55,442 - app.core.logging - INFO - Logging setup completed
2025-06-07 15:15:55,478 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-06-07 15:15:55,508 - app.db.mongodb - INFO - Connected to MongoDB
2025-06-10 02:23:28,791 - app.core.logging - INFO - Logging setup completed
2025-06-10 02:23:28,806 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-06-10 02:23:28,817 - app.db.mongodb - INFO - Connected to MongoDB
2025-06-10 02:32:38,404 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-06-10 02:32:38,406 - app.db.mongodb - INFO - MongoDB connection closed
2025-06-10 02:33:08,959 - app.core.logging - INFO - Logging setup completed
2025-06-10 02:33:08,974 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-06-10 02:33:08,984 - app.db.mongodb - INFO - Connected to MongoDB
2025-06-10 02:37:27,451 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-06-10 02:37:27,451 - app.db.mongodb - INFO - MongoDB connection closed
2025-06-10 02:37:56,756 - app.core.logging - INFO - Logging setup completed
2025-06-10 02:37:56,771 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-06-10 02:37:56,780 - app.db.mongodb - INFO - Connected to MongoDB
2025-06-10 02:59:48,027 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-06-10 02:59:48,031 - app.db.mongodb - INFO - MongoDB connection closed
2025-06-10 03:00:16,376 - app.core.logging - INFO - Logging setup completed
2025-06-10 03:00:16,391 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-06-10 03:00:16,399 - app.db.mongodb - INFO - Connected to MongoDB
2025-06-16 17:39:23,765 - app.core.logging - INFO - Logging setup completed
2025-06-16 17:39:23,778 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-06-16 17:39:23,787 - app.db.mongodb - INFO - Connected to MongoDB
2025-06-19 16:04:01,378 - app.core.logging - INFO - Logging setup completed
2025-06-19 16:04:01,405 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-06-19 16:04:01,417 - app.db.mongodb - INFO - Connected to MongoDB
2025-06-20 02:18:39,525 - app.core.logging - INFO - Logging setup completed
2025-06-20 02:18:39,540 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-06-20 02:18:39,549 - app.db.mongodb - INFO - Connected to MongoDB
2025-06-24 02:32:54,693 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-06-24 02:32:54,761 - app.db.mongodb - INFO - MongoDB connection closed
2025-06-24 02:33:09,526 - app.core.logging - INFO - Logging setup completed
2025-06-24 02:33:09,541 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-06-24 02:33:09,553 - app.db.mongodb - INFO - Connected to MongoDB
2025-06-24 08:17:07,756 - app.core.logging - INFO - Logging setup completed
2025-06-24 08:17:07,771 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-06-24 08:17:07,781 - app.db.mongodb - INFO - Connected to MongoDB
2025-06-26 07:52:29,391 - app.core.logging - INFO - Logging setup completed
2025-06-26 07:52:29,406 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-06-26 07:52:29,416 - app.db.mongodb - INFO - Connected to MongoDB
2025-06-29 04:06:42,084 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-06-29 04:06:42,106 - app.db.mongodb - INFO - MongoDB connection closed
2025-06-29 04:06:54,181 - app.core.logging - INFO - Logging setup completed
2025-06-29 04:06:54,207 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-06-29 04:06:54,220 - app.db.mongodb - INFO - Connected to MongoDB
2025-06-30 05:29:02,925 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-06-30 05:29:02,950 - app.db.mongodb - INFO - MongoDB connection closed
2025-06-30 05:29:16,850 - app.core.logging - INFO - Logging setup completed
2025-06-30 05:29:16,865 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-06-30 05:29:16,878 - app.db.mongodb - INFO - Connected to MongoDB
2025-07-08 14:55:16,556 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-07-08 14:55:16,600 - app.db.mongodb - INFO - MongoDB connection closed
2025-07-08 14:55:30,966 - app.core.logging - INFO - Logging setup completed
2025-07-08 14:55:30,984 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-07-08 14:55:31,002 - app.db.mongodb - INFO - Connected to MongoDB
2025-07-23 18:28:29,652 - app.core.logging - INFO - Logging setup completed
2025-07-23 18:28:29,666 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-07-23 18:28:29,677 - app.db.mongodb - INFO - Connected to MongoDB
2025-07-24 03:19:37,397 - app.core.logging - INFO - Logging setup completed
2025-07-24 03:19:37,411 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-07-24 03:19:37,425 - app.db.mongodb - INFO - Connected to MongoDB
2025-07-29 14:34:43,160 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-07-29 14:34:43,181 - app.db.mongodb - INFO - MongoDB connection closed
2025-07-29 14:34:44,177 - app.core.logging - INFO - Logging setup completed
2025-07-29 14:34:44,190 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-07-29 14:34:44,200 - app.db.mongodb - INFO - Connected to MongoDB
2025-07-29 14:35:21,541 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-07-29 14:35:21,542 - app.db.mongodb - INFO - MongoDB connection closed
2025-07-29 14:35:22,839 - app.core.logging - INFO - Logging setup completed
2025-07-29 14:35:22,875 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-07-29 14:35:22,893 - app.db.mongodb - INFO - Connected to MongoDB
2025-08-01 03:49:54,893 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-08-01 03:49:54,918 - app.db.mongodb - INFO - MongoDB connection closed
2025-08-01 03:49:56,094 - app.core.logging - INFO - Logging setup completed
2025-08-01 03:49:56,118 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-08-01 03:49:56,128 - app.db.mongodb - INFO - Connected to MongoDB
2025-08-01 03:54:51,134 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-08-01 03:54:51,134 - app.db.mongodb - INFO - MongoDB connection closed
2025-08-01 03:54:51,987 - app.core.logging - INFO - Logging setup completed
2025-08-01 03:54:52,005 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-08-01 03:54:52,010 - app.db.mongodb - INFO - Connected to MongoDB
2025-08-01 03:56:28,811 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-08-01 03:56:28,812 - app.db.mongodb - INFO - MongoDB connection closed
2025-08-01 03:56:29,648 - app.core.logging - INFO - Logging setup completed
2025-08-01 03:56:29,669 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-08-01 03:56:29,675 - app.db.mongodb - INFO - Connected to MongoDB
2025-08-01 03:59:46,106 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-08-01 03:59:46,106 - app.db.mongodb - INFO - MongoDB connection closed
2025-08-01 03:59:46,903 - app.core.logging - INFO - Logging setup completed
2025-08-01 03:59:46,939 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-08-01 03:59:46,946 - app.db.mongodb - INFO - Connected to MongoDB
2025-08-01 04:02:18,380 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-08-01 04:02:18,380 - app.db.mongodb - INFO - MongoDB connection closed
2025-08-01 04:02:19,389 - app.core.logging - INFO - Logging setup completed
2025-08-01 04:02:19,409 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-08-01 04:02:19,416 - app.db.mongodb - INFO - Connected to MongoDB
2025-08-01 04:04:29,691 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-08-01 04:04:29,692 - app.db.mongodb - INFO - MongoDB connection closed
2025-08-01 04:04:30,923 - app.core.logging - INFO - Logging setup completed
2025-08-01 04:04:30,955 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-08-01 04:04:30,963 - app.db.mongodb - INFO - Connected to MongoDB
2025-08-01 04:05:37,746 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-08-01 04:05:37,748 - app.db.mongodb - INFO - MongoDB connection closed
2025-08-01 04:05:38,606 - app.core.logging - INFO - Logging setup completed
2025-08-01 04:05:38,622 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-08-01 04:05:38,628 - app.db.mongodb - INFO - Connected to MongoDB
2025-08-01 04:13:32,938 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-08-01 04:13:32,939 - app.db.mongodb - INFO - MongoDB connection closed
2025-08-01 04:13:33,952 - app.core.logging - INFO - Logging setup completed
2025-08-01 04:13:33,971 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-08-01 04:13:33,978 - app.db.mongodb - INFO - Connected to MongoDB
2025-08-01 04:17:30,167 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-08-01 04:17:30,168 - app.db.mongodb - INFO - MongoDB connection closed
2025-08-01 04:17:31,466 - app.core.logging - INFO - Logging setup completed
2025-08-01 04:17:31,497 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-08-01 04:17:31,505 - app.db.mongodb - INFO - Connected to MongoDB
2025-08-01 04:31:17,289 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-08-01 04:31:17,294 - app.db.mongodb - INFO - MongoDB connection closed
2025-08-01 04:31:18,319 - app.core.logging - INFO - Logging setup completed
2025-08-01 04:31:18,340 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-08-01 04:31:18,351 - app.db.mongodb - INFO - Connected to MongoDB
2025-08-01 04:35:05,751 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-08-01 04:35:05,752 - app.db.mongodb - INFO - MongoDB connection closed
2025-08-01 04:35:06,671 - app.core.logging - INFO - Logging setup completed
2025-08-01 04:35:06,688 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-08-01 04:35:06,693 - app.db.mongodb - INFO - Connected to MongoDB
2025-08-01 04:42:56,789 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-08-01 04:42:56,790 - app.db.mongodb - INFO - MongoDB connection closed
2025-08-01 04:42:57,533 - app.core.logging - INFO - Logging setup completed
2025-08-01 04:42:57,554 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-08-01 04:42:57,560 - app.db.mongodb - INFO - Connected to MongoDB
2025-08-01 04:44:45,342 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-08-01 04:44:45,343 - app.db.mongodb - INFO - MongoDB connection closed
2025-08-01 04:44:46,273 - app.core.logging - INFO - Logging setup completed
2025-08-01 04:44:46,303 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-08-01 04:44:46,309 - app.db.mongodb - INFO - Connected to MongoDB
2025-08-01 04:46:37,386 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-08-01 04:46:37,387 - app.db.mongodb - INFO - MongoDB connection closed
2025-08-01 04:46:38,196 - app.core.logging - INFO - Logging setup completed
2025-08-01 04:46:38,213 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-08-01 04:46:38,218 - app.db.mongodb - INFO - Connected to MongoDB
2025-08-01 04:51:07,543 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-08-01 04:51:07,544 - app.db.mongodb - INFO - MongoDB connection closed
2025-08-01 04:51:08,359 - app.core.logging - INFO - Logging setup completed
2025-08-01 04:51:08,376 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-08-01 04:51:08,381 - app.db.mongodb - INFO - Connected to MongoDB
2025-08-01 04:54:44,495 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-08-01 04:54:44,495 - app.db.mongodb - INFO - MongoDB connection closed
2025-08-01 04:54:45,669 - app.core.logging - INFO - Logging setup completed
2025-08-01 04:54:45,699 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-08-01 04:54:45,709 - app.db.mongodb - INFO - Connected to MongoDB
2025-08-01 04:57:22,494 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-08-01 04:57:22,494 - app.db.mongodb - INFO - MongoDB connection closed
2025-08-01 04:57:23,444 - app.core.logging - INFO - Logging setup completed
2025-08-01 04:57:23,462 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-08-01 04:57:23,467 - app.db.mongodb - INFO - Connected to MongoDB
2025-08-01 05:06:36,621 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-08-01 05:06:36,622 - app.db.mongodb - INFO - MongoDB connection closed
2025-08-01 05:06:37,852 - app.core.logging - INFO - Logging setup completed
2025-08-01 05:06:37,873 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-08-01 05:06:37,880 - app.db.mongodb - INFO - Connected to MongoDB
2025-08-01 05:47:09,614 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-08-01 05:47:09,615 - app.db.mongodb - INFO - MongoDB connection closed
2025-08-01 05:47:11,179 - app.core.logging - INFO - Logging setup completed
2025-08-01 05:47:11,205 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-08-01 05:47:11,218 - app.db.mongodb - INFO - Connected to MongoDB
2025-08-01 05:48:02,402 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-08-01 05:48:02,402 - app.db.mongodb - INFO - MongoDB connection closed
2025-08-01 05:48:03,508 - app.core.logging - INFO - Logging setup completed
2025-08-01 05:48:03,539 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-08-01 05:48:03,548 - app.db.mongodb - INFO - Connected to MongoDB
2025-08-01 06:24:54,657 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-08-01 06:24:54,662 - app.db.mongodb - INFO - MongoDB connection closed
2025-08-01 06:24:56,423 - app.core.logging - INFO - Logging setup completed
2025-08-01 06:24:56,463 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-08-01 06:24:56,479 - app.db.mongodb - INFO - Connected to MongoDB
2025-08-01 06:30:06,632 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-08-01 06:30:06,657 - app.db.mongodb - INFO - MongoDB connection closed
2025-08-01 06:30:08,286 - app.core.logging - INFO - Logging setup completed
2025-08-01 06:30:08,332 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-08-01 06:30:08,348 - app.db.mongodb - INFO - Connected to MongoDB
2025-08-01 06:31:37,547 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-08-01 06:31:37,548 - app.db.mongodb - INFO - MongoDB connection closed
2025-08-01 06:31:39,030 - app.core.logging - INFO - Logging setup completed
2025-08-01 06:31:39,066 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-08-01 06:31:39,076 - app.db.mongodb - INFO - Connected to MongoDB
2025-08-01 06:32:18,200 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-08-01 06:32:18,201 - app.db.mongodb - INFO - MongoDB connection closed
2025-08-01 06:32:19,553 - app.core.logging - INFO - Logging setup completed
2025-08-01 06:32:19,590 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-08-01 06:32:19,600 - app.db.mongodb - INFO - Connected to MongoDB
2025-08-01 06:34:52,686 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-08-01 06:34:52,686 - app.db.mongodb - INFO - MongoDB connection closed
2025-08-01 06:34:54,306 - app.core.logging - INFO - Logging setup completed
2025-08-01 06:34:54,355 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-08-01 06:34:54,367 - app.db.mongodb - INFO - Connected to MongoDB
2025-08-01 06:35:42,844 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-08-01 06:35:42,844 - app.db.mongodb - INFO - MongoDB connection closed
2025-08-01 06:35:44,450 - app.core.logging - INFO - Logging setup completed
2025-08-01 06:35:44,492 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-08-01 06:35:44,503 - app.db.mongodb - INFO - Connected to MongoDB
2025-08-01 06:44:03,552 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-08-01 06:44:03,552 - app.db.mongodb - INFO - MongoDB connection closed
2025-08-01 06:44:04,722 - app.core.logging - INFO - Logging setup completed
2025-08-01 06:44:04,752 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-08-01 06:44:04,761 - app.db.mongodb - INFO - Connected to MongoDB
2025-08-01 07:48:11,257 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-08-01 07:48:11,276 - app.db.mongodb - INFO - MongoDB connection closed
2025-08-01 07:48:13,306 - app.core.logging - INFO - Logging setup completed
2025-08-01 07:48:13,348 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-08-01 07:48:13,368 - app.db.mongodb - INFO - Connected to MongoDB
2025-08-03 05:36:48,270 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-08-03 05:36:48,298 - app.db.mongodb - INFO - MongoDB connection closed
2025-08-03 05:36:50,026 - app.core.logging - INFO - Logging setup completed
2025-08-03 05:36:50,068 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-08-03 05:36:50,090 - app.db.mongodb - INFO - Connected to MongoDB
2025-08-03 05:37:33,354 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-08-03 05:37:33,354 - app.db.mongodb - INFO - MongoDB connection closed
2025-08-03 05:37:34,577 - app.core.logging - INFO - Logging setup completed
2025-08-03 05:37:34,613 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-08-03 05:37:34,622 - app.db.mongodb - INFO - Connected to MongoDB
2025-08-03 05:42:21,257 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-08-03 05:42:21,258 - app.db.mongodb - INFO - MongoDB connection closed
2025-08-03 05:42:22,822 - app.core.logging - INFO - Logging setup completed
2025-08-03 05:42:22,859 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-08-03 05:42:22,872 - app.db.mongodb - INFO - Connected to MongoDB
2025-08-03 05:59:51,250 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-08-03 05:59:51,250 - app.db.mongodb - INFO - MongoDB connection closed
2025-08-03 05:59:52,508 - app.core.logging - INFO - Logging setup completed
2025-08-03 05:59:52,539 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-08-03 05:59:52,553 - app.db.mongodb - INFO - Connected to MongoDB
2025-08-03 06:00:51,007 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-08-03 06:00:51,008 - app.db.mongodb - INFO - MongoDB connection closed
2025-08-03 06:00:52,210 - app.core.logging - INFO - Logging setup completed
2025-08-03 06:00:52,240 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-08-03 06:00:52,251 - app.db.mongodb - INFO - Connected to MongoDB
2025-08-03 16:12:27,971 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-08-03 16:12:27,988 - app.db.mongodb - INFO - MongoDB connection closed
2025-08-03 16:12:37,699 - app.core.logging - INFO - Logging setup completed
2025-08-03 16:12:37,731 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-08-03 16:12:37,746 - app.db.mongodb - INFO - Connected to MongoDB
2025-08-03 16:16:00,665 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-08-03 16:16:00,667 - app.db.mongodb - INFO - MongoDB connection closed
2025-08-03 16:16:01,965 - app.core.logging - INFO - Logging setup completed
2025-08-03 16:16:01,998 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-08-03 16:16:02,010 - app.db.mongodb - INFO - Connected to MongoDB
2025-08-03 16:31:46,188 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-08-03 16:31:46,188 - app.db.mongodb - INFO - MongoDB connection closed
2025-08-03 16:31:47,378 - app.core.logging - INFO - Logging setup completed
2025-08-03 16:31:47,413 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-08-03 16:31:47,422 - app.db.mongodb - INFO - Connected to MongoDB
2025-08-03 16:43:07,578 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-08-03 16:43:07,582 - app.db.mongodb - INFO - MongoDB connection closed
2025-08-03 16:43:09,179 - app.core.logging - INFO - Logging setup completed
2025-08-03 16:43:09,217 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-08-03 16:43:09,227 - app.db.mongodb - INFO - Connected to MongoDB
2025-08-03 16:44:18,902 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-08-03 16:44:18,902 - app.db.mongodb - INFO - MongoDB connection closed
2025-08-03 16:44:20,165 - app.core.logging - INFO - Logging setup completed
2025-08-03 16:44:20,216 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-08-03 16:44:20,229 - app.db.mongodb - INFO - Connected to MongoDB
2025-08-03 16:54:56,459 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-08-03 16:54:56,460 - app.db.mongodb - INFO - MongoDB connection closed
2025-08-03 16:54:57,993 - app.core.logging - INFO - Logging setup completed
2025-08-03 16:54:58,030 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-08-03 16:54:58,045 - app.db.mongodb - INFO - Connected to MongoDB
2025-08-03 16:59:43,250 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-08-03 16:59:43,250 - app.db.mongodb - INFO - MongoDB connection closed
2025-08-03 16:59:44,411 - app.core.logging - INFO - Logging setup completed
2025-08-03 16:59:44,437 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-08-03 16:59:44,444 - app.db.mongodb - INFO - Connected to MongoDB
2025-08-03 17:00:30,686 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-08-03 17:00:30,687 - app.db.mongodb - INFO - MongoDB connection closed
2025-08-03 17:00:32,038 - app.core.logging - INFO - Logging setup completed
2025-08-03 17:00:32,071 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-08-03 17:00:32,080 - app.db.mongodb - INFO - Connected to MongoDB
2025-08-03 17:01:54,400 - app.services.risk_service - INFO - Upserted risk data for VNM - 2024Q1
2025-08-03 17:01:56,421 - app.services.risk_service - INFO - Upserted risk data for VNM - 2024Q1
2025-08-03 17:05:56,908 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-08-03 17:05:56,912 - app.db.mongodb - INFO - MongoDB connection closed
2025-08-03 17:06:06,635 - app.core.logging - INFO - Logging setup completed
2025-08-03 17:06:06,668 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-08-03 17:06:06,678 - app.db.mongodb - INFO - Connected to MongoDB
2025-08-03 17:13:18,431 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-08-03 17:13:18,433 - app.db.mongodb - INFO - MongoDB connection closed
2025-08-03 17:13:19,726 - app.core.logging - INFO - Logging setup completed
2025-08-03 17:13:19,752 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-08-03 17:13:19,759 - app.db.mongodb - INFO - Connected to MongoDB
2025-08-03 17:13:51,474 - app.services.risk_service - INFO - Upserted 72 risk records
2025-08-04 02:18:32,433 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-08-04 02:18:32,435 - app.db.mongodb - INFO - MongoDB connection closed
2025-08-04 02:18:33,337 - app.core.logging - INFO - Logging setup completed
2025-08-04 02:18:33,356 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-08-04 02:18:33,368 - app.db.mongodb - INFO - Connected to MongoDB
2025-08-04 02:19:01,591 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-08-04 02:19:01,591 - app.db.mongodb - INFO - MongoDB connection closed
2025-08-04 02:19:02,426 - app.core.logging - INFO - Logging setup completed
2025-08-04 02:19:02,444 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-08-04 02:19:02,449 - app.db.mongodb - INFO - Connected to MongoDB
2025-08-04 02:19:27,157 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-08-04 02:19:27,158 - app.db.mongodb - INFO - MongoDB connection closed
2025-08-04 02:19:28,116 - app.core.logging - INFO - Logging setup completed
2025-08-04 02:19:28,135 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-08-04 02:19:28,142 - app.db.mongodb - INFO - Connected to MongoDB
2025-08-04 02:19:53,858 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-08-04 02:19:53,859 - app.db.mongodb - INFO - MongoDB connection closed
2025-08-04 02:19:54,870 - app.core.logging - INFO - Logging setup completed
2025-08-04 02:19:54,890 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-08-04 02:19:54,901 - app.db.mongodb - INFO - Connected to MongoDB
2025-08-05 04:09:34,974 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-08-05 04:09:50,468 - app.core.logging - INFO - Logging setup completed
2025-08-05 04:09:50,507 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-08-05 04:09:50,537 - app.db.mongodb - INFO - Connected to MongoDB
2025-08-06 10:35:19,535 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-08-06 10:35:19,577 - app.db.mongodb - INFO - MongoDB connection closed
2025-08-06 10:35:51,367 - app.core.logging - INFO - Logging setup completed
2025-08-06 10:35:51,387 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-08-06 10:35:51,402 - app.db.mongodb - INFO - Connected to MongoDB
2025-08-16 11:17:59,175 - app.core.logging - INFO - Logging setup completed
2025-08-16 11:17:59,199 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-08-16 11:17:59,212 - app.db.mongodb - INFO - Connected to MongoDB
2025-09-01 16:23:17,136 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-09-01 16:23:17,196 - app.db.mongodb - INFO - MongoDB connection closed
2025-09-02 07:22:16,647 - app.core.logging - INFO - Logging setup completed
2025-09-02 07:22:16,668 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-09-02 07:22:16,682 - app.db.mongodb - INFO - Connected to MongoDB
2025-09-04 02:48:18,820 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-09-04 02:48:18,844 - app.db.mongodb - INFO - MongoDB connection closed
2025-09-04 02:48:25,810 - app.core.logging - INFO - Logging setup completed
2025-09-04 02:48:25,874 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-09-04 02:48:25,904 - app.db.mongodb - INFO - Connected to MongoDB
2025-09-04 03:04:27,587 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-09-04 03:04:27,602 - app.db.mongodb - INFO - MongoDB connection closed
2025-09-04 03:04:29,437 - app.core.logging - INFO - Logging setup completed
2025-09-04 03:04:29,472 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-09-04 03:04:29,493 - app.db.mongodb - INFO - Connected to MongoDB
2025-09-04 03:04:37,226 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-09-04 03:04:37,226 - app.db.mongodb - INFO - MongoDB connection closed
2025-09-04 03:04:38,575 - app.core.logging - INFO - Logging setup completed
2025-09-04 03:04:38,610 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-09-04 03:04:38,619 - app.db.mongodb - INFO - Connected to MongoDB
2025-09-04 03:04:45,754 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-09-04 03:04:45,755 - app.db.mongodb - INFO - MongoDB connection closed
2025-09-04 03:04:47,145 - app.core.logging - INFO - Logging setup completed
2025-09-04 03:04:47,183 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-09-04 03:04:47,192 - app.db.mongodb - INFO - Connected to MongoDB
2025-09-04 03:04:54,326 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-09-04 03:04:54,326 - app.db.mongodb - INFO - MongoDB connection closed
2025-09-04 03:04:55,774 - app.core.logging - INFO - Logging setup completed
2025-09-04 03:04:55,810 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-09-04 03:04:55,819 - app.db.mongodb - INFO - Connected to MongoDB
2025-09-04 03:05:05,459 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-09-04 03:05:05,460 - app.db.mongodb - INFO - MongoDB connection closed
2025-09-04 03:05:06,822 - app.core.logging - INFO - Logging setup completed
2025-09-04 03:05:06,861 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-09-04 03:05:06,870 - app.db.mongodb - INFO - Connected to MongoDB
2025-09-04 03:05:15,605 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-09-04 03:05:15,605 - app.db.mongodb - INFO - MongoDB connection closed
2025-09-04 03:05:16,996 - app.core.logging - INFO - Logging setup completed
2025-09-04 03:05:17,032 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-09-04 03:05:17,041 - app.db.mongodb - INFO - Connected to MongoDB
2025-09-04 03:05:27,187 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-09-04 03:05:27,187 - app.db.mongodb - INFO - MongoDB connection closed
2025-09-04 03:05:28,089 - app.core.logging - INFO - Logging setup completed
2025-09-04 03:05:28,111 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-09-04 03:05:28,116 - app.db.mongodb - INFO - Connected to MongoDB
2025-09-04 03:05:38,564 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-09-04 03:05:38,565 - app.db.mongodb - INFO - MongoDB connection closed
2025-09-04 03:05:39,504 - app.core.logging - INFO - Logging setup completed
2025-09-04 03:05:39,522 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-09-04 03:05:39,531 - app.db.mongodb - INFO - Connected to MongoDB
2025-09-04 03:05:48,668 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-09-04 03:05:48,668 - app.db.mongodb - INFO - MongoDB connection closed
2025-09-04 03:05:49,715 - app.core.logging - INFO - Logging setup completed
2025-09-04 03:05:49,734 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-09-04 03:05:49,741 - app.db.mongodb - INFO - Connected to MongoDB
2025-09-04 03:06:03,403 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-09-04 03:06:03,403 - app.db.mongodb - INFO - MongoDB connection closed
2025-09-04 03:06:04,585 - app.core.logging - INFO - Logging setup completed
2025-09-04 03:06:04,609 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-09-04 03:06:04,615 - app.db.mongodb - INFO - Connected to MongoDB
2025-09-04 03:06:15,064 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-09-04 03:06:15,065 - app.db.mongodb - INFO - MongoDB connection closed
2025-09-04 03:06:16,212 - app.core.logging - INFO - Logging setup completed
2025-09-04 03:06:16,232 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-09-04 03:06:16,238 - app.db.mongodb - INFO - Connected to MongoDB
2025-09-04 03:06:25,182 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-09-04 03:06:25,182 - app.db.mongodb - INFO - MongoDB connection closed
2025-09-04 03:06:26,273 - app.core.logging - INFO - Logging setup completed
2025-09-04 03:06:26,305 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-09-04 03:06:26,314 - app.db.mongodb - INFO - Connected to MongoDB
2025-09-04 03:06:40,768 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-09-04 03:06:40,769 - app.db.mongodb - INFO - MongoDB connection closed
2025-09-04 03:06:41,965 - app.core.logging - INFO - Logging setup completed
2025-09-04 03:06:41,989 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-09-04 03:06:41,995 - app.db.mongodb - INFO - Connected to MongoDB
2025-09-04 03:06:51,029 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-09-04 03:06:51,030 - app.db.mongodb - INFO - MongoDB connection closed
2025-09-04 03:06:52,062 - app.core.logging - INFO - Logging setup completed
2025-09-04 03:06:52,083 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-09-04 03:06:52,089 - app.db.mongodb - INFO - Connected to MongoDB
2025-09-04 03:06:59,825 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-09-04 03:06:59,826 - app.db.mongodb - INFO - MongoDB connection closed
2025-09-04 03:07:00,838 - app.core.logging - INFO - Logging setup completed
2025-09-04 03:07:00,859 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-09-04 03:07:00,865 - app.db.mongodb - INFO - Connected to MongoDB
2025-09-04 03:07:17,342 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-09-04 03:07:17,342 - app.db.mongodb - INFO - MongoDB connection closed
2025-09-04 03:07:18,220 - app.core.logging - INFO - Logging setup completed
2025-09-04 03:07:18,244 - app.db.mongodb - INFO - Connecting to MongoDB...
2025-09-04 03:07:18,249 - app.db.mongodb - INFO - Connected to MongoDB
2025-09-04 03:07:39,338 - app.db.mongodb - INFO - Closing MongoDB connection...
2025-09-04 03:07:39,339 - app.db.mongodb - INFO - MongoDB connection closed
2025-09-04 03:07:40 - app.core.logging - INFO - setup_logging:67 - ==================================================
2025-09-04 03:07:40 - app.core.logging - INFO - setup_logging:68 - Enhanced logging setup completed
2025-09-04 03:07:40 - app.core.logging - INFO - setup_logging:69 - Log level: INFO
2025-09-04 03:07:40 - app.core.logging - INFO - setup_logging:70 - Log file: logs/app.log
2025-09-04 03:07:40 - app.core.logging - INFO - setup_logging:71 - Error log file: logs/app_errors.log
2025-09-04 03:07:40 - app.core.logging - INFO - setup_logging:72 - ==================================================
2025-09-04 03:07:40 - app.db.mongodb - INFO - connect_to_mongo:14 - Connecting to MongoDB...
2025-09-04 03:07:40 - app.db.mongodb - INFO - connect_to_mongo:17 - Connected to MongoDB
2025-09-04 03:26:42 - app.db.mongodb - INFO - close_mongo_connection:21 - Closing MongoDB connection...
2025-09-04 03:26:42 - app.db.mongodb - INFO - close_mongo_connection:24 - MongoDB connection closed
2025-09-04 03:26:50 - app.core.logging - INFO - setup_logging:67 - ==================================================
2025-09-04 03:26:50 - app.core.logging - INFO - setup_logging:68 - Enhanced logging setup completed
2025-09-04 03:26:50 - app.core.logging - INFO - setup_logging:69 - Log level: INFO
2025-09-04 03:26:50 - app.core.logging - INFO - setup_logging:70 - Log file: logs/app.log
2025-09-04 03:26:50 - app.core.logging - INFO - setup_logging:71 - Error log file: logs/app_errors.log
2025-09-04 03:26:50 - app.core.logging - INFO - setup_logging:72 - ==================================================
2025-09-04 03:26:50 - app.db.mongodb - INFO - connect_to_mongo:14 - Connecting to MongoDB...
2025-09-04 03:26:50 - app.db.mongodb - INFO - connect_to_mongo:17 - Connected to MongoDB
2025-09-04 03:27:46 - app.db.mongodb - INFO - close_mongo_connection:21 - Closing MongoDB connection...
2025-09-04 03:27:46 - app.db.mongodb - INFO - close_mongo_connection:24 - MongoDB connection closed
2025-09-04 03:28:18 - app.core.logging - INFO - setup_logging:67 - ==================================================
2025-09-04 03:28:18 - app.core.logging - INFO - setup_logging:68 - Enhanced logging setup completed
2025-09-04 03:28:18 - app.core.logging - INFO - setup_logging:69 - Log level: INFO
2025-09-04 03:28:18 - app.core.logging - INFO - setup_logging:70 - Log file: logs/app.log
2025-09-04 03:28:18 - app.core.logging - INFO - setup_logging:71 - Error log file: logs/app_errors.log
2025-09-04 03:28:18 - app.core.logging - INFO - setup_logging:72 - ==================================================
2025-09-04 03:28:18 - app.db.mongodb - INFO - connect_to_mongo:14 - Connecting to MongoDB...
2025-09-04 03:28:18 - app.db.mongodb - INFO - connect_to_mongo:17 - Connected to MongoDB
2025-09-04 03:29:20 - app.services.recommend_service - DEBUG - get_by_date:29 - Starting get_by_date operation for date: 2025-09-03, signal_type: None
2025-09-04 03:29:20 - app.services.recommend_service - DEBUG - get_by_date:37 - Querying database with: {'hit_date': '2025-09-03'}
2025-09-04 03:29:20 - app.services.recommend_service - DEBUG - get_by_date:41 - Found 0 recommendations for date 2025-09-03
2025-09-04 03:29:20 - app.services.recommend_service - INFO - get_by_date:43 - Successfully retrieved 0 recommendations for date 2025-09-03
2025-09-04 03:29:37 - app.services.pattern_service - DEBUG - get_patterns:25 - Starting get_patterns operation
2025-09-04 03:29:37 - app.services.pattern_service - DEBUG - get_patterns:29 - Found 11 patterns in database
2025-09-04 03:29:37 - app.services.pattern_service - INFO - get_patterns:32 - Successfully retrieved 11 buy patterns
2025-09-04 03:30:42 - app.db.mongodb - INFO - close_mongo_connection:21 - Closing MongoDB connection...
2025-09-04 03:30:42 - app.db.mongodb - INFO - close_mongo_connection:24 - MongoDB connection closed
2025-09-04 03:30:43 - app.core.logging - INFO - setup_logging:67 - ==================================================
2025-09-04 03:30:43 - app.core.logging - INFO - setup_logging:68 - Enhanced logging setup completed
2025-09-04 03:30:43 - app.core.logging - INFO - setup_logging:69 - Log level: INFO
2025-09-04 03:30:43 - app.core.logging - INFO - setup_logging:70 - Log file: logs/app.log
2025-09-04 03:30:43 - app.core.logging - INFO - setup_logging:71 - Error log file: logs/app_errors.log
2025-09-04 03:30:43 - app.core.logging - INFO - setup_logging:72 - ==================================================
2025-09-04 03:30:43 - app.db.mongodb - INFO - connect_to_mongo:14 - Connecting to MongoDB...
2025-09-04 03:30:43 - app.db.mongodb - INFO - connect_to_mongo:17 - Connected to MongoDB
2025-09-04 03:32:05 - app.db.mongodb - INFO - close_mongo_connection:21 - Closing MongoDB connection...
2025-09-04 03:32:05 - app.db.mongodb - INFO - close_mongo_connection:24 - MongoDB connection closed
2025-09-04 03:32:05 - app.core.logging - INFO - setup_logging:67 - ==================================================
2025-09-04 03:32:05 - app.core.logging - INFO - setup_logging:68 - Enhanced logging setup completed
2025-09-04 03:32:05 - app.core.logging - INFO - setup_logging:69 - Log level: INFO
2025-09-04 03:32:05 - app.core.logging - INFO - setup_logging:70 - Log file: logs/app.log
2025-09-04 03:32:05 - app.core.logging - INFO - setup_logging:71 - Error log file: logs/app_errors.log
2025-09-04 03:32:05 - app.core.logging - INFO - setup_logging:72 - ==================================================
2025-09-04 03:32:06 - app.main - INFO - startup_event:43 - 🚀 Starting up Stock Data API...
2025-09-04 03:32:06 - app.db.mongodb - INFO - connect_to_mongo:14 - Connecting to MongoDB...
2025-09-04 03:32:06 - app.db.mongodb - INFO - connect_to_mongo:17 - Connected to MongoDB
2025-09-04 03:32:06 - app.main - INFO - startup_event:46 - ✅ Successfully connected to MongoDB
2025-09-04 03:32:06 - app.main - INFO - startup_event:47 - 🎯 API is ready to serve requests
2025-09-04 03:32:26 - app.main - INFO - shutdown_event:55 - 🛑 Shutting down Stock Data API...
2025-09-04 03:32:26 - app.db.mongodb - INFO - close_mongo_connection:21 - Closing MongoDB connection...
2025-09-04 03:32:26 - app.db.mongodb - INFO - close_mongo_connection:24 - MongoDB connection closed
2025-09-04 03:32:26 - app.main - INFO - shutdown_event:58 - ✅ Successfully disconnected from MongoDB
2025-09-04 03:32:26 - app.main - INFO - shutdown_event:59 - 👋 API shutdown completed
2025-09-04 03:32:27 - app.core.logging - INFO - setup_logging:67 - ==================================================
2025-09-04 03:32:27 - app.core.logging - INFO - setup_logging:68 - Enhanced logging setup completed
2025-09-04 03:32:27 - app.core.logging - INFO - setup_logging:69 - Log level: INFO
2025-09-04 03:32:27 - app.core.logging - INFO - setup_logging:70 - Log file: logs/app.log
2025-09-04 03:32:27 - app.core.logging - INFO - setup_logging:71 - Error log file: logs/app_errors.log
2025-09-04 03:32:27 - app.core.logging - INFO - setup_logging:72 - ==================================================
2025-09-04 03:32:27 - app.main - INFO - startup_event:43 - 🚀 Starting up Stock Data API...
2025-09-04 03:32:27 - app.db.mongodb - INFO - connect_to_mongo:14 - Connecting to MongoDB...
2025-09-04 03:32:27 - app.db.mongodb - INFO - connect_to_mongo:17 - Connected to MongoDB
2025-09-04 03:32:27 - app.main - INFO - startup_event:46 - ✅ Successfully connected to MongoDB
2025-09-04 03:32:27 - app.main - INFO - startup_event:47 - 🎯 API is ready to serve requests
2025-09-04 03:32:39 - app.main - INFO - shutdown_event:55 - 🛑 Shutting down Stock Data API...
2025-09-04 03:32:39 - app.db.mongodb - INFO - close_mongo_connection:21 - Closing MongoDB connection...
2025-09-04 03:32:39 - app.db.mongodb - INFO - close_mongo_connection:24 - MongoDB connection closed
2025-09-04 03:32:39 - app.main - INFO - shutdown_event:58 - ✅ Successfully disconnected from MongoDB
2025-09-04 03:32:39 - app.main - INFO - shutdown_event:59 - 👋 API shutdown completed
2025-09-04 03:32:40 - app.core.logging - INFO - setup_logging:67 - ==================================================
2025-09-04 03:32:40 - app.core.logging - INFO - setup_logging:68 - Enhanced logging setup completed
2025-09-04 03:32:40 - app.core.logging - INFO - setup_logging:69 - Log level: INFO
2025-09-04 03:32:40 - app.core.logging - INFO - setup_logging:70 - Log file: logs/app.log
2025-09-04 03:32:40 - app.core.logging - INFO - setup_logging:71 - Error log file: logs/app_errors.log
2025-09-04 03:32:40 - app.core.logging - INFO - setup_logging:72 - ==================================================
2025-09-04 03:32:40 - app.main - INFO - startup_event:43 - 🚀 Starting up Stock Data API...
2025-09-04 03:32:40 - app.db.mongodb - INFO - connect_to_mongo:14 - Connecting to MongoDB...
2025-09-04 03:32:40 - app.db.mongodb - INFO - connect_to_mongo:17 - Connected to MongoDB
2025-09-04 03:32:40 - app.main - INFO - startup_event:46 - ✅ Successfully connected to MongoDB
2025-09-04 03:32:40 - app.main - INFO - startup_event:47 - 🎯 API is ready to serve requests
2025-09-04 03:32:49 - app.main - INFO - shutdown_event:55 - 🛑 Shutting down Stock Data API...
2025-09-04 03:32:49 - app.db.mongodb - INFO - close_mongo_connection:21 - Closing MongoDB connection...
2025-09-04 03:32:49 - app.db.mongodb - INFO - close_mongo_connection:24 - MongoDB connection closed
2025-09-04 03:32:49 - app.main - INFO - shutdown_event:58 - ✅ Successfully disconnected from MongoDB
2025-09-04 03:32:49 - app.main - INFO - shutdown_event:59 - 👋 API shutdown completed
2025-09-04 03:32:50 - app.core.logging - INFO - setup_logging:67 - ==================================================
2025-09-04 03:32:50 - app.core.logging - INFO - setup_logging:68 - Enhanced logging setup completed
2025-09-04 03:32:50 - app.core.logging - INFO - setup_logging:69 - Log level: INFO
2025-09-04 03:32:50 - app.core.logging - INFO - setup_logging:70 - Log file: logs/app.log
2025-09-04 03:32:50 - app.core.logging - INFO - setup_logging:71 - Error log file: logs/app_errors.log
2025-09-04 03:32:50 - app.core.logging - INFO - setup_logging:72 - ==================================================
2025-09-04 03:32:50 - app.main - INFO - startup_event:43 - 🚀 Starting up Stock Data API...
2025-09-04 03:32:50 - app.db.mongodb - INFO - connect_to_mongo:14 - Connecting to MongoDB...
2025-09-04 03:32:50 - app.db.mongodb - INFO - connect_to_mongo:17 - Connected to MongoDB
2025-09-04 03:32:50 - app.main - INFO - startup_event:46 - ✅ Successfully connected to MongoDB
2025-09-04 03:32:50 - app.main - INFO - startup_event:47 - 🎯 API is ready to serve requests
2025-09-04 03:33:01 - app.main - INFO - shutdown_event:55 - 🛑 Shutting down Stock Data API...
2025-09-04 03:33:01 - app.db.mongodb - INFO - close_mongo_connection:21 - Closing MongoDB connection...
2025-09-04 03:33:01 - app.db.mongodb - INFO - close_mongo_connection:24 - MongoDB connection closed
2025-09-04 03:33:01 - app.main - INFO - shutdown_event:58 - ✅ Successfully disconnected from MongoDB
2025-09-04 03:33:01 - app.main - INFO - shutdown_event:59 - 👋 API shutdown completed
2025-09-04 03:33:02 - app.core.logging - INFO - setup_logging:67 - ==================================================
2025-09-04 03:33:02 - app.core.logging - INFO - setup_logging:68 - Enhanced logging setup completed
2025-09-04 03:33:02 - app.core.logging - INFO - setup_logging:69 - Log level: INFO
2025-09-04 03:33:02 - app.core.logging - INFO - setup_logging:70 - Log file: logs/app.log
2025-09-04 03:33:02 - app.core.logging - INFO - setup_logging:71 - Error log file: logs/app_errors.log
2025-09-04 03:33:02 - app.core.logging - INFO - setup_logging:72 - ==================================================
2025-09-04 03:33:02 - app.main - INFO - startup_event:43 - 🚀 Starting up Stock Data API...
2025-09-04 03:33:02 - app.db.mongodb - INFO - connect_to_mongo:14 - Connecting to MongoDB...
2025-09-04 03:33:02 - app.db.mongodb - INFO - connect_to_mongo:17 - Connected to MongoDB
2025-09-04 03:33:02 - app.main - INFO - startup_event:46 - ✅ Successfully connected to MongoDB
2025-09-04 03:33:02 - app.main - INFO - startup_event:47 - 🎯 API is ready to serve requests
2025-09-04 03:33:12 - app.main - INFO - shutdown_event:55 - 🛑 Shutting down Stock Data API...
2025-09-04 03:33:12 - app.db.mongodb - INFO - close_mongo_connection:21 - Closing MongoDB connection...
2025-09-04 03:33:12 - app.db.mongodb - INFO - close_mongo_connection:24 - MongoDB connection closed
2025-09-04 03:33:12 - app.main - INFO - shutdown_event:58 - ✅ Successfully disconnected from MongoDB
2025-09-04 03:33:12 - app.main - INFO - shutdown_event:59 - 👋 API shutdown completed
2025-09-04 03:33:13 - app.core.logging - INFO - setup_logging:67 - ==================================================
2025-09-04 03:33:13 - app.core.logging - INFO - setup_logging:68 - Enhanced logging setup completed
2025-09-04 03:33:13 - app.core.logging - INFO - setup_logging:69 - Log level: INFO
2025-09-04 03:33:13 - app.core.logging - INFO - setup_logging:70 - Log file: logs/app.log
2025-09-04 03:33:13 - app.core.logging - INFO - setup_logging:71 - Error log file: logs/app_errors.log
2025-09-04 03:33:13 - app.core.logging - INFO - setup_logging:72 - ==================================================
2025-09-04 03:33:13 - app.main - INFO - startup_event:43 - 🚀 Starting up Stock Data API...
2025-09-04 03:33:13 - app.db.mongodb - INFO - connect_to_mongo:14 - Connecting to MongoDB...
2025-09-04 03:33:13 - app.db.mongodb - INFO - connect_to_mongo:17 - Connected to MongoDB
2025-09-04 03:33:13 - app.main - INFO - startup_event:46 - ✅ Successfully connected to MongoDB
2025-09-04 03:33:13 - app.main - INFO - startup_event:47 - 🎯 API is ready to serve requests
2025-09-04 03:33:23 - app.main - INFO - shutdown_event:55 - 🛑 Shutting down Stock Data API...
2025-09-04 03:33:23 - app.db.mongodb - INFO - close_mongo_connection:21 - Closing MongoDB connection...
2025-09-04 03:33:23 - app.db.mongodb - INFO - close_mongo_connection:24 - MongoDB connection closed
2025-09-04 03:33:23 - app.main - INFO - shutdown_event:58 - ✅ Successfully disconnected from MongoDB
2025-09-04 03:33:23 - app.main - INFO - shutdown_event:59 - 👋 API shutdown completed
2025-09-04 03:33:24 - app.core.logging - INFO - setup_logging:67 - ==================================================
2025-09-04 03:33:24 - app.core.logging - INFO - setup_logging:68 - Enhanced logging setup completed
2025-09-04 03:33:24 - app.core.logging - INFO - setup_logging:69 - Log level: INFO
2025-09-04 03:33:24 - app.core.logging - INFO - setup_logging:70 - Log file: logs/app.log
2025-09-04 03:33:24 - app.core.logging - INFO - setup_logging:71 - Error log file: logs/app_errors.log
2025-09-04 03:33:24 - app.core.logging - INFO - setup_logging:72 - ==================================================
2025-09-04 03:33:24 - app.main - INFO - startup_event:43 - 🚀 Starting up Stock Data API...
2025-09-04 03:33:24 - app.db.mongodb - INFO - connect_to_mongo:14 - Connecting to MongoDB...
2025-09-04 03:33:24 - app.db.mongodb - INFO - connect_to_mongo:17 - Connected to MongoDB
2025-09-04 03:33:24 - app.main - INFO - startup_event:46 - ✅ Successfully connected to MongoDB
2025-09-04 03:33:24 - app.main - INFO - startup_event:47 - 🎯 API is ready to serve requests
2025-09-04 03:33:35 - app.main - INFO - shutdown_event:55 - 🛑 Shutting down Stock Data API...
2025-09-04 03:33:35 - app.db.mongodb - INFO - close_mongo_connection:21 - Closing MongoDB connection...
2025-09-04 03:33:35 - app.db.mongodb - INFO - close_mongo_connection:24 - MongoDB connection closed
2025-09-04 03:33:35 - app.main - INFO - shutdown_event:58 - ✅ Successfully disconnected from MongoDB
2025-09-04 03:33:35 - app.main - INFO - shutdown_event:59 - 👋 API shutdown completed
2025-09-04 03:33:36 - app.core.logging - INFO - setup_logging:67 - ==================================================
2025-09-04 03:33:36 - app.core.logging - INFO - setup_logging:68 - Enhanced logging setup completed
2025-09-04 03:33:36 - app.core.logging - INFO - setup_logging:69 - Log level: INFO
2025-09-04 03:33:36 - app.core.logging - INFO - setup_logging:70 - Log file: logs/app.log
2025-09-04 03:33:36 - app.core.logging - INFO - setup_logging:71 - Error log file: logs/app_errors.log
2025-09-04 03:33:36 - app.core.logging - INFO - setup_logging:72 - ==================================================
2025-09-04 03:33:36 - app.main - INFO - startup_event:43 - 🚀 Starting up Stock Data API...
2025-09-04 03:33:36 - app.db.mongodb - INFO - connect_to_mongo:14 - Connecting to MongoDB...
2025-09-04 03:33:36 - app.db.mongodb - INFO - connect_to_mongo:17 - Connected to MongoDB
2025-09-04 03:33:36 - app.main - INFO - startup_event:46 - ✅ Successfully connected to MongoDB
2025-09-04 03:33:36 - app.main - INFO - startup_event:47 - 🎯 API is ready to serve requests
2025-09-04 03:33:55 - app.main - INFO - shutdown_event:55 - 🛑 Shutting down Stock Data API...
2025-09-04 03:33:55 - app.db.mongodb - INFO - close_mongo_connection:21 - Closing MongoDB connection...
2025-09-04 03:33:55 - app.db.mongodb - INFO - close_mongo_connection:24 - MongoDB connection closed
2025-09-04 03:33:55 - app.main - INFO - shutdown_event:58 - ✅ Successfully disconnected from MongoDB
2025-09-04 03:33:55 - app.main - INFO - shutdown_event:59 - 👋 API shutdown completed
2025-09-04 03:33:56 - app.core.logging - INFO - setup_logging:67 - ==================================================
2025-09-04 03:33:56 - app.core.logging - INFO - setup_logging:68 - Enhanced logging setup completed
2025-09-04 03:33:56 - app.core.logging - INFO - setup_logging:69 - Log level: INFO
2025-09-04 03:33:56 - app.core.logging - INFO - setup_logging:70 - Log file: logs/app.log
2025-09-04 03:33:56 - app.core.logging - INFO - setup_logging:71 - Error log file: logs/app_errors.log
2025-09-04 03:33:56 - app.core.logging - INFO - setup_logging:72 - ==================================================
2025-09-04 03:33:56 - app.main - INFO - startup_event:43 - 🚀 Starting up Stock Data API...
2025-09-04 03:33:56 - app.db.mongodb - INFO - connect_to_mongo:14 - Connecting to MongoDB...
2025-09-04 03:33:56 - app.db.mongodb - INFO - connect_to_mongo:17 - Connected to MongoDB
2025-09-04 03:33:56 - app.main - INFO - startup_event:46 - ✅ Successfully connected to MongoDB
2025-09-04 03:33:56 - app.main - INFO - startup_event:47 - 🎯 API is ready to serve requests
2025-09-04 03:34:07 - app.main - INFO - shutdown_event:55 - 🛑 Shutting down Stock Data API...
2025-09-04 03:34:07 - app.db.mongodb - INFO - close_mongo_connection:21 - Closing MongoDB connection...
2025-09-04 03:34:07 - app.db.mongodb - INFO - close_mongo_connection:24 - MongoDB connection closed
2025-09-04 03:34:07 - app.main - INFO - shutdown_event:58 - ✅ Successfully disconnected from MongoDB
2025-09-04 03:34:07 - app.main - INFO - shutdown_event:59 - 👋 API shutdown completed
2025-09-04 03:34:08 - app.core.logging - INFO - setup_logging:67 - ==================================================
2025-09-04 03:34:08 - app.core.logging - INFO - setup_logging:68 - Enhanced logging setup completed
2025-09-04 03:34:08 - app.core.logging - INFO - setup_logging:69 - Log level: INFO
2025-09-04 03:34:08 - app.core.logging - INFO - setup_logging:70 - Log file: logs/app.log
2025-09-04 03:34:08 - app.core.logging - INFO - setup_logging:71 - Error log file: logs/app_errors.log
2025-09-04 03:34:08 - app.core.logging - INFO - setup_logging:72 - ==================================================
2025-09-04 03:34:08 - app.main - INFO - startup_event:43 - 🚀 Starting up Stock Data API...
2025-09-04 03:34:08 - app.db.mongodb - INFO - connect_to_mongo:14 - Connecting to MongoDB...
2025-09-04 03:34:08 - app.db.mongodb - INFO - connect_to_mongo:17 - Connected to MongoDB
2025-09-04 03:34:08 - app.main - INFO - startup_event:46 - ✅ Successfully connected to MongoDB
2025-09-04 03:34:08 - app.main - INFO - startup_event:47 - 🎯 API is ready to serve requests
2025-09-04 03:39:10 - app.main - INFO - shutdown_event:55 - 🛑 Shutting down Stock Data API...
2025-09-04 03:39:10 - app.db.mongodb - INFO - close_mongo_connection:21 - Closing MongoDB connection...
2025-09-04 03:39:10 - app.db.mongodb - INFO - close_mongo_connection:24 - MongoDB connection closed
2025-09-04 03:39:10 - app.main - INFO - shutdown_event:58 - ✅ Successfully disconnected from MongoDB
2025-09-04 03:39:10 - app.main - INFO - shutdown_event:59 - 👋 API shutdown completed
2025-09-04 03:39:11 - app.core.logging - INFO - setup_logging:67 - ==================================================
2025-09-04 03:39:11 - app.core.logging - INFO - setup_logging:68 - Enhanced logging setup completed
2025-09-04 03:39:11 - app.core.logging - INFO - setup_logging:69 - Log level: INFO
2025-09-04 03:39:11 - app.core.logging - INFO - setup_logging:70 - Log file: logs/app.log
2025-09-04 03:39:11 - app.core.logging - INFO - setup_logging:71 - Error log file: logs/app_errors.log
2025-09-04 03:39:11 - app.core.logging - INFO - setup_logging:72 - ==================================================
2025-09-04 03:39:12 - app.main - INFO - startup_event:43 - 🚀 Starting up Stock Data API...
2025-09-04 03:39:12 - app.db.mongodb - INFO - connect_to_mongo:14 - 🔌 Connecting to MongoDB at mongodb://mongodb:27017
2025-09-04 03:39:12 - app.db.mongodb - DEBUG - connect_to_mongo:15 - Database name: stock_recommend_db
2025-09-04 03:39:12 - app.db.mongodb - INFO - connect_to_mongo:23 - ✅ Successfully connected to MongoDB
2025-09-04 03:39:12 - app.db.mongodb - DEBUG - connect_to_mongo:24 - Connected to database: stock_recommend_db
2025-09-04 03:39:12 - app.main - INFO - startup_event:46 - ✅ Successfully connected to MongoDB
2025-09-04 03:39:12 - app.main - INFO - startup_event:47 - 🎯 API is ready to serve requests
2025-09-04 04:19:05 - app.main - INFO - shutdown_event:55 - 🛑 Shutting down Stock Data API...
2025-09-04 04:19:05 - app.db.mongodb - INFO - close_mongo_connection:32 - 🔌 Closing MongoDB connection...
2025-09-04 04:19:05 - app.db.mongodb - INFO - close_mongo_connection:36 - ✅ MongoDB connection closed successfully
2025-09-04 04:19:05 - app.main - INFO - shutdown_event:58 - ✅ Successfully disconnected from MongoDB
2025-09-04 04:19:05 - app.main - INFO - shutdown_event:59 - 👋 API shutdown completed
2025-09-04 04:19:12 - app.core.logging - INFO - setup_logging:67 - ==================================================
2025-09-04 04:19:12 - app.core.logging - INFO - setup_logging:68 - Enhanced logging setup completed
2025-09-04 04:19:12 - app.core.logging - INFO - setup_logging:69 - Log level: INFO
2025-09-04 04:19:12 - app.core.logging - INFO - setup_logging:70 - Log file: logs/app.log
2025-09-04 04:19:12 - app.core.logging - INFO - setup_logging:71 - Error log file: logs/app_errors.log
2025-09-04 04:19:12 - app.core.logging - INFO - setup_logging:72 - ==================================================
2025-09-04 04:19:12 - app.main - INFO - startup_event:43 - 🚀 Starting up Stock Data API...
2025-09-04 04:19:12 - app.db.mongodb - INFO - connect_to_mongo:14 - 🔌 Connecting to MongoDB at mongodb://mongodb:27017
2025-09-04 04:19:12 - app.db.mongodb - DEBUG - connect_to_mongo:15 - Database name: stock_recommend_db
2025-09-04 04:19:12 - app.db.mongodb - INFO - connect_to_mongo:23 - ✅ Successfully connected to MongoDB
2025-09-04 04:19:12 - app.db.mongodb - DEBUG - connect_to_mongo:24 - Connected to database: stock_recommend_db
2025-09-04 04:19:12 - app.main - INFO - startup_event:46 - ✅ Successfully connected to MongoDB
2025-09-04 04:19:12 - app.main - INFO - startup_event:47 - 🎯 API is ready to serve requests
2025-09-04 04:19:44 - app.core.middleware - INFO - _log_request:58 - Request 1756959584310: GET /docs from **********
2025-09-04 04:19:44 - app.core.middleware - DEBUG - _log_request:74 - Request 1756959584310 headers: {'host': 'localhost:8502', 'connection': 'keep-alive', 'cache-control': 'max-age=0', 'sec-ch-ua': '"Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"', 'sec-ch-ua-mobile': '?0', 'sec-ch-ua-platform': '"Linux"', 'upgrade-insecure-requests': '1', 'user-agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7', 'sec-fetch-site': 'none', 'sec-fetch-mode': 'navigate', 'sec-fetch-user': '?1', 'sec-fetch-dest': 'document', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'vi,en;q=0.9,vi-VN;q=0.8,en-US;q=0.7', 'cookie': '[REDACTED]'}
2025-09-04 04:19:44 - app.core.middleware - INFO - _log_response:91 - Response 1756959584310: 200 in 0.0018s
2025-09-04 04:19:44 - app.core.middleware - DEBUG - _log_response:98 - Response 1756959584310 headers: {'content-length': '937', 'content-type': 'text/html; charset=utf-8'}
2025-09-04 04:19:44 - app.core.middleware - INFO - _log_request:58 - Request 1756959584477: GET /api/v1/openapi.json from **********
2025-09-04 04:19:44 - app.core.middleware - DEBUG - _log_request:74 - Request 1756959584477 headers: {'host': 'localhost:8502', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"Linux"', 'user-agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json,*/*', 'sec-ch-ua': '"Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"', 'sec-ch-ua-mobile': '?0', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:8502/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'vi,en;q=0.9,vi-VN;q=0.8,en-US;q=0.7', 'cookie': '[REDACTED]'}
2025-09-04 04:19:44 - app.core.middleware - INFO - _log_response:91 - Response 1756959584477: 200 in 0.0460s
2025-09-04 04:19:44 - app.core.middleware - DEBUG - _log_response:98 - Response 1756959584477 headers: {'content-length': '12673', 'content-type': 'application/json'}
2025-09-04 04:20:07 - app.core.middleware - INFO - _log_request:58 - Request 1756959607393: GET /api/v1/recommends from **********
2025-09-04 04:20:07 - app.core.middleware - DEBUG - _log_request:65 - Request 1756959607393 query params: {'target_date': '2025-09-03'}
2025-09-04 04:20:07 - app.core.middleware - DEBUG - _log_request:74 - Request 1756959607393 headers: {'host': 'localhost:8502', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"Linux"', 'user-agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json', 'sec-ch-ua': '"Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"', 'x-api-key': '[REDACTED]', 'sec-ch-ua-mobile': '?0', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:8502/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'vi,en;q=0.9,vi-VN;q=0.8,en-US;q=0.7', 'cookie': '[REDACTED]'}
2025-09-04 04:20:07 - app.api.routes - INFO - get_recommends:34 - 📊 GET /recommends - date: 2025-09-03, signal_type: None
2025-09-04 04:20:07 - app.services.recommend_service - DEBUG - get_by_date:29 - Starting get_by_date operation for date: 2025-09-03, signal_type: None
2025-09-04 04:20:07 - app.services.recommend_service - DEBUG - get_by_date:37 - Querying database with: {'hit_date': '2025-09-03'}
2025-09-04 04:20:07 - app.services.recommend_service - DEBUG - get_by_date:41 - Found 0 recommendations for date 2025-09-03
2025-09-04 04:20:07 - app.services.recommend_service - INFO - get_by_date:43 - Successfully retrieved 0 recommendations for date 2025-09-03
2025-09-04 04:20:07 - app.api.routes - WARNING - get_recommends:41 - No recommendations found for date 2025-09-03 with signal_type None
2025-09-04 04:20:07 - app.api.routes - WARNING - get_recommends:47 - HTTP error in get_recommends: Recommendation not found
2025-09-04 04:20:07 - app.core.middleware - INFO - _log_response:91 - Response 1756959607393: 404 in 0.0081s
2025-09-04 04:20:07 - app.core.middleware - DEBUG - _log_response:98 - Response 1756959607393 headers: {'content-length': '37', 'content-type': 'application/json'}
2025-09-04 04:20:12 - app.core.middleware - INFO - _log_request:58 - Request 1756959612988: GET /api/v1/recommends from **********
2025-09-04 04:20:12 - app.core.middleware - DEBUG - _log_request:65 - Request 1756959612988 query params: {'target_date': '2025-09-03'}
2025-09-04 04:20:12 - app.core.middleware - DEBUG - _log_request:74 - Request 1756959612988 headers: {'host': 'localhost:8502', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"Linux"', 'user-agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json', 'sec-ch-ua': '"Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"', 'x-api-key': '[REDACTED]', 'sec-ch-ua-mobile': '?0', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:8502/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'vi,en;q=0.9,vi-VN;q=0.8,en-US;q=0.7', 'cookie': '[REDACTED]'}
2025-09-04 04:20:12 - app.api.routes - INFO - get_recommends:34 - 📊 GET /recommends - date: 2025-09-03, signal_type: None
2025-09-04 04:20:12 - app.services.recommend_service - DEBUG - get_by_date:29 - Starting get_by_date operation for date: 2025-09-03, signal_type: None
2025-09-04 04:20:12 - app.services.recommend_service - DEBUG - get_by_date:37 - Querying database with: {'hit_date': '2025-09-03'}
2025-09-04 04:20:12 - app.services.recommend_service - DEBUG - get_by_date:41 - Found 0 recommendations for date 2025-09-03
2025-09-04 04:20:12 - app.services.recommend_service - INFO - get_by_date:43 - Successfully retrieved 0 recommendations for date 2025-09-03
2025-09-04 04:20:12 - app.api.routes - WARNING - get_recommends:41 - No recommendations found for date 2025-09-03 with signal_type None
2025-09-04 04:20:12 - app.api.routes - WARNING - get_recommends:47 - HTTP error in get_recommends: Recommendation not found
2025-09-04 04:20:12 - app.core.middleware - INFO - _log_response:91 - Response 1756959612988: 404 in 0.0044s
2025-09-04 04:20:12 - app.core.middleware - DEBUG - _log_response:98 - Response 1756959612988 headers: {'content-length': '37', 'content-type': 'application/json'}
2025-09-04 04:20:29 - app.core.middleware - INFO - _log_request:58 - Request 1756959629020: POST /api/v1/recommends from **********
2025-09-04 04:20:29 - app.core.middleware - DEBUG - _log_request:74 - Request 1756959629020 headers: {'host': 'localhost:8502', 'connection': 'keep-alive', 'content-length': '160', 'sec-ch-ua-platform': '"Linux"', 'user-agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json', 'sec-ch-ua': '"Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"', 'content-type': 'application/json', 'x-api-key': '[REDACTED]', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:8502', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:8502/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'vi,en;q=0.9,vi-VN;q=0.8,en-US;q=0.7', 'cookie': '[REDACTED]'}
2025-09-04 04:20:29 - app.core.middleware - DEBUG - _log_request:81 - Request 1756959629020 body: {
  "close_price": 85.5,
  "hit_date": "2024-02-01",
  "median_volume_1m": 150000,
  "pattern": "_BullDvg",
  "score": 4,
  "signal": "buy",
  "ticker": "VNM"
}
2025-09-04 04:20:29 - app.api.routes - INFO - insert_recommends:92 - 💾 POST /recommends - inserting 1 recommendation(s)
2025-09-04 04:20:29 - app.services.recommend_service - DEBUG - insert_db:83 - Starting insert_db operation with data type: <class 'app.models.recommend_model.StockRecommend'>
2025-09-04 04:20:29 - app.services.recommend_service - DEBUG - insert_db:98 - Inserting single stock recommendation for ticker: VNM
2025-09-04 04:20:29 - app.services.recommend_service - DEBUG - insert_db:100 - Single data keys: ['signal', 'ticker', 'hit_date', 'pattern', 'median_volume_1m', 'close_price', 'score']
2025-09-04 04:20:29 - app.services.recommend_service - DEBUG - insert_db:103 - Insert one result - inserted_id: 68b9138d09aeb9f21b11422b
2025-09-04 04:20:29 - app.services.recommend_service - INFO - insert_db:104 - Successfully inserted ticker data for VNM on 2024-02-01
2025-09-04 04:20:29 - app.api.routes - INFO - insert_recommends:100 - ✅ Successfully inserted 1 recommendation(s)
2025-09-04 04:20:29 - app.core.middleware - INFO - _log_response:91 - Response 1756959629020: 200 in 0.0125s
2025-09-04 04:20:29 - app.core.middleware - DEBUG - _log_response:98 - Response 1756959629020 headers: {'content-length': '4', 'content-type': 'application/json'}
2025-09-04 04:20:31 - app.core.middleware - INFO - _log_request:58 - Request 1756959631761: POST /api/v1/recommends from **********
2025-09-04 04:20:31 - app.core.middleware - DEBUG - _log_request:74 - Request 1756959631761 headers: {'host': 'localhost:8502', 'connection': 'keep-alive', 'content-length': '160', 'sec-ch-ua-platform': '"Linux"', 'user-agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json', 'sec-ch-ua': '"Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"', 'content-type': 'application/json', 'x-api-key': '[REDACTED]', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:8502', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:8502/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'vi,en;q=0.9,vi-VN;q=0.8,en-US;q=0.7', 'cookie': '[REDACTED]'}
2025-09-04 04:20:31 - app.core.middleware - DEBUG - _log_request:81 - Request 1756959631761 body: {
  "close_price": 85.5,
  "hit_date": "2024-02-01",
  "median_volume_1m": 150000,
  "pattern": "_BullDvg",
  "score": 4,
  "signal": "buy",
  "ticker": "VNM"
}
2025-09-04 04:20:31 - app.api.routes - INFO - insert_recommends:92 - 💾 POST /recommends - inserting 1 recommendation(s)
2025-09-04 04:20:31 - app.services.recommend_service - DEBUG - insert_db:83 - Starting insert_db operation with data type: <class 'app.models.recommend_model.StockRecommend'>
2025-09-04 04:20:31 - app.services.recommend_service - DEBUG - insert_db:98 - Inserting single stock recommendation for ticker: VNM
2025-09-04 04:20:31 - app.services.recommend_service - DEBUG - insert_db:100 - Single data keys: ['signal', 'ticker', 'hit_date', 'pattern', 'median_volume_1m', 'close_price', 'score']
2025-09-04 04:20:31 - app.services.recommend_service - DEBUG - insert_db:103 - Insert one result - inserted_id: 68b9138f09aeb9f21b11422c
2025-09-04 04:20:31 - app.services.recommend_service - INFO - insert_db:104 - Successfully inserted ticker data for VNM on 2024-02-01
2025-09-04 04:20:31 - app.api.routes - INFO - insert_recommends:100 - ✅ Successfully inserted 1 recommendation(s)
2025-09-04 04:20:31 - app.core.middleware - INFO - _log_response:91 - Response 1756959631761: 200 in 0.0037s
2025-09-04 04:20:31 - app.core.middleware - DEBUG - _log_response:98 - Response 1756959631761 headers: {'content-length': '4', 'content-type': 'application/json'}
2025-09-04 04:20:38 - app.core.middleware - INFO - _log_request:58 - Request 1756959638980: POST /api/v1/recommends from **********
2025-09-04 04:20:38 - app.core.middleware - DEBUG - _log_request:74 - Request 1756959638980 headers: {'host': 'localhost:8502', 'connection': 'keep-alive', 'content-length': '160', 'sec-ch-ua-platform': '"Linux"', 'user-agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json', 'sec-ch-ua': '"Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"', 'content-type': 'application/json', 'x-api-key': '[REDACTED]', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:8502', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:8502/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'vi,en;q=0.9,vi-VN;q=0.8,en-US;q=0.7', 'cookie': '[REDACTED]'}
2025-09-04 04:20:38 - app.core.middleware - DEBUG - _log_request:81 - Request 1756959638980 body: {
  "close_price": 85.5,
  "hit_date": "2024-02-01",
  "median_volume_1m": 150000,
  "pattern": "_BullDvg",
  "score": 4,
  "signal": "buy",
  "ticker": "VNM"
}
2025-09-04 04:20:38 - app.api.routes - INFO - insert_recommends:92 - 💾 POST /recommends - inserting 1 recommendation(s)
2025-09-04 04:20:38 - app.services.recommend_service - DEBUG - insert_db:83 - Starting insert_db operation with data type: <class 'app.models.recommend_model.StockRecommend'>
2025-09-04 04:20:38 - app.services.recommend_service - DEBUG - insert_db:98 - Inserting single stock recommendation for ticker: VNM
2025-09-04 04:20:38 - app.services.recommend_service - DEBUG - insert_db:100 - Single data keys: ['signal', 'ticker', 'hit_date', 'pattern', 'median_volume_1m', 'close_price', 'score']
2025-09-04 04:20:38 - app.services.recommend_service - DEBUG - insert_db:103 - Insert one result - inserted_id: 68b9139609aeb9f21b11422d
2025-09-04 04:20:38 - app.services.recommend_service - INFO - insert_db:104 - Successfully inserted ticker data for VNM on 2024-02-01
2025-09-04 04:20:38 - app.api.routes - INFO - insert_recommends:100 - ✅ Successfully inserted 1 recommendation(s)
2025-09-04 04:20:38 - app.core.middleware - INFO - _log_response:91 - Response 1756959638980: 200 in 0.0059s
2025-09-04 04:20:38 - app.core.middleware - DEBUG - _log_response:98 - Response 1756959638980 headers: {'content-length': '4', 'content-type': 'application/json'}
2025-09-04 04:20:53 - app.core.middleware - INFO - _log_request:58 - Request 1756959653621: POST /api/v1/recommends from **********
2025-09-04 04:20:53 - app.core.middleware - DEBUG - _log_request:74 - Request 1756959653621 headers: {'host': 'localhost:8502', 'connection': 'keep-alive', 'content-length': '160', 'sec-ch-ua-platform': '"Linux"', 'user-agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json', 'sec-ch-ua': '"Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"', 'content-type': 'application/json', 'x-api-key': '[REDACTED]', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:8502', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:8502/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'vi,en;q=0.9,vi-VN;q=0.8,en-US;q=0.7', 'cookie': '[REDACTED]'}
2025-09-04 04:20:53 - app.core.middleware - DEBUG - _log_request:81 - Request 1756959653621 body: {
  "close_price": 85.5,
  "hit_date": "2024-02-01",
  "median_volume_1m": 150000,
  "pattern": "_BullDvg",
  "score": 4,
  "signal": "buy",
  "ticker": "VNM"
}
2025-09-04 04:20:53 - app.api.routes - INFO - insert_recommends:92 - 💾 POST /recommends - inserting 1 recommendation(s)
2025-09-04 04:20:53 - app.services.recommend_service - DEBUG - insert_db:83 - Starting insert_db operation with data type: <class 'app.models.recommend_model.StockRecommend'>
2025-09-04 04:20:53 - app.services.recommend_service - DEBUG - insert_db:98 - Inserting single stock recommendation for ticker: VNM
2025-09-04 04:20:53 - app.services.recommend_service - DEBUG - insert_db:100 - Single data keys: ['signal', 'ticker', 'hit_date', 'pattern', 'median_volume_1m', 'close_price', 'score']
2025-09-04 04:20:53 - app.services.recommend_service - DEBUG - insert_db:103 - Insert one result - inserted_id: 68b913a509aeb9f21b11422e
2025-09-04 04:20:53 - app.services.recommend_service - INFO - insert_db:104 - Successfully inserted ticker data for VNM on 2024-02-01
2025-09-04 04:20:53 - app.api.routes - INFO - insert_recommends:100 - ✅ Successfully inserted 1 recommendation(s)
2025-09-04 04:20:53 - app.core.middleware - INFO - _log_response:91 - Response 1756959653621: 200 in 0.0041s
2025-09-04 04:20:53 - app.core.middleware - DEBUG - _log_response:98 - Response 1756959653621 headers: {'content-length': '4', 'content-type': 'application/json'}
2025-09-04 04:21:09 - app.core.middleware - INFO - _log_request:58 - Request 1756959669800: GET /api/v1/patterns from **********
2025-09-04 04:21:09 - app.core.middleware - DEBUG - _log_request:74 - Request 1756959669800 headers: {'host': 'localhost:8502', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"Linux"', 'user-agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json', 'sec-ch-ua': '"Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"', 'x-api-key': '[REDACTED]', 'sec-ch-ua-mobile': '?0', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:8502/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'vi,en;q=0.9,vi-VN;q=0.8,en-US;q=0.7', 'cookie': '[REDACTED]'}
2025-09-04 04:21:09 - app.api.routes - INFO - get_patterns:117 - 🎯 GET /patterns - retrieving all patterns
2025-09-04 04:21:09 - app.services.pattern_service - DEBUG - get_patterns:25 - Starting get_patterns operation
2025-09-04 04:21:09 - app.services.pattern_service - DEBUG - get_patterns:29 - Found 11 patterns in database
2025-09-04 04:21:09 - app.services.pattern_service - INFO - get_patterns:32 - Successfully retrieved 11 buy patterns
2025-09-04 04:21:09 - app.api.routes - INFO - get_patterns:124 - ✅ Successfully returned 11 patterns
2025-09-04 04:21:09 - app.core.middleware - INFO - _log_response:91 - Response 1756959669800: 200 in 0.0104s
2025-09-04 04:21:09 - app.core.middleware - DEBUG - _log_response:98 - Response 1756959669800 headers: {'content-length': '2775', 'content-type': 'application/json'}
2025-09-04 04:21:11 - app.core.middleware - INFO - _log_request:58 - Request 1756959671534: GET /api/v1/patterns from **********
2025-09-04 04:21:11 - app.core.middleware - DEBUG - _log_request:74 - Request 1756959671534 headers: {'host': 'localhost:8502', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"Linux"', 'user-agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json', 'sec-ch-ua': '"Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"', 'x-api-key': '[REDACTED]', 'sec-ch-ua-mobile': '?0', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:8502/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'vi,en;q=0.9,vi-VN;q=0.8,en-US;q=0.7', 'cookie': '[REDACTED]'}
2025-09-04 04:21:11 - app.api.routes - INFO - get_patterns:117 - 🎯 GET /patterns - retrieving all patterns
2025-09-04 04:21:11 - app.services.pattern_service - DEBUG - get_patterns:25 - Starting get_patterns operation
2025-09-04 04:21:11 - app.services.pattern_service - DEBUG - get_patterns:29 - Found 11 patterns in database
2025-09-04 04:21:11 - app.services.pattern_service - INFO - get_patterns:32 - Successfully retrieved 11 buy patterns
2025-09-04 04:21:11 - app.api.routes - INFO - get_patterns:124 - ✅ Successfully returned 11 patterns
2025-09-04 04:21:11 - app.core.middleware - INFO - _log_response:91 - Response 1756959671534: 200 in 0.0041s
2025-09-04 04:21:11 - app.core.middleware - DEBUG - _log_response:98 - Response 1756959671534 headers: {'content-length': '2775', 'content-type': 'application/json'}
2025-09-04 04:21:20 - app.core.middleware - INFO - _log_request:58 - Request 1756959680589: GET /api/v1/patterns from **********
2025-09-04 04:21:20 - app.core.middleware - DEBUG - _log_request:74 - Request 1756959680589 headers: {'host': 'localhost:8502', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"Linux"', 'user-agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json', 'sec-ch-ua': '"Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"', 'x-api-key': '[REDACTED]', 'sec-ch-ua-mobile': '?0', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:8502/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'vi,en;q=0.9,vi-VN;q=0.8,en-US;q=0.7', 'cookie': '[REDACTED]'}
2025-09-04 04:21:20 - app.api.routes - INFO - get_patterns:117 - 🎯 GET /patterns - retrieving all patterns
2025-09-04 04:21:20 - app.services.pattern_service - DEBUG - get_patterns:25 - Starting get_patterns operation
2025-09-04 04:21:20 - app.services.pattern_service - DEBUG - get_patterns:29 - Found 11 patterns in database
2025-09-04 04:21:20 - app.services.pattern_service - INFO - get_patterns:32 - Successfully retrieved 11 buy patterns
2025-09-04 04:21:20 - app.api.routes - INFO - get_patterns:124 - ✅ Successfully returned 11 patterns
2025-09-04 04:21:20 - app.core.middleware - INFO - _log_response:91 - Response 1756959680589: 200 in 0.0029s
2025-09-04 04:21:20 - app.core.middleware - DEBUG - _log_response:98 - Response 1756959680589 headers: {'content-length': '2775', 'content-type': 'application/json'}
2025-09-04 04:21:35 - app.core.middleware - INFO - _log_request:58 - Request 1756959695430: GET /api/v1/patterns/stategy from **********
2025-09-04 04:21:35 - app.core.middleware - DEBUG - _log_request:65 - Request 1756959695430 query params: {'strategy': 'ranking_point'}
2025-09-04 04:21:35 - app.core.middleware - DEBUG - _log_request:74 - Request 1756959695430 headers: {'host': 'localhost:8502', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"Linux"', 'user-agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json', 'sec-ch-ua': '"Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"', 'x-api-key': '[REDACTED]', 'sec-ch-ua-mobile': '?0', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:8502/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'vi,en;q=0.9,vi-VN;q=0.8,en-US;q=0.7', 'cookie': '[REDACTED]'}
2025-09-04 04:21:35 - app.api.routes - INFO - get_patterns_by_strategy:142 - 🎯 GET /patterns/strategy - strategy: ranking_point
2025-09-04 04:21:35 - app.services.pattern_service - DEBUG - get_patterns_by_strategy:70 - Starting get_patterns_by_strategy operation for strategy: ranking_point
2025-09-04 04:21:35 - app.services.pattern_service - DEBUG - get_patterns_by_strategy:74 - Found 11 raw patterns in database
2025-09-04 04:21:35 - app.services.pattern_service - DEBUG - get_patterns_by_strategy:81 - Pattern _BKMA200 has score 0.0 for strategy ranking_point
2025-09-04 04:21:35 - app.services.pattern_service - DEBUG - get_patterns_by_strategy:81 - Pattern _TrendingGrowth has score 0.0 for strategy ranking_point
2025-09-04 04:21:35 - app.services.pattern_service - DEBUG - get_patterns_by_strategy:81 - Pattern _TL3M has score 0.0 for strategy ranking_point
2025-09-04 04:21:35 - app.services.pattern_service - DEBUG - get_patterns_by_strategy:81 - Pattern _BuySupport has score 0.0 for strategy ranking_point
2025-09-04 04:21:35 - app.services.pattern_service - DEBUG - get_patterns_by_strategy:81 - Pattern _RSILow30 has score 0.0 for strategy ranking_point
2025-09-04 04:21:35 - app.services.pattern_service - DEBUG - get_patterns_by_strategy:81 - Pattern _UnderBV has score 0.0 for strategy ranking_point
2025-09-04 04:21:35 - app.services.pattern_service - DEBUG - get_patterns_by_strategy:81 - Pattern _SuperGrowth has score 0.0 for strategy ranking_point
2025-09-04 04:21:35 - app.services.pattern_service - DEBUG - get_patterns_by_strategy:81 - Pattern _SurpriseEarning has score 0.0 for strategy ranking_point
2025-09-04 04:21:35 - app.services.pattern_service - DEBUG - get_patterns_by_strategy:81 - Pattern _Conservative has score 0.0 for strategy ranking_point
2025-09-04 04:21:35 - app.services.pattern_service - DEBUG - get_patterns_by_strategy:81 - Pattern _BullDvg has score 0.0 for strategy ranking_point
2025-09-04 04:21:35 - app.services.pattern_service - DEBUG - get_patterns_by_strategy:81 - Pattern _VolMax1Y has score 0.0 for strategy ranking_point
2025-09-04 04:21:35 - app.services.pattern_service - INFO - get_patterns_by_strategy:84 - Successfully retrieved 11 patterns for strategy: ranking_point
2025-09-04 04:21:35 - app.api.routes - INFO - get_patterns_by_strategy:149 - ✅ Successfully returned 11 patterns for strategy ranking_point
2025-09-04 04:21:35 - app.core.middleware - INFO - _log_response:91 - Response 1756959695430: 200 in 0.0076s
2025-09-04 04:21:35 - app.core.middleware - DEBUG - _log_response:98 - Response 1756959695430 headers: {'content-length': '1851', 'content-type': 'application/json'}
2025-09-04 04:21:45 - app.core.middleware - INFO - _log_request:58 - Request 1756959705339: GET /api/v1/patterns/stategy from **********
2025-09-04 04:21:45 - app.core.middleware - DEBUG - _log_request:65 - Request 1756959705339 query params: {'strategy': 'ranking_point'}
2025-09-04 04:21:45 - app.core.middleware - DEBUG - _log_request:74 - Request 1756959705339 headers: {'host': 'localhost:8502', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"Linux"', 'user-agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json', 'sec-ch-ua': '"Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"', 'x-api-key': '[REDACTED]', 'sec-ch-ua-mobile': '?0', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:8502/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'vi,en;q=0.9,vi-VN;q=0.8,en-US;q=0.7', 'cookie': '[REDACTED]'}
2025-09-04 04:21:45 - app.api.routes - INFO - get_patterns_by_strategy:142 - 🎯 GET /patterns/strategy - strategy: ranking_point
2025-09-04 04:21:45 - app.services.pattern_service - DEBUG - get_patterns_by_strategy:70 - Starting get_patterns_by_strategy operation for strategy: ranking_point
2025-09-04 04:21:45 - app.services.pattern_service - DEBUG - get_patterns_by_strategy:74 - Found 11 raw patterns in database
2025-09-04 04:21:45 - app.services.pattern_service - DEBUG - get_patterns_by_strategy:81 - Pattern _BKMA200 has score 0.0 for strategy ranking_point
2025-09-04 04:21:45 - app.services.pattern_service - DEBUG - get_patterns_by_strategy:81 - Pattern _TrendingGrowth has score 0.0 for strategy ranking_point
2025-09-04 04:21:45 - app.services.pattern_service - DEBUG - get_patterns_by_strategy:81 - Pattern _TL3M has score 0.0 for strategy ranking_point
2025-09-04 04:21:45 - app.services.pattern_service - DEBUG - get_patterns_by_strategy:81 - Pattern _BuySupport has score 0.0 for strategy ranking_point
2025-09-04 04:21:45 - app.services.pattern_service - DEBUG - get_patterns_by_strategy:81 - Pattern _RSILow30 has score 0.0 for strategy ranking_point
2025-09-04 04:21:45 - app.services.pattern_service - DEBUG - get_patterns_by_strategy:81 - Pattern _UnderBV has score 0.0 for strategy ranking_point
2025-09-04 04:21:45 - app.services.pattern_service - DEBUG - get_patterns_by_strategy:81 - Pattern _SuperGrowth has score 0.0 for strategy ranking_point
2025-09-04 04:21:45 - app.services.pattern_service - DEBUG - get_patterns_by_strategy:81 - Pattern _SurpriseEarning has score 0.0 for strategy ranking_point
2025-09-04 04:21:45 - app.services.pattern_service - DEBUG - get_patterns_by_strategy:81 - Pattern _Conservative has score 0.0 for strategy ranking_point
2025-09-04 04:21:45 - app.services.pattern_service - DEBUG - get_patterns_by_strategy:81 - Pattern _BullDvg has score 0.0 for strategy ranking_point
2025-09-04 04:21:45 - app.services.pattern_service - DEBUG - get_patterns_by_strategy:81 - Pattern _VolMax1Y has score 0.0 for strategy ranking_point
2025-09-04 04:21:45 - app.services.pattern_service - INFO - get_patterns_by_strategy:84 - Successfully retrieved 11 patterns for strategy: ranking_point
2025-09-04 04:21:45 - app.api.routes - INFO - get_patterns_by_strategy:149 - ✅ Successfully returned 11 patterns for strategy ranking_point
2025-09-04 04:21:45 - app.core.middleware - INFO - _log_response:91 - Response 1756959705339: 200 in 0.0041s
2025-09-04 04:21:45 - app.core.middleware - DEBUG - _log_response:98 - Response 1756959705339 headers: {'content-length': '1851', 'content-type': 'application/json'}
2025-09-04 04:21:51 - app.core.middleware - INFO - _log_request:58 - Request 1756959711923: GET /api/v1/patterns/stategy from **********
2025-09-04 04:21:51 - app.core.middleware - DEBUG - _log_request:65 - Request 1756959711923 query params: {'strategy': 'ranking_point'}
2025-09-04 04:21:51 - app.core.middleware - DEBUG - _log_request:74 - Request 1756959711923 headers: {'host': 'localhost:8502', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"Linux"', 'user-agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json', 'sec-ch-ua': '"Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"', 'x-api-key': '[REDACTED]', 'sec-ch-ua-mobile': '?0', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:8502/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'vi,en;q=0.9,vi-VN;q=0.8,en-US;q=0.7', 'cookie': '[REDACTED]'}
2025-09-04 04:21:51 - app.api.routes - INFO - get_patterns_by_strategy:142 - 🎯 GET /patterns/strategy - strategy: ranking_point
2025-09-04 04:21:51 - app.services.pattern_service - DEBUG - get_patterns_by_strategy:70 - Starting get_patterns_by_strategy operation for strategy: ranking_point
2025-09-04 04:21:51 - app.services.pattern_service - DEBUG - get_patterns_by_strategy:74 - Found 11 raw patterns in database
2025-09-04 04:21:51 - app.services.pattern_service - DEBUG - get_patterns_by_strategy:81 - Pattern _BKMA200 has score 0.0 for strategy ranking_point
2025-09-04 04:21:51 - app.services.pattern_service - DEBUG - get_patterns_by_strategy:81 - Pattern _TrendingGrowth has score 0.0 for strategy ranking_point
2025-09-04 04:21:51 - app.services.pattern_service - DEBUG - get_patterns_by_strategy:81 - Pattern _TL3M has score 0.0 for strategy ranking_point
2025-09-04 04:21:51 - app.services.pattern_service - DEBUG - get_patterns_by_strategy:81 - Pattern _BuySupport has score 0.0 for strategy ranking_point
2025-09-04 04:21:51 - app.services.pattern_service - DEBUG - get_patterns_by_strategy:81 - Pattern _RSILow30 has score 0.0 for strategy ranking_point
2025-09-04 04:21:51 - app.services.pattern_service - DEBUG - get_patterns_by_strategy:81 - Pattern _UnderBV has score 0.0 for strategy ranking_point
2025-09-04 04:21:51 - app.services.pattern_service - DEBUG - get_patterns_by_strategy:81 - Pattern _SuperGrowth has score 0.0 for strategy ranking_point
2025-09-04 04:21:51 - app.services.pattern_service - DEBUG - get_patterns_by_strategy:81 - Pattern _SurpriseEarning has score 0.0 for strategy ranking_point
2025-09-04 04:21:51 - app.services.pattern_service - DEBUG - get_patterns_by_strategy:81 - Pattern _Conservative has score 0.0 for strategy ranking_point
2025-09-04 04:21:51 - app.services.pattern_service - DEBUG - get_patterns_by_strategy:81 - Pattern _BullDvg has score 0.0 for strategy ranking_point
2025-09-04 04:21:51 - app.services.pattern_service - DEBUG - get_patterns_by_strategy:81 - Pattern _VolMax1Y has score 0.0 for strategy ranking_point
2025-09-04 04:21:51 - app.services.pattern_service - INFO - get_patterns_by_strategy:84 - Successfully retrieved 11 patterns for strategy: ranking_point
2025-09-04 04:21:51 - app.api.routes - INFO - get_patterns_by_strategy:149 - ✅ Successfully returned 11 patterns for strategy ranking_point
2025-09-04 04:21:51 - app.core.middleware - INFO - _log_response:91 - Response 1756959711923: 200 in 0.0044s
2025-09-04 04:21:51 - app.core.middleware - DEBUG - _log_response:98 - Response 1756959711923 headers: {'content-length': '1851', 'content-type': 'application/json'}
2025-09-04 04:22:14 - app.core.middleware - INFO - _log_request:58 - Request 1756959734379: GET /api/v1/riskrating/HPG from **********
2025-09-04 04:22:14 - app.core.middleware - DEBUG - _log_request:74 - Request 1756959734379 headers: {'host': 'localhost:8502', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"Linux"', 'user-agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json', 'sec-ch-ua': '"Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"', 'x-api-key': '[REDACTED]', 'sec-ch-ua-mobile': '?0', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:8502/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'vi,en;q=0.9,vi-VN;q=0.8,en-US;q=0.7', 'cookie': '[REDACTED]'}
2025-09-04 04:22:14 - app.api.routes - INFO - get_riskrating_by_ticker:233 - ⚠️ GET /riskrating/HPG
2025-09-04 04:22:14 - app.services.risk_service - DEBUG - get_risk_by_ticker:48 - Starting get_risk_by_ticker operation for ticker: HPG
2025-09-04 04:22:14 - app.services.risk_service - DEBUG - get_risk_by_ticker:51 - Querying database with: {'ticker': 'HPG'}
2025-09-04 04:22:14 - app.services.risk_service - DEBUG - get_risk_by_ticker:55 - Found risk data with keys: ['_id', 'quarter', 'ticker', 'beta', 'deviation', 'downside_beta', 'downside_deviation', 'downside_risk_rating', 'risk_rating']
2025-09-04 04:22:14 - app.services.risk_service - INFO - get_risk_by_ticker:57 - Successfully retrieved risk rating for ticker: HPG
2025-09-04 04:22:14 - app.api.routes - INFO - get_riskrating_by_ticker:242 - ✅ Successfully returned risk rating for ticker HPG
2025-09-04 04:22:14 - app.core.middleware - INFO - _log_response:91 - Response 1756959734379: 200 in 0.0134s
2025-09-04 04:22:14 - app.core.middleware - DEBUG - _log_response:98 - Response 1756959734379 headers: {'content-length': '156', 'content-type': 'application/json'}
2025-09-04 04:22:43 - app.core.middleware - INFO - _log_request:58 - Request 1756959763799: GET /api/v1/riskrating from **********
2025-09-04 04:22:43 - app.core.middleware - DEBUG - _log_request:65 - Request 1756959763799 query params: {'quarter': '2024q4'}
2025-09-04 04:22:43 - app.core.middleware - DEBUG - _log_request:74 - Request 1756959763799 headers: {'host': 'localhost:8502', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"Linux"', 'user-agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json', 'sec-ch-ua': '"Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"', 'x-api-key': '[REDACTED]', 'sec-ch-ua-mobile': '?0', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:8502/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'vi,en;q=0.9,vi-VN;q=0.8,en-US;q=0.7', 'cookie': '[REDACTED]'}
2025-09-04 04:22:43 - app.api.routes - INFO - get_riskrating:260 - ⚠️ GET /riskrating - quarter: 2024q4
2025-09-04 04:22:43 - app.services.risk_service - DEBUG - get_risks:23 - Starting get_risks operation for quarter: 2024q4
2025-09-04 04:22:43 - app.services.risk_service - DEBUG - get_risks:27 - Filtering by quarter: 2024q4
2025-09-04 04:22:43 - app.services.risk_service - DEBUG - get_risks:32 - Querying database with: {'quarter': '2024q4'}
2025-09-04 04:22:43 - app.services.risk_service - DEBUG - get_risks:36 - Found 0 risk ratings
2025-09-04 04:22:43 - app.services.risk_service - INFO - get_risks:38 - Successfully retrieved 0 risk ratings for quarter: 2024q4
2025-09-04 04:22:43 - app.api.routes - WARNING - get_riskrating:264 - No risk ratings found for quarter: 2024q4
2025-09-04 04:22:43 - app.api.routes - WARNING - get_riskrating:270 - HTTP error in get_riskrating: Riskrating not found
2025-09-04 04:22:43 - app.core.middleware - INFO - _log_response:91 - Response 1756959763799: 404 in 0.0040s
2025-09-04 04:22:43 - app.core.middleware - DEBUG - _log_response:98 - Response 1756959763799 headers: {'content-length': '33', 'content-type': 'application/json'}
2025-09-04 04:22:54 - app.core.middleware - INFO - _log_request:58 - Request 1756959774406: GET /api/v1/riskrating from **********
2025-09-04 04:22:54 - app.core.middleware - DEBUG - _log_request:74 - Request 1756959774406 headers: {'host': 'localhost:8502', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"Linux"', 'user-agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json', 'sec-ch-ua': '"Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"', 'x-api-key': '[REDACTED]', 'sec-ch-ua-mobile': '?0', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:8502/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'vi,en;q=0.9,vi-VN;q=0.8,en-US;q=0.7', 'cookie': '[REDACTED]'}
2025-09-04 04:22:54 - app.api.routes - INFO - get_riskrating:260 - ⚠️ GET /riskrating - quarter: None
2025-09-04 04:22:54 - app.services.risk_service - DEBUG - get_risks:23 - Starting get_risks operation for quarter: None
2025-09-04 04:22:54 - app.services.risk_service - DEBUG - get_risks:30 - Getting all risk ratings (no quarter filter)
2025-09-04 04:22:54 - app.services.risk_service - DEBUG - get_risks:32 - Querying database with: {}
2025-09-04 04:22:54 - app.services.risk_service - DEBUG - get_risks:36 - Found 72 risk ratings
2025-09-04 04:22:54 - app.services.risk_service - INFO - get_risks:38 - Successfully retrieved 72 risk ratings for quarter: all
2025-09-04 04:22:54 - app.api.routes - INFO - get_riskrating:267 - ✅ Successfully returned 72 risk ratings for quarter None
2025-09-04 04:22:54 - app.core.middleware - INFO - _log_response:91 - Response 1756959774406: 200 in 0.0062s
2025-09-04 04:22:54 - app.core.middleware - DEBUG - _log_response:98 - Response 1756959774406 headers: {'content-length': '11053', 'content-type': 'application/json'}
2025-09-04 04:23:09 - app.core.middleware - INFO - _log_request:58 - Request 1756959789559: GET /api/v1/riskrating from **********
2025-09-04 04:23:09 - app.core.middleware - DEBUG - _log_request:74 - Request 1756959789559 headers: {'host': 'localhost:8502', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"Linux"', 'user-agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json', 'sec-ch-ua': '"Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"', 'x-api-key': '[REDACTED]', 'sec-ch-ua-mobile': '?0', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:8502/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'vi,en;q=0.9,vi-VN;q=0.8,en-US;q=0.7', 'cookie': '[REDACTED]'}
2025-09-04 04:23:09 - app.api.routes - INFO - get_riskrating:260 - ⚠️ GET /riskrating - quarter: None
2025-09-04 04:23:09 - app.services.risk_service - DEBUG - get_risks:23 - Starting get_risks operation for quarter: None
2025-09-04 04:23:09 - app.services.risk_service - DEBUG - get_risks:30 - Getting all risk ratings (no quarter filter)
2025-09-04 04:23:09 - app.services.risk_service - DEBUG - get_risks:32 - Querying database with: {}
2025-09-04 04:23:09 - app.services.risk_service - DEBUG - get_risks:36 - Found 72 risk ratings
2025-09-04 04:23:09 - app.services.risk_service - INFO - get_risks:38 - Successfully retrieved 72 risk ratings for quarter: all
2025-09-04 04:23:09 - app.api.routes - INFO - get_riskrating:267 - ✅ Successfully returned 72 risk ratings for quarter None
2025-09-04 04:23:09 - app.core.middleware - INFO - _log_response:91 - Response 1756959789559: 200 in 0.0055s
2025-09-04 04:23:09 - app.core.middleware - DEBUG - _log_response:98 - Response 1756959789559 headers: {'content-length': '11053', 'content-type': 'application/json'}
2025-09-04 04:25:48 - app.main - INFO - shutdown_event:55 - 🛑 Shutting down Stock Data API...
2025-09-04 04:25:48 - app.db.mongodb - INFO - close_mongo_connection:32 - 🔌 Closing MongoDB connection...
2025-09-04 04:25:48 - app.db.mongodb - INFO - close_mongo_connection:36 - ✅ MongoDB connection closed successfully
2025-09-04 04:25:48 - app.main - INFO - shutdown_event:58 - ✅ Successfully disconnected from MongoDB
2025-09-04 04:25:48 - app.main - INFO - shutdown_event:59 - 👋 API shutdown completed
