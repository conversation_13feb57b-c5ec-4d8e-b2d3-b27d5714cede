2025-09-04 10:40:26,350 - demo - DEBUG - 🔍 DEBUG: Detailed information for debugging
2025-09-04 10:40:26,350 - demo - INFO - ℹ️ INFO: General information about application flow
2025-09-04 10:40:26,350 - demo - WARNING - ⚠️ WARNING: Something unexpected happened
2025-09-04 10:40:26,350 - demo - ERROR - ❌ ERROR: An error occurred
2025-09-04 10:40:26,350 - demo - INFO - 📊 API Request processed
2025-09-04 10:40:26,350 - demo.service - INFO - 🚀 Starting recommendation service operation
2025-09-04 10:40:26,350 - demo.service - DEBUG - 🗄️ Executing database query
2025-09-04 10:40:26,350 - demo.service - DEBUG - Query: {'hit_date': '2024-01-15', 'signal': 'buy'}
2025-09-04 10:40:26,351 - demo.service - DEBUG - 📊 Processing 25 records from database
2025-09-04 10:40:26,351 - demo.service - DEBUG - Applying filters and transformations
2025-09-04 10:40:26,351 - demo.service - INFO - ✅ Service operation completed successfully
2025-09-04 10:40:26,351 - demo.service - INFO - Returned 25 recommendations to client
2025-09-04 10:40:26,351 - demo.api - INFO - 📡 GET /api/v1/recommends
2025-09-04 10:40:26,351 - demo.api - DEBUG - Parameters: {'date': '2024-01-15', 'signal': 'buy'}
2025-09-04 10:40:26,351 - demo.api - DEBUG - Processing request...
2025-09-04 10:40:26,351 - demo.api - INFO - ✅ Response: 200 OK, returned data
2025-09-04 10:40:26,351 - demo.api - INFO - 📡 POST /api/v1/patterns
2025-09-04 10:40:26,351 - demo.api - DEBUG - Parameters: {'pattern': 'bullish_engulfing'}
2025-09-04 10:40:26,351 - demo.api - DEBUG - Processing request...
2025-09-04 10:40:26,351 - demo.api - INFO - ✅ Response: 201 Created
2025-09-04 10:40:26,352 - demo.api - INFO - 📡 GET /api/v1/riskrating/VIC
2025-09-04 10:40:26,352 - demo.api - DEBUG - Processing request...
2025-09-04 10:40:26,352 - demo.api - INFO - ✅ Response: 200 OK, returned data
2025-09-04 10:40:26,352 - demo.error - WARNING - ⚠️ Validation error: Invalid date format: expected YYYY-MM-DD
2025-09-04 10:40:26,352 - demo.error - ERROR - ❌ Database connection error: Failed to connect to MongoDB
Traceback (most recent call last):
  File "/home/<USER>/dev/ta/kaffa_v2/api_service/demo_logging.py", line 120, in demo_error_logging
    raise ConnectionError("Failed to connect to MongoDB")
ConnectionError: Failed to connect to MongoDB
2025-09-04 10:40:26,352 - demo.error - ERROR - ❌ Unexpected error in calculation: division by zero
Traceback (most recent call last):
  File "/home/<USER>/dev/ta/kaffa_v2/api_service/demo_logging.py", line 126, in demo_error_logging
    result = 10 / 0
ZeroDivisionError: division by zero
2025-09-04 10:40:26,353 - demo.performance - DEBUG - ⏱️ Starting: Database query
2025-09-04 10:40:26,453 - demo.performance - DEBUG - ✅ Completed: Database query in 0.100s
2025-09-04 10:40:26,453 - demo.performance - DEBUG - ⏱️ Starting: Data processing
2025-09-04 10:40:26,504 - demo.performance - DEBUG - ✅ Completed: Data processing in 0.050s
2025-09-04 10:40:26,504 - demo.performance - DEBUG - ⏱️ Starting: Response serialization
2025-09-04 10:40:26,525 - demo.performance - DEBUG - ✅ Completed: Response serialization in 0.020s
2025-09-04 10:40:26,525 - demo.performance - INFO - 📊 Total request processing time: 0.172s
2025-09-04 10:40:26,526 - demo.business - INFO - 🎯 Starting stock recommendation algorithm
2025-09-04 10:40:26,526 - demo.business - DEBUG - 📥 Input: 500 stocks, 20 technical indicators
2025-09-04 10:40:26,526 - demo.business - DEBUG - Filters: market_cap > 1B, volume > 1M
2025-09-04 10:40:26,526 - demo.business - DEBUG - 🔍 Step 1: Filtering stocks by criteria
2025-09-04 10:40:26,527 - demo.business - DEBUG - Result: 150 stocks passed initial filters
2025-09-04 10:40:26,527 - demo.business - DEBUG - 🔍 Step 2: Calculating technical scores
2025-09-04 10:40:26,527 - demo.business - DEBUG - Applied 20 technical indicators
2025-09-04 10:40:26,528 - demo.business - DEBUG - 🔍 Step 3: Risk assessment
2025-09-04 10:40:26,528 - demo.business - DEBUG - Applied risk scoring model
2025-09-04 10:40:26,528 - demo.business - DEBUG - 🔍 Step 4: Final ranking
2025-09-04 10:40:26,528 - demo.business - DEBUG - Sorted by composite score
2025-09-04 10:40:26,529 - demo.business - INFO - 📤 Output: Top 25 stock recommendations
2025-09-04 10:40:26,529 - demo.business - DEBUG - Score range: 85-95 (out of 100)
2025-09-04 10:40:26,529 - demo.security - INFO - 🔐 Authentication attempt
2025-09-04 10:40:26,530 - demo.security - DEBUG - API key validation: SUCCESS
2025-09-04 10:40:26,530 - demo.security - INFO - ✅ User authenticated successfully
2025-09-04 10:40:26,530 - demo.security - DEBUG - 🛡️ Checking user permissions for endpoint: /api/v1/patterns
2025-09-04 10:40:26,530 - demo.security - INFO - ✅ User authorized for requested operation
2025-09-04 10:40:26,531 - demo.security - INFO - 📊 Data access: patterns collection
2025-09-04 10:40:26,531 - demo.security - DEBUG - Query: user requested all patterns
2025-09-04 10:40:26,531 - demo.security - INFO - ✅ Data access granted, returned 15 patterns
2025-09-04 10:40:26,531 - demo.security - WARNING - ⚠️ Rate limit warning: 90 requests in last minute
2025-09-04 10:40:26,531 - demo.security - WARNING - ⚠️ Unusual access pattern detected for user
