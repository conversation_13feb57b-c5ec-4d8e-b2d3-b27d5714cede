#!/usr/bin/env python3
"""
Test script để kiểm tra logging configuration
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.core.logging import setup_logging, get_logger
from app.core.middleware import DatabaseLoggingMiddleware
import asyncio

def test_basic_logging():
    """Test basic logging functionality"""
    print("🧪 Testing basic logging functionality...")
    
    # Setup logging
    setup_logging()
    logger = get_logger(__name__)
    
    # Test different log levels
    logger.debug("🔍 This is a DEBUG message")
    logger.info("ℹ️ This is an INFO message")
    logger.warning("⚠️ This is a WARNING message")
    logger.error("❌ This is an ERROR message")
    
    # Test logging with extra data
    logger.info("📊 Processing data", extra={
        'user_id': 123,
        'action': 'test_logging',
        'data_count': 42
    })
    
    # Test exception logging
    try:
        raise ValueError("This is a test exception")
    except Exception as e:
        logger.error("Exception occurred during testing", exc_info=True)
    
    print("✅ Basic logging test completed")

def test_database_logging():
    """Test database logging middleware"""
    print("🧪 Testing database logging middleware...")
    
    logger = get_logger(__name__)
    
    # Test database operation logging
    with DatabaseLoggingMiddleware("find", "test_collection", {"ticker": "VIC"}):
        # Simulate database operation
        import time
        time.sleep(0.1)
        logger.debug("Simulated database operation completed")
    
    # Test with result logging
    with DatabaseLoggingMiddleware("insert", "test_collection") as db_log:
        # Simulate database operation
        time.sleep(0.05)
        db_log.log_result(result_count=5, result_info="5 records inserted successfully")
    
    print("✅ Database logging test completed")

async def test_service_logging():
    """Test service-level logging"""
    print("🧪 Testing service-level logging...")
    
    logger = get_logger(__name__)
    
    # Simulate service operations
    logger.info("🚀 Starting service operation")
    
    # Simulate API call
    logger.info("📡 API call: GET /test-endpoint")
    logger.debug("Request parameters: {'param1': 'value1', 'param2': 'value2'}")
    
    # Simulate processing
    await asyncio.sleep(0.1)
    logger.debug("Processing completed")
    
    # Simulate response
    logger.info("✅ API response: 200 OK, returned 10 records")
    
    print("✅ Service logging test completed")

def test_log_file_creation():
    """Test if log files are created properly"""
    print("🧪 Testing log file creation...")
    
    from app.core.config import settings
    
    # Check if log directory exists
    log_dir = os.path.dirname(settings.LOG_FILE)
    if os.path.exists(log_dir):
        print(f"✅ Log directory exists: {log_dir}")
    else:
        print(f"❌ Log directory missing: {log_dir}")
    
    # Check if log file exists
    if os.path.exists(settings.LOG_FILE):
        print(f"✅ Log file exists: {settings.LOG_FILE}")
        
        # Check file size
        file_size = os.path.getsize(settings.LOG_FILE)
        print(f"📊 Log file size: {file_size} bytes")
        
        # Show last few lines
        try:
            with open(settings.LOG_FILE, 'r') as f:
                lines = f.readlines()
                if lines:
                    print("📝 Last few log entries:")
                    for line in lines[-3:]:
                        print(f"   {line.strip()}")
        except Exception as e:
            print(f"❌ Error reading log file: {e}")
    else:
        print(f"❌ Log file missing: {settings.LOG_FILE}")
    
    # Check error log file
    error_log_file = settings.LOG_FILE.replace('.log', '_errors.log')
    if os.path.exists(error_log_file):
        print(f"✅ Error log file exists: {error_log_file}")
    else:
        print(f"ℹ️ Error log file not created yet: {error_log_file}")

def test_logger_hierarchy():
    """Test logger hierarchy and levels"""
    print("🧪 Testing logger hierarchy...")
    
    # Test different loggers
    main_logger = get_logger("main")
    service_logger = get_logger("app.services.test")
    api_logger = get_logger("app.api.test")
    
    main_logger.info("Main logger test")
    service_logger.info("Service logger test")
    api_logger.info("API logger test")
    
    # Test logger levels
    import logging
    print(f"Root logger level: {logging.getLogger().level}")
    print(f"Main logger level: {main_logger.level}")
    print(f"Service logger level: {service_logger.level}")
    print(f"API logger level: {api_logger.level}")
    
    print("✅ Logger hierarchy test completed")

async def main():
    """Run all logging tests"""
    print("🚀 Starting comprehensive logging tests...")
    print("=" * 60)
    
    try:
        test_basic_logging()
        print()
        
        test_database_logging()
        print()
        
        await test_service_logging()
        print()
        
        test_log_file_creation()
        print()
        
        test_logger_hierarchy()
        print()
        
        print("=" * 60)
        print("🎉 All logging tests completed successfully!")
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
