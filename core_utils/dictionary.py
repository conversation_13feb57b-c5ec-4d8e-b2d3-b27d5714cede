DICTIONARY = {
    'ticker': 'Stock ticker e.g. (ticker=="HPG")',
    'time': 'YYYY-MM-DD for filtering within a specific period e.g. (time>="2019-01-01")&(time<="2020-01-01")',
    'quarter': 'quarter e.g. (quarter=="2019Q4")',
    'Inflation_5': 'The inflation rate is 5 percent a year.',
    'Inflation_6': 'The inflation rate is 6 percent a year.',
    'Inflation_7': 'The inflation rate is 7 percent a year.',
    'Inflation_8': 'The inflation rate is 8 percent a year.',
    'Inflation_9': 'The inflation rate is 9 percent a year.',
    'Inflation_10': 'The inflation rate is 10 percent a year.',
    'D_RSI': '0..1 RSI daily',
    'D_RSI_T1W': '0..1 RSI daily in previous 1W',
    'D_RSI_T1': '0..1 RSI daily in previous 1D',
    'D_RSI_MaxT3': '0..1 RSI max over the last 3D',
    'D_RSI_MaxT3_Close': 'Close at RSI max over the last 3D',
    'D_RSI_MaxT3_Volume': 'Volume at RSI max over the last 3D',
    'D_RSI_MaxT3_MFI': 'MFI at RSI max over the last 3D',
    'D_RSI_MaxT3_CMF': 'CMF at RSI max over the last 3D',
    'D_RSI_MaxT3_MACD': 'MACD at RSI max over the last 3D',
    'D_RSI_MaxT3_MACDdiff': 'MACDdiff at RSI max over the last 3D',
    'D_RSI_Max1W': '0..1 RSI max over the last 1W',
    'D_RSI_Max1W_Close': 'Close at RSI max over the last 1W',
    'D_RSI_Max1W_Volume': 'Volume at RSI max over the last 1W',
    'D_RSI_Max1W_MFI': 'MFI at RSI max over the last 1W',
    'D_RSI_Max1W_CMF': 'CMF at RSI max over the last 1W',
    'D_RSI_Max1W_MACD': 'MACD at RSI max over the last 1W',
    'D_RSI_Max1W_MACDdiff': 'MACDdiff at RSI max over the last 1W',
    'D_RSI_Max1M': '0..1 RSI max over the last 1M',
    'D_RSI_Max1M_Close': 'Close at RSI max over the last 1M',
    'D_RSI_Max1M_Volume': 'Volume at RSI max over the last 1M',
    'D_RSI_Max1M_MFI': 'MFI at RSI max over the last 1M',
    'D_RSI_Max1M_CMF': 'CMF at RSI max over the last 1M',
    'D_RSI_Max1M_MACD': 'MACD at RSI max over the last 1M',
    'D_RSI_Max1M_MACDdiff': 'MACDdiff at RSI max over the last 1M',
    'D_RSI_Max3M': '0..1 RSI max over the last 3M',
    'D_RSI_Max3M_Close': 'Close at RSI max over the last 3M',
    'D_RSI_Max3M_Volume': 'Volume at RSI max over the last 3M',
    'D_RSI_Max3M_MFI': 'MFI at RSI max over the last 3M',
    'D_RSI_Max3M_CMF': 'CMF at RSI max over the last 3M',
    'D_RSI_Max3M_MACD': 'MACD at RSI max over the last 3M',
    'D_RSI_Max3M_MACDdiff': 'MACDdiff at RSI max over the last 3M',
    'D_RSI_MinT3': '0..1 RSI min over the last 3D',
    'D_RSI_MinT3_Close': 'Close at RSI min over the last 3D',
    'D_RSI_MinT3_Volume': 'Volume at RSI min over the last 3D',
    'D_RSI_MinT3_MFI': 'MFI at RSI min over the last 3D',
    'D_RSI_MinT3_CMF': 'CMF at RSI min over the last 3D',
    'D_RSI_MinT3_MACD': 'MACD at RSI min over the last 3D',
    'D_RSI_MinT3_MACDdiff': 'MACDdiff at RSI min over the last 3D',
    'D_RSI_Min1W': '0..1 RSI min over the last 1W',
    'D_RSI_Min1W_Close': 'Close at RSI min over the last 1W',
    'D_RSI_Min1W_Volume': 'Volume at RSI min over the last 1W',
    'D_RSI_Min1W_MFI': 'MFI at RSI min over the last 1W',
    'D_RSI_Min1W_CMF': 'CMF at RSI min over the last 1W',
    'D_RSI_Min1W_MACD': 'MACD at RSI min over the last 1W',
    'D_RSI_Min1W_MACDdiff': 'MACDdiff at RSI min over the last 1W',
    'D_RSI_Min1M': '0..1 RSI min over the last 1M',
    'D_RSI_Min1M_Close': 'Close at RSI min over the last 1M',
    'D_RSI_Min1M_Volume': 'Volume at RSI min over the last 1M',
    'D_RSI_Min1M_MFI': 'MFI at RSI min over the last 1M',
    'D_RSI_Min1M_CMF': 'CMF at RSI min over the last 1M',
    'D_RSI_Min1M_MACD': 'MACD at RSI min over the last 1M',
    'D_RSI_Min1M_MACDdiff': 'MACDdiff at RSI min over the last 1M',
    'D_RSI_Min3M': '0..1 RSI min over the last 3M',
    'D_RSI_Min3M_Close': 'Close at RSI min over the last 3M',
    'D_RSI_Min3M_Volume': 'Volume at RSI min over the last 3M',
    'D_RSI_Min3M_MFI': 'MFI at RSI min over the last 3M',
    'D_RSI_Min3M_CMF': 'CMF at RSI min over the last 3M',
    'D_RSI_Min3M_MACD': 'MACD at RSI min over the last 3M',
    'D_RSI_Min3M_MACDdiff': 'MACDdiff at RSI min over the last 3M',
    'D_MFI': '0..1 MFI daily',
    'D_MFI_T1W': '0..1 MFI daily in previous 1W',
    'D_MFI_T1': '0..1 MFI daily in previous 1D',
    'D_RawMFI': 'RawMFI daily - Positive money divide negative money in 14 periods',
    'D_RawMFI_T1W': 'RawMFI daily - Positive money divide negative money in 14 periods in previous 1W',
    'D_CMF': '0..1 CMF daily',
    'D_CMF_T1W': '0..1 CMF daily in previous 1W',
    'D_CMF_T1': '0..1 CMF daily in previous 1D',
    'D_ADX': '0..1 ADX daily',
    'D_ATR': '0..1 ATR daily',
    'D_MACD': 'MACD daily',
    'D_MACD_T1': 'MACD daily in previous 1D',
    'D_MACD_T1W': 'MACD daily in previous 1W',
    'D_MACDsign': 'MACDsign daily',
    'D_MACDsign_T1W': 'MACDsign daily in previous 1W',
    'D_MACDdiff': 'MACDdiff daily = MACD - MACDsign',
    'D_MACDdiff_T1W': 'MACDdiff daily in previous 1W ',
    'STOK14': 'Stochastic %K 14 days',
    'STOD14': 'Stochastic %D 14 days',
    'C_H1W': '0..1 Ratio of Current Price / Highest price in the last 1W',
    'C_H1M': '0..1 Ratio of Current Price / Highest price in the last 1M',
    'C_H3M': '0..1 Ratio of Current Price / Highest price in the last 3M',
    'C_H6M': '0..1 Ratio of Current Price / Highest price in the last 6M',
    'C_H1Y': '0..1 Ratio of Current Price / Highest price in the last 1Y',
    'C_H2Y': '0..1 Ratio of Current Price / Highest price in the last 2Y',
    'C_L1W': '0..1 Ratio of Current Price / Lowest price in the last 1W',
    'C_L1M': '0..1 Ratio of Current Price / Lowest price in the last 1M',
    'C_L3M': '0..1 Ratio of Current Price / Lowest price in the last 3M',
    'C_L1Y': '0..1 Ratio of Current Price / Lowest price in the last 1Y',
    'C_L2Y': '0..1 Ratio of Current Price / Lowest price in the last 2Y',
    'Close_T1W': '0..inf Close price in the previous 1W',
    'Close_T1M': '0..inf Close price in the previous 1M',
    'Close_1M': '0..inf Close price in the next 1M',
    'D_CMB': '-1..2 CMB index daily',
    'D_CMB_Fast': '-1..2 CMB average 13',
    'D_CMB_Slow': '-1..2 CMB average 33',
    'D_CMB_XFast': '0 .. M # indicate how many periods since the CMB crossed CMB Fast (0=strong signal)',
    'D_CMB_XSlow': '0 .. M # indicate how many periods since the CMB crossed CMB Slow (0=strong signal)',
    'D_CMB_Peak': '-1, 0, 1 # indicate if weekly CMB has just got top (1) or bottom (-1) in the previous period',
    'D_CMB_Peak_T1': '-1, 0, 1 # indicate if weekly CMB has just got top (1) or bottom (-1) in the previous period in the previous 1D',
    'D_CMB_Step': '-inf .. inf # CMB divergence - CMB delta between two consecutive periods. Step Negative/Zero/Positive corresponding to negative/No/positive divergence. Step=0 no divergence.',
    'D_CMB_LEN': '0 .. M # CMB divergence - original length between two peaks',
    'D_CMB_LAG': '0 .. M # CMB divergence - delay',
    'W_CMB': 'CMB index weekly',
    'W_CMB_Fast': 'CMB average 13',
    'W_CMB_Slow': 'CMB average 33',
    'W_CMB_Peak': '-1, 0, 1 # indicate if weekly CMB has just got top (1) or bottom (-1) in the previous period',
    'W_CMB_Peak_T1': '-1, 0, 1 # indicate if weekly CMB has just got top (1) or bottom (-1) in the previous period in the previous 1D',
    'W_CMB_Step': '-inf .. inf # CMB divergence - CMB delta between two consecutive periods. Step Negative/Zero/Positive corresponding to negative/No/positive divergence. Step=0 no divergence.',
    'W_CMB_LEN': '0 .. M # CMB divergence - length of latest pattern',
    'W_CMB_LAG': '0 .. M # CMB divergence - number of periods since the latest pattern',
    'M_CMB': 'CMB index monthly',
    'M_CMB_Peak_T1': '-1 .. 2 # Value at the latest top/bottom',
    'M_CMB_Peak_T2': '-1 .. 2 # Value at the previous top/bottom',
    'M_CMB_Step': '-inf .. inf # CMB divergence - CMB delta between two consecutive periods. Step Negative/Zero/Positive corresponding to negative/No/positive divergence. Step=0 no divergence.',
    'M_CMB_LEN': '0 .. M # CMB positive divergence - length of latest pattern',
    'M_CMB_LAG': '0 .. M # CMB positive divergence - number of periods since the latest pattern',
    # 'P1W': '-100%..X00% Average profit of the stock in the next 1W Cutloss at -X%',
    # 'P1M': '-100%..X00% Average profit of the stock in the next 1M Cutloss at -X%',
    # 'P3M': '-100%..X00% Average profit of the stock in the next 3M Cutloss at -X%',
    # 'P1Y': '-100%..X00% Average profit of the stock in the next 1Y Cutloss at -X%',
    # 'P2Y': '-100%..X00% Average profit of the stock in the next 2Y Cutloss at -X%',
    # 'Win X% Y': '0..100% Percentage of Win in the next period Y. Win=1 if High value>X% and cutloss does not happen within period Y',
    # 'Cutloss X% Y': '0..100% Percentage of Cutloss X% in within period Y.',
    'L1W': '0..1 Lowest price in the next 1W compared to the current close price. L1W=0.9 means the lowest price in the next 1W is 90% of the current price',
    'L2W': '0..1 Lowest price in the next 2W compared to the current close price. L2W=0.9 means the lowest price in the next 2W is 90% of the current price',
    'L3W': '0..1 Lowest price in the next 3W compared to the current close price. L3W=0.9 means the lowest price in the next 3W is 90% of the current price',
    'L1M': '0..1 Lowest price in the next 1M compared to the current close price. L1M=0.9 means the lowest price in the next 1M is 90% of the current price',
    'L2M': '0..1 Lowest price in the next 2M compared to the current close price. L2M=0.9 means the lowest price in the next 2M is 90% of the current price',
    'L3M': '0..1 Lowest price in the next 3M compared to the current close price. L3M=0.9 means the lowest price in the next 3M is 90% of the current price',
    'L6M': '0..1 Lowest price in the next 6M compared to the current close price. L3M=0.9 means the lowest price in the next 3M is 90% of the current price',
    'L1Y': '0..1 Lowest price in the next 1Y compared to the current close price. L1Y=0.9 means the lowest price in the next 1Y is 90% of the current price',
    'L2Y': '0..1 Lowest price in the next 2Y compared to the current close price. L2Y=0.9 means the lowest price in the next 2Y is 90% of the current price',
    'H1W': '1..n Highest price in the next 1W compared to the current close price. H1W=1.1 means the highest price in the next 1W is 100% of the current price',
    'H2W': '1..n Highest price in the next 2W compared to the current close price. H2W=1.1 means the highest price in the next 2W is 100% of the current price',
    'H3W': '1..n Highest price in the next 3W compared to the current close price. H3W=1.1 means the highest price in the next 3W is 100% of the current price',
    'H1M': '1..n Highest price in the next 1M compared to the current close price. H1M=1.1 means the highest price in the next 1M is 100% of the current price',
    'H2M': '1..n Highest price in the next 2M compared to the current close price. H2M=1.1 means the highest price in the next 2M is 100% of the current price',
    'H3M': '1..n Highest price in the next 3M compared to the current close price. H3M=1.1 means the highest price in the next 3M is 100% of the current price',
    'H6M': '1..n Highest price in the next 6M compared to the current close price. H3M=1.1 means the highest price in the next 3M is 100% of the current price',
    'H1Y': '1..n Highest price in the next 1Y compared to the current close price. H1Y=1.1 means the highest price in the next 1Y is 100% of the current price',
    'H2Y': '1..n Highest price in the next 2Y compared to the current close price. H2Y=1.1 means the highest price in the next 2Y is 100% of the current price',
    'C1W': '0..1 Price in the next 1W compared to the current close price. C1W=0.9 means the price in the next 1W is 90% of the current price',
    'C2W': '0..1 Price in the next 2W compared to the current close price. C2W=0.9 means the price in the next 2W is 90% of the current price',
    'C3W': '0..1 Price in the next 3W compared to the current close price. C3W=0.9 means the price in the next 3W is 90% of the current price',
    'C1M': '0..1 Price in the next 1M compared to the current close price. C1M=0.9 means the price in the next 1M is 90% of the current price',
    'C2M': '0..1 Price in the next 2M compared to the current close price. C2M=0.9 means the price in the next 2M is 90% of the current price',
    'C3M': '0..1 Price in the next 3M compared to the current close price. C3M=0.9 means the price in the next 3M is 90% of the current price',
    'C6M': '0..1 Price in the next 6M compared to the current close price. C6M=0.9 means the price in the next 3M is 90% of the current price',
    'C1Y': '0..1 Price in the next 1Y compared to the current close price. C1Y=0.9 means the price in the next 1Y is 90% of the current price',
    'C2Y': '0..1 Price in the next 2Y compared to the current close price. C2Y=0.9 means the price in the next 2Y is 90% of the current price',
    'O1W': '0..1 Open price in the next 1W compared to the current close price. O1W=0.9 means the open price in the next 1W is 90% of the current price',
    'O2W': '0..1 Open price in the next 2W compared to the current close price. O2W=0.9 means the open price in the next 2W is 90% of the current price',
    'O3W': '0..1 Open price in the next 3W compared to the current close price. O3W=0.9 means the open price in the next 3W is 90% of the current price',
    'O1M': '0..1 Open price in the next 1M compared to the current close price. O1M=0.9 means the open price in the next 1M is 90% of the current price',
    'O2M': '0..1 Open price in the next 2M compared to the current close price. O2M=0.9 means the open price in the next 2M is 90% of the current price',
    'O3M': '0..1 Open price in the next 3M compared to the current close price. O3M=0.9 means the open price in the next 3M is 90% of the current price',
    'O6M': '0..1 Open price in the next 6M compared to the current close price. O6M=0.9 means the open price in the next 3M is 90% of the current price',
    'O1Y': '0..1 Open price in the next 1Y compared to the current close price. O1Y=0.9 means the open price in the next 1Y is 90% of the current price',
    'O2Y': '0..1 Open price in the next 2Y compared to the current close price. O2Y=0.9 means the open price in the next 2Y is 90% of the current price',
    'Close': 'Close price',
    'Open': 'Open price',
    'High': 'High price',
    'Low': 'Low price',
    'Price': 'Unadjusted closing price',
    'Close_T1': 'Yesterday close price',
    'Open_1D': 'Open price in the next 1D',
    'Volume': 'Measure of how much a given stock has traded in a day',
    'Trading_Session': 'Matched transaction value of all transactions in a session',
    'Volume_Session': 'Matched transaction volume of all transactions in a session',
    'VNINDEX': '0..inf VNINDEX value',
    'VNINDEX_MA200': 'MA200 VNINDEX value',
    'VNINDEX_PE': '0..inf VNINDEX P/E ratio',
    'VNINDEX_PE_MA2Y': '2-years moving average of VNINDEX P/E ratio',
    'VNINDEX_PE_MA4Y': '4-years moving average of VNINDEX P/E ratio',
    'VNINDEX_PE_MA5Y': '5-years moving average of VNINDEX P/E ratio',
    'VNINDEX_LNST': '0..inf VNINDEX LNST',
    'VNINDEX_T1W': '0..inf VNINDEX in the previous 1W',
    'VNINDEX_T2W': '0..inf VNINDEX in the previous 2W',
    'VNINDEX_T1M': '0..inf VNINDEX in the previous 1M',
    'VNINDEX_T3M': '0..inf VNINDEX in the previous 3M',
    'VNINDEX_RSI': 'RSI daily value of VNINDEX',
    # 'VNINDEX_RSI_T3': 'RSI daily value of VNINDEX in the previous 3D',
    'VNINDEX_RSI_T1W': 'RSI daily value of VNINDEX in the previous 1W',
    'VNINDEX_MFI': 'MFI daily value of VNINDEX',
    # 'VNINDEX_MFI_T3': 'MFI daily value of VNINDEX in the previous 3D',
    'VNINDEX_MFI_T1W': 'MFI daily value of VNINDEX in the previous 1W',
    'VNINDEX_RawMFI': 'Raw MFI daily value of VNINDEX',
    # 'VNINDEX_RawMFI_T3': 'Raw MFI daily value of VNINDEX in the previous 3D',
    'VNINDEX_RawMFI_T1W': 'Raw MFI daily value of VNINDEX in the previous 1W',
    'VNINDEX_CMF': 'CMF daily value of VNINDEX',
    # 'VNINDEX_CMF_T3': 'CMF daily value of VNINDEX in the previous 3D',
    'VNINDEX_CMF_T1W': 'CMF daily value of VNINDEX in the previous 1W',
    'VNINDEX_MACD': 'MACD value of VNINDEX',
    # 'VNINDEX_MACD_T3': 'MACD value of VNINDEX in the previous 3D',
    'VNINDEX_MACD_T1W': 'MACD value of VNINDEX in the previous 1W',
    'VNINDEX_MACDsign': 'MACDsign value of VNINDEX',
    # 'VNINDEX_MACDsign_T3': 'MACDsign value of VNINDEX in the previous 3D',
    'VNINDEX_MACDsign_T1W': 'MACDsign value of VNINDEX in the previous 1W',
    'VNINDEX_MACDdiff': 'MACDdiffvalue of VNINDEX',
    # 'VNINDEX_MACDdiff_T3': 'MACDdiff value of VNINDEX in the previous 3D',
    'VNINDEX_MACDdiff_T1W': 'MACDdiff value of VNINDEX in the previous 1W',
    # 'VNINDEX_Volume_Max5Y_ID': 'ID at max Volume of VNINDEX in the last 5 years',
    # 'VNINDEX_Volume_Max5Y_Close': 'Hight at Volume of VNINDEX in top Volume in last 5 years',
    # 'VNINDEX_Volume_Max3Y_ID': 'ID at max Volume of VNINDEX in the last 3 years',
    # 'VNINDEX_Volume_Max3Y_Close': 'Hight at Volume of VNINDEX in top Volume in last 3 years',
    # 'VNINDEX_RSI_MaxT3': '0..1 VNINDEX RSI max over the last 3D',
    # 'VNINDEX_RSI_MaxT3_Close': 'VNINDEX Close at RSI max over the last 3D',
    # 'VNINDEX_RSI_MaxT3_MFI': 'VNINDEX MFI at RSI max over the last 3D',
    # 'VNINDEX_RSI_MaxT3_CMF': 'VNINDEX CMF at RSI max over the last 3D',
    # 'VNINDEX_RSI_MaxT3_MACD': 'VNINDEX MACD at RSI max over the last 3D',
    'VNINDEX_RSI_Max1W': '0..1 VNINDEX RSI max over the last 1W',
    'VNINDEX_RSI_Max1W_Close': 'VNINDEX Close at RSI max over the last 1W',
    'VNINDEX_RSI_Max1W_Volume': 'VNINDEX Volume at RSI max over the last 1W',
    'VNINDEX_RSI_Max1W_MFI': 'VNINDEX MFI at RSI max over the last 1W',
    'VNINDEX_RSI_Max1W_CMF': 'VNINDEX CMF at RSI max over the last 1W',
    'VNINDEX_RSI_Max1W_MACD': 'VNINDEX MACD at RSI max over the last 1W',
    # 'VNINDEX_RSI_Max1M': '0..1 VNINDEX RSI max over the last 1M',
    # 'VNINDEX_RSI_Max1M_Close': 'VNINDEX Close at RSI max over the last 1M',
    # 'VNINDEX_RSI_Max1M_MFI': 'VNINDEX MFI at RSI max over the last 1M',
    # 'VNINDEX_RSI_Max1M_CMF': 'VNINDEX CMF at RSI max over the last 1M',
    # 'VNINDEX_RSI_Max1M_MACD': 'VNINDEX MACD at RSI max over the last 1M',
    'VNINDEX_RSI_Max3M': '0..1 VNINDEX RSI max over the last 3M',
    'VNINDEX_RSI_Max3M_Close': 'VNINDEX Close at RSI max over the last 3M',
    'VNINDEX_RSI_Max3M_Volume': 'VNINDEX Volume at RSI max over the last 3M',
    'VNINDEX_RSI_Max3M_MFI': 'VNINDEX MFI at RSI max over the last 3M',
    'VNINDEX_RSI_Max3M_CMF': 'VNINDEX CMF at RSI max over the last 3M',
    'VNINDEX_RSI_Max3M_MACD': 'VNINDEX MACD at RSI max over the last 3M',
    'VNINDEX_RSI_MinT3': '0..1 VNINDEX RSI min over the last 3D',
    'VNINDEX_RSI_MinT3_Close': 'VNINDEX Close at RSI min over the last 3D',
    'VNINDEX_RSI_MinT3_Volume': 'VNINDEX Volume at RSI min over the last 3D',
    'VNINDEX_RSI_MinT3_MFI': 'VNINDEX MFI at RSI min over the last 3D',
    'VNINDEX_RSI_MinT3_CMF': 'VNINDEX CMF at RSI min over the last 3D',
    'VNINDEX_RSI_MinT3_MACD': 'VNINDEX MACD at RSI min over the last 3D',
    # 'VNINDEX_RSI_Min1W': '0..1 VNINDEX RSI min over the last 1W',
    # 'VNINDEX_RSI_Min1W_Close': 'VNINDEX Close at RSI min over the last 1W',
    # 'VNINDEX_RSI_Min1W_MFI': 'VNINDEX MFI at RSI min over the last 1W',
    # 'VNINDEX_RSI_Min1W_CMF': 'VNINDEX CMF at RSI min over the last 1W',
    # 'VNINDEX_RSI_Min1W_MACD': 'VNINDEX MACD at RSI min over the last 1W',
    # 'VNINDEX_RSI_Min1M': '0..1 VNINDEX RSI min over the last 1M',
    # 'VNINDEX_RSI_Min1M_Close': 'VNINDEX Close at RSI min over the last 1M',
    # 'VNINDEX_RSI_Min1M_MFI': 'VNINDEX MFI at RSI min over the last 1M',
    # 'VNINDEX_RSI_Min1M_CMF': 'VNINDEX CMF at RSI min over the last 1M',
    # 'VNINDEX_RSI_Min1M_MACD': 'VNINDEX MACD at RSI min over the last 1M',
    # 'VNINDEX_RSI_Min3M': '0..1 VNINDEX RSI min over the last 3M',
    # 'VNINDEX_RSI_Min3M_Close': 'VNINDEX Close at RSI min over the last 3M',
    # 'VNINDEX_RSI_Min3M_MFI': 'VNINDEX MFI at RSI min over the last 3M',
    # 'VNINDEX_RSI_Min3M_CMF': 'VNINDEX CMF at RSI min over the last 3M',
    # 'VNINDEX_RSI_Min3M_MACD': 'VNINDEX MACD at RSI min over the last 3M',
    # 'VN30_Volume_Max5Y_ID': 'ID at max Volume of VN30 in the last 5 years',
    # 'VN30_Volume_Max5Y_Close': 'Hight at Volume of VN30 in top Volume in last 5 years',
    # 'VN30_Volume_Max3Y_ID': 'ID at max Volume of VN30 in the last 3 years',
    # 'VN30_Volume_Max3Y_Close': 'Hight at Volume of VN30 in top Volume in last 3 years',
    'VN30': '0..inf VN30 value',
    'VN30_MA200': 'MA200 VN30 value',
    'VN30_T1W': '0..inf The value is ratio current index / index previous 1W',
    'VN30_T2W': '0..inf The value is ratio current index / index previous 2W',
    'VN30_T1M': '0..inf The value is ratio current index / index previous 1M',
    'VN30_T3M': '0..inf The value is ratio current index / index previous 3M',
    'VN30_RSI': 'RSI daily value of VN30',
    'VN30_RSI_T1W': 'RSI daily value of VN30 in the previous 1W',
    'VN30_MFI': 'MFI daily value of VN30',
    'VN30_MFI_T1W': 'MFI daily value of VN30 in the previous 1W',
    'VN30_RawMFI': 'Raw MFI daily value of VN30',
    'VN30_RawMFI_T1W': 'Raw MFI daily value of VN30 in the previous 1W',
    'VN30_CMF': 'CMF daily value of VN30',
    'VN30_CMF_T1W': 'CMF daily value of VN30 in the previous 1W',
    'VN30_MACD': 'MACD value of VN30',
    'VN30_MACD_T1W': 'MACD value of VN30 in the previous 1W',
    'VN30_MACDsign': 'MACDsign value of VN30',
    'VN30_MACDsign_T1W': 'MACDsign value of VN30 in the previous 1W',
    'VN30_MACDdiff': 'MACDdiffvalue of VN30',
    'VN30_MACDdiff_T1W': 'MACDdiffvalue of VN30 in the previous 1W',
    # 'VN30_RSI_Max1W': '0..1 VN30 RSI max over the last 1W',
    # 'VN30_RSI_Max1W_Close': 'VN30 Close at RSI max over the last 1W',
    # 'VN30_RSI_Max1W_MFI': 'VN30 MFI at RSI max over the last 1W',
    # 'VN30_RSI_Max1W_CMF': 'VN30 CMF at RSI max over the last 1W',
    # 'VN30_RSI_Max1W_MACD': 'VN30 MACD at RSI max over the last 1W',
    # 'VN30_RSI_Min1W': '0..1 VN30 RSI min over the last 1W',
    # 'VN30_RSI_Min1W_Close': 'VN30 Close at RSI min over the last 1W',
    # 'VN30_RSI_Min1W_MFI': 'VN30 MFI at RSI min over the last 1W',
    # 'VN30_RSI_Min1W_CMF': 'VN30 CMF at RSI min over the last 1W',
    # 'VN30_RSI_Min1W_MACD': 'VN30 MACD at RSI min over the last 1W',
    # 'VN30_RSI_MaxT3': '0..1 VN30 RSI max over the last 3D',
    # 'VN30_RSI_MaxT3_Close': 'VN30 Close at RSI max over the last 3D',
    # 'VN30_RSI_MaxT3_MFI': 'VN30 MFI at RSI max over the last 3D',
    # 'VN30_RSI_MaxT3_CMF': 'VN30 CMF at RSI max over the last 3D',
    # 'VN30_RSI_MaxT3_MACD': 'VN30 MACD at RSI max over the last 3D',
    # 'VN30_RSI_Max1M': '0..1 VN30 RSI max over the last 1M',
    # 'VN30_RSI_Max1M_Close': 'VN30 Close at RSI max over the last 1M',
    # 'VN30_RSI_Max1M_MFI': 'VN30 MFI at RSI max over the last 1M',
    # 'VN30_RSI_Max1M_CMF': 'VN30 CMF at RSI max over the last 1M',
    # 'VN30_RSI_Max1M_MACD': 'VN30 MACD at RSI max over the last 1M',
    # 'VN30_RSI_Max3M': '0..1 VN30 RSI max over the last 3M',
    # 'VN30_RSI_Max3M_Close': 'VN30 Close at RSI max over the last 3M',
    # 'VN30_RSI_Max3M_MFI': 'VN30 MFI at RSI max over the last 3M',
    # 'VN30_RSI_Max3M_CMF': 'VN30 CMF at RSI max over the last 3M',
    # 'VN30_RSI_Max3M_MACD': 'VN30 MACD at RSI max over the last 3M',
    # 'VN30_RSI_MinT3': '0..1 VN30 RSI min over the last 3D',
    # 'VN30_RSI_MinT3_Close': 'VN30 Close at RSI min over the last 3D',
    # 'VN30_RSI_MinT3_MFI': 'VN30 MFI at RSI min over the last 3D',
    # 'VN30_RSI_MinT3_CMF': 'VN30 CMF at RSI min over the last 3D',
    # 'VN30_RSI_MinT3_MACD': 'VN30 MACD at RSI min over the last 3D',
    # 'VN30_RSI_Min1M': '0..1 VN30 RSI min over the last 1M',
    # 'VN30_RSI_Min1M_Close': 'VN30 Close at RSI min over the last 1M',
    # 'VN30_RSI_Min1M_MFI': 'VN30 MFI at RSI min over the last 1M',
    # 'VN30_RSI_Min1M_CMF': 'VN30 CMF at RSI min over the last 1M',
    # 'VN30_RSI_Min1M_MACD': 'VN30 MACD at RSI min over the last 1M',
    # 'VN30_RSI_Min3M': '0..1 VN30 RSI min over the last 3M',
    # 'VN30_RSI_Min3M_Close': 'VN30 Close at RSI min over the last 3M',
    # 'VN30_RSI_Min3M_MFI': 'VN30 MFI at RSI min over the last 3M',
    # 'VN30_RSI_Min3M_CMF': 'VN30 CMF at RSI min over the last 3M',
    # 'VN30_RSI_Min3M_MACD': 'VN30 MACD at RSI min over the last 3M',
    'PB': '0..Inf Price/book ratio',
    'PE': '0..Inf P/E ratio',
    'PS': '0..inf P/S ratio',
    'EVEB': '0..inf EV/EBITDA ratio',
    'PCF': '0..inf P/Cash Flow ratio',
    'BVPS': '0..inf BVPS',
    'EPS': '0..inf EPS',
    'CF_Invest_3Y': 'SUM Cashflow investing of fixed assets and other long-term assets in the last 3 years',
    'CF_Invest_5Y': 'SUM Cashflow investing of fixed assets and other long-term assets in the last 5 years',
    'CF_Invest_P0': 'Cashflow investing of fixed assets and other long-term assets on the current quarter',
    'CF_Invest_P1': 'Cashflow investing of fixed assets and other long-term assets on the previous quarter',
    'CF_Invest_P2': 'Cashflow investing of fixed assets and other long-term assets on the previous 2 quarters',
    'CF_Invest_P3': 'Cashflow investing of fixed assets and other long-term assets on the previous 3 quarters',
    'CF_Invest_P4': 'Cashflow investing of fixed assets and other long-term assets on the previous 4 quarters',
    'Revenue_P0': 'Revenue on the current quarter',
    'Revenue_P1': 'Revenue on the previous quarter',
    'Revenue_P2': 'Revenue on the previous 2 quarters',
    'Revenue_P3': 'Revenue on the previous 3 quarters',
    'Revenue_P4': 'Revenue on the previous 4 quarters',
    'Revenue_P5': 'Revenue on the previous 5 quarters',
    'Revenue_P6': 'Revenue on the previous 6 quarters',
    'Revenue_P7': 'Revenue on the previous 7 quarters',
    'GPM_P0': 'Gross Profit Margin (%) on the current quarter',
    'GPM_P1': 'Gross Profit Margin (%) on the previous quarter',
    'GPM_P2': 'Gross Profit Margin (%) on the previous 2 quarters',
    'GPM_P3': 'Gross Profit Margin (%) on the previous 3 quarters',
    'GPM_P4': 'Gross Profit Margin (%) on the previous 4 quarters',
    'GPM_P5': 'Gross Profit Margin (%) on the previous 5 quarters',
    'GPM_P6': 'Gross Profit Margin (%) on the previous 6 quarters',
    'GPM_P7': 'Gross Profit Margin (%) on the previous 7 quarters',
    'DY': '0..inf Dividend yield (%)',
    'Debt_Eq': '0..inf Debt/Equity',
    'PEG': '0..inf PEG ratio (PEG=PE/GROW)',
    'MA10': '0..inf 10-day moving average',
    'MA20': '0..inf 20-day moving average',
    'MA50': '0..inf 50-day moving average',
    'MA100': '0..inf 100-day moving average',
    'MA200': '0..inf 200-day moving average',
    'MA10_T1': '0..inf 10-day moving average at T-1',
    'MA20_T1': '0..inf 20-day moving average at T-1',
    'MA50_T1': '0..inf 50-day moving average at T-1',
    'MA100_T1': '0..inf 100-day moving average at T-1',
    'MA200_T1': '0..inf 200-day moving average at T-1',
    'MA200_T100': '0..inf 200-day moving average at T-100',
    'MA200_T200': '0..inf 200-day moving average at T-200',
    'ROE3Y': '0..1 Averaged ROE in the last 3 years',
    'ROE5Y': '0..1 Averaged ROE in the last 5 years',
    'ROE10Y': '0..1 Averaged ROE in the last 10 years',
    'ROE_Min3Y': '0..1 Min ROE in the last 3 years',
    'ROE_Min5Y': '0..1 Min ROE in the last 5 years',
    'ROE_Min10Y': '0..1 Min ROE in the last 10 years',
    'ROIC3Y': '0..1 Averaged ROIC in the last 3 years',
    'ROIC5Y': '0..1 Averaged ROIC in the last 5 years',
    'ROIC10Y': '0..1 Averaged ROIC in the last 10 years',
    'ROIC_Min3Y': '0..1 Min ROIC in the last 3 years',
    'ROIC_Min5Y': '0..1 Min ROIC in the last 5 years',
    'ROIC_Min10Y': '0..1 Min ROIC in the last 10 years',
    'CF_OA_3Y': 'SUM Cashflow over assets in the last 3 years',
    'CF_OA_5Y': 'SUM Cashflow over assets in the last 5 years',
    'CF_OA_P0': 'Cashflow over assets on the current quarter',
    'CF_OA_P1': 'Cashflow over assets on the previous quarter',
    'CF_OA_P2': 'Cashflow over assets on the 2 previous quarter',
    'CF_OA_P3': 'Cashflow over assets on the 3 previous quarter',
    'CF_OA_P4': 'Cashflow over assets on the 4 previous quarter',
    'Dividend_Min3Y': '0..inf Min dividend in the last 3 years',
    'Dividend_1Y': '0..inf Dividend in the last 1 year',
    'Dividend_3Y': '0..inf Dividend in the last 3 years',
    'NP_R': 'Net profit compared to the same quarter - (NP_P4/ NP_P0 - 1)',
    'NP_P0': 'Net profit 0on the current quarter',
    'NP_P1': 'Net profit previous quarter',
    'NP_P2': 'Net profit 2 previous quarter',
    'NP_P3': 'Net profit 3 previous quarter',
    'NP_P4': 'Net profit 4 previous quarter',
    'NP_P5': 'Net profit 5 previous quarter',
    'NP_P6': 'Net profit 6 previous quarter',
    'NP_P7': 'Net profit 7 previous quarter',
    'ID_LO_2Y': '0 .. M. Index of Lowest value in 2 years',
    'ID_LO_3Y': '0 .. M. Index of Lowest value in 3 years',
    'ID_LO_5Y': '0 .. M. Index of Lowest value in 5 years',
    'ID_HI_2Y': '0 .. M. Index of Highest value in 2 years',
    'ID_HI_3Y': '0 .. M. Index of Highest value in 3 years',
    'ID_HI_5Y': '0 .. M. Index of Highest value in 5 years',
    'HI_3M_T1': '0 .. M. Highest value in 3 months',
    'HI_1M_T1': '0 .. M. Highest value in 1 month',
    'LO_3M_T1': '0 .. M. Lowest value in 3 months',
    'LO_1M_T1': '0 .. M. Lowest value in 1 month',
    'FSCORE': '0..9 F-Score - Piotroski',
    'FSCORE_P1': '0..9 F-Score - Piotroski at previous quarter',
    'totalAsset_P0': 'Total Asset on the current quarter',
    'Cash_P0': 'Cash + Short-term investmenton on the current quarter',
    'StDebt_P0': 'Short-term debt on the current quarter',
    'AR_P0': 'Accounts receivable on the current quarter',
    'CR_P0': 'Current ratio on the current quarter',
    'CR_P4': 'Current ratio at 4 previous quarters',
    'LtDebt_P0': 'Long-term liabilities (Bn. VND) on the current quarter',
    'IntCov_P0': 'Interest Coverage on the current quarter',
    'IntCov_P4': 'Interest Coverage at 4 previous quarters',
    'ROA_P0': 'Return on Assets on the current quarter',
    'ROA_P4': 'Return on Assets at 4 previous quarters',
    'EBITM_P0': 'EBIT Margin % on the current quarter',
    'EBITM_P4': 'EBIT Margin % at 4 previous quarters',
    'NPM_P0': 'Net Profit Margin (%) % on the current quarter',
    'NPM_P4': 'Net Profit Margin (%) % at 4 previous quartersr',
    'EBITDA_P0': 'EBITDA (Bn. VND) on the current quarter',
    'CashR_P0': 'Cash Ratio on the current quarter',
    'CashR_P4': 'Cash Ratio at 4 previous quarters',
    'QuickR_P0': 'Quick Ratio on the current quarter',
    'QuickR_P4': 'Quick Ratio at 4 previous quarters',
    'FinLev_P0': 'Financial Leverage on the current quarter',
    'FinLev_P4': 'Financial Leverage at 4 previous quarters',
    'AssetTurn_P0': 'Asset Turnover on the current quarter',
    'AssetTurn_P4': 'Asset Turnover at 4 previous quarters',
    'FAssetTurn_P0': 'Fixed Asset Turnover on the current quarter',
    'FAssetTurn_P4': 'Fixed Asset Turnover at 4 previous quarters',
    'DSO_P0': 'Days Sales Outstanding on the current quarter',
    'DSO_P4': 'Days Sales Outstanding at 4 previous quarters',
    'DIO_P0': 'Days Inventory Outstanding on the current quarter',
    'DIO_P4': 'Days Inventory Outstanding at 4 previous quarters',
    'DPO_P0': 'Days Payable Outstanding on the current quarter',
    'DPO_P4': 'Days Payable Outstanding at 4 previous quarters',
    'CashCycle_P0': 'Cash Conversion Cycle on the current quarter',
    'CashCycle_P4': 'Cash Conversion Cycle at 4 previous quarters',
    'InvTurn_P0': 'Inventory Turnover on the current quarter',
    'InvTurn_P4': 'Inventory Turnover at 4 previous quarters',
    'STLTDebt_Eq_P0': 'Short-term and long-term debt to equity on the current quarter',
    'STLTDebt_Eq_P4': 'Short-term and long-term debt to equity at 4 previous quarters',
    'Debt_Eq_P0': 'Debt to equity on the current quarter',
    'Debt_Eq_P4': 'Debt to equity at 4 previous quarters',
    'FAsset_Eq_P0': 'Fixed Asset to equity on the current quarter',
    'FAsset_Eq_P4': 'Fixed Asset to equity at 4 previous quarters',
    'OwnEq_Cap_P0': 'Owner\'s equity to capital on the current quarter',
    'OwnEq_Cap_P4': 'Owner\'s equity to capital at 4 previous quarters',
    'Revenue_YoY_P0': 'Revenue growth (Revenue YoY (%)) compared to the same quarter of the previous year (P0/P4 - 1)',
    'Revenue_YoY_P4': 'Revenue growth (Revenue YoY (%)) compared to the same quarter of the previous year (P4/P8 - 1)',
    'ID_Current': 'ID Current session',
    'ID_Release': 'ID financial report release',
    'Volume_Max5Y': 'Volume at max Volume in the last 5 years',
    'Volume_Max5Y_High': 'Hight at max Volume in the last 5 years',
    'Volume_Max5Y_Low': 'Low at max Volume in the last 5 years',
    'Volume_Max5Y_Close': 'Close at max Volume in the last 5 years',
    'Volume_Max5Y_ID': 'ID at max Volume in the last 5 years',
    'Volume_Max2Y': 'Volume at max Volume in the last 2 years',
    'Volume_Max2Y_High': 'Hight at max Volume in the last 2 years',
    'Volume_Max2Y_Low': 'Low at max Volume in the last 2 years',
    'Volume_Max2Y_Close': 'Close at max Volume in the last 2 years',
    'Volume_Max2Y_ID': 'ID at max Volume in the last 2 years',
    'Volume_Max1Y': 'Volume at max Volume in the last 1 years',
    'Volume_Max1Y_High': 'Hight at max Volume in the last 1 years',
    'Volume_Max1Y_Low': 'Low at max Volume in the last 1 years',
    'Volume_Max1Y_Close': 'Close at max Volume in the last 1 years',
    'Volume_Max1Y_ID': 'ID at max Volume in the last 1 years',
    'Volume_MaxTop5_2Y': 'Volume in top 5 Volume in last 2 years',
    'Volume_MaxTop5_2Y_High': 'Hight at Volume in top 5 Volume in last 2 years',
    'Volume_MaxTop5_2Y_Low': 'Low at Volume in top 5 Volume in last 2 years',
    'Volume_MaxTop5_2Y_Close': 'Close Volume in top 5 Volume in last 2 years',
    'Volume_MaxTop5_2Y_ID': 'ID at Volume in top 5 Volume in last 2 years',
    'VAP1W': 'Close in the largest trading area in 1W',
    'VAP2W': 'Close in the largest trading area in 2W',
    'VAP1M': 'Close in the largest trading area in 1M',
    'VAP3M': 'Close in the largest trading area in 3M',
    'VAP6M': 'Close in the largest trading area in 6M',
    'Res_1Y': 'Resistance line lookback 1 years',
    'Sup_1Y': 'Support line lookback 1 years',
    'Res_1Y_T1': 'Resistance line lookback 1 years at T-1',
    'Sup_1Y_T1': 'Support line lookback 1 years at T-1',
    'STrend_S': 'Strend line in short term',
    'STrend_L': 'Strend line in long term',
    'vol1M': 'Volume largest trading in 1M',
    'vol3M': 'Volume largest trading in 3M',
    'vol1M_Low': 'Low price in volume 1M',
    'vol3M_Low': 'Low price in volume 3M',
    'Volume_3M_P90': 'Volume at percentile 90 Volume in the last 3 months',
    'Volume_3M_P80': 'Volume at percentile 80 Volume in the last 3 months',
    'Volume_3M_P50': 'Volume at percentile 50 Volume in the last 3 months',
    'Volume_6M_P50': 'Volume at percentile 50 Volume in the last 6 months',
    'Volume_1Y_P50': 'Volume at percentile 50 Volume in the last 1 year',
    'Volume_1W': 'Mean daily trading volume over the last 1 week',
    'Volume_1M': 'Mean daily trading volume over the last 1 month',
    'Volume_1M_P50': 'Volume at percentile 50 Volume in the last 1 months',
    'Volume_3M': 'Mean daily trading volume over the last 3 months',
    'Change_T1': 'Price change from previous day',
    'Change_T5': 'Price change from 1 week ago',
    'Change_1W': 'Averaged Price change (Change_T1) at the last 1 week',
    'Change_1M': 'Averaged Price change (Change_T1) at the last 1 month',
    'Change_3M': 'Averaged Price change (Change_T1) at the last 3 months',
    'ROE_Trailing': 'Sum of ROE at the last 4 quarters',
    'ROIC_Trailing': 'Sum of ROIC (self-calculation) at the last 4 quarters',
    'ROIC_Trailing_v1': 'Sum of ROIC % (getting ROIC % from report) at the last 4 quarters',
    'OShares': 'Outstanding Shares at the end of the quarter',
    'ID_C_XMA200': '0 .. M. Index of Close price crossed MA200 at most recently',
    'ID_C_XMA200_P1': '0 .. M. Index of Close price crossed MA200 at previous time',
    'ID_C_XVAP1M_Up_P0': '0 .. M. Index of Close price crossed up VAP1M line at most recently',
    'ID_C_XVAP1M_Up_P1': '0 .. M. Index of Close price crossed up VAP1M line at previous time',
    'ID_C_XVAP1M_Up_P2': '0 .. M. Index of Close price crossed up VAP1M line at 2 previous time',
    'ID_C_XVAP1M_Down_P0': '0 .. M. Index of Close price crossed down VAP1M line at most recently',
    'ID_C_XVAP1M_Down_P1': '0 .. M. Index of Close price crossed down VAP1M line at previous time',
    'ID_C_XVAP1M_Down_P2': '0 .. M. Index of Close price crossed down VAP1M line at 2 previous time',
    'ID_C_XVAP3M_Up_P0': '0 .. M. Index of Close price crossed up VAP3M line at most recently',
    'ID_C_XVAP3M_Up_P1': '0 .. M. Index of Close price crossed up VAP3M line at previous time',
    'ID_C_XVAP3M_Up_P2': '0 .. M. Index of Close price crossed up VAP3M line at 2 previous time',
    'ID_C_XVAP3M_Down_P0': '0 .. M. Index of Close price crossed down VAP3M line at most recently',
    'ID_C_XVAP3M_Down_P1': '0 .. M. Index of Close price crossed down VAP3M line at previous time',
    'ID_C_XVAP3M_Down_P2': '0 .. M. Index of Close price crossed down VAP3M line at 2 previous time',
    'ID_XVAP1M_Up_P0': '0 .. M. Index of Close price crossed up VAP1M value at most recently',
    'ID_XVAP1M_Up_P1': '0 .. M. Index of Close price crossed up VAP1M value at previous time',
    'ID_XVAP1M_Up_P2': '0 .. M. Index of Close price crossed up VAP1M value at 2 previous time',
    'ID_XVAP1M_Down_P0': '0 .. M. Index of Close price crossed down VAP1M value at most recently',
    'ID_XVAP1M_Down_P1': '0 .. M. Index of Close price crossed down VAP1M value at previous time',
    'ID_XVAP1M_Down_P2': '0 .. M. Index of Close price crossed down VAP1M value at 2 previous time',
    'ID_XVAP3M_Up_P0': '0 .. M. Index of Close price crossed up VAP3M value at most recently',
    'ID_XVAP3M_Up_P1': '0 .. M. Index of Close price crossed up VAP3M value at previous time',
    'ID_XVAP3M_Up_P2': '0 .. M. Index of Close price crossed up VAP3M value at 2 previous time',
    'ID_XVAP3M_Down_P0': '0 .. M. Index of Close price crossed down VAP3M value at most recently',
    'ID_XVAP3M_Down_P1': '0 .. M. Index of Close price crossed down VAP3M value at previous time',
    'ID_XVAP3M_Down_P2': '0 .. M. Index of Close price crossed down VAP3M value at 2 previous time',
    'T1W': 'Ratio current close / previous 1W',
    'T2W': 'Ratio current close / previous 2W',
    'T1M': 'Ratio current close / previous 1M',
    'T3M': 'Ratio current close / previous 3M',
    'Open_D00': 'Open price in the previous 0D compared to the last 5D average close price',
    'Open_D01': 'Open price in the previous 1D compared to the last 5D average close price',
    'Open_D02': 'Open price in the previous 2D compared to the last 5D average close price',
    'Open_D03': 'Open price in the previous 3D compared to the last 5D average close price',
    'Open_D04': 'Open price in the previous 4D compared to the last 5D average close price',
    'Open_D05': 'Open price in the previous 5D compared to the last 5D average close price',
    'High_D00': 'High price in the previous 0D compared to the last 5D average close price',
    'High_D01': 'High price in the previous 1D compared to the last 5D average close price',
    'High_D02': 'High price in the previous 2D compared to the last 5D average close price',
    'High_D03': 'High price in the previous 3D compared to the last 5D average close price',
    'High_D04': 'High price in the previous 4D compared to the last 5D average close price',
    'High_D05': 'High price in the previous 5D compared to the last 5D average close price',
    'Low_D00': 'Low price in the previous 0D compared to the last 5D average close price',
    'Low_D01': 'Low price in the previous 1D compared to the last 5D average close price',
    'Low_D02': 'Low price in the previous 2D compared to the last 5D average close price',
    'Low_D03': 'Low price in the previous 3D compared to the last 5D average close price',
    'Low_D04': 'Low price in the previous 4D compared to the last 5D average close price',
    'Low_D05': 'Low price in the previous 5D compared to the last 5D average close price',
    'Close_D01': 'Close price in the previous 1D compared to the last 5D average close price',
    'Close_D02': 'Close price in the previous 2D compared to the last 5D average close price',
    'Close_D03': 'Close price in the previous 3D compared to the last 5D average close price',
    'Close_D04': 'Close price in the previous 4D compared to the last 5D average close price',
    'Close_D05': 'Close price in the previous 5D compared to the last 5D average close price',
    'Volume_D00': 'Volume in the previous 0D compared to the last 5D average volume',
    'Volume_D01': 'Volume in the previous 1D compared to the last 5D average volume',
    'Volume_D02': 'Volume in the previous 2D compared to the last 5D average volume',
    'Volume_D03': 'Volume in the previous 3D compared to the last 5D average volume',
    'Volume_D04': 'Volume in the previous 4D compared to the last 5D average volume',
    'Volume_D05': 'Volume in the previous 5D compared to the last 5D average volume',
    'Open_W00': 'Open price in the previous 0W compared to the last 5W average close price',
    'Open_W01': 'Open price in the previous 1W compared to the last 5W average close price',
    'Open_W02': 'Open price in the previous 2W compared to the last 5W average close price',
    'Open_W03': 'Open price in the previous 3W compared to the last 5W average close price',
    'Open_W04': 'Open price in the previous 4W compared to the last 5W average close price',
    'Open_W05': 'Open price in the previous 5W compared to the last 5W average close price',
    'High_W00': 'High price in the previous 0W compared to the last 5W average close price',
    'High_W01': 'High price in the previous 1W compared to the last 5W average close price',
    'High_W02': 'High price in the previous 2W compared to the last 5W average close price',
    'High_W03': 'High price in the previous 3W compared to the last 5W average close price',
    'High_W04': 'High price in the previous 4W compared to the last 5W average close price',
    'High_W05': 'High price in the previous 5W compared to the last 5W average close price',
    'Low_W00': 'Low price in the previous 0W compared to the last 5W average close price',
    'Low_W01': 'Low price in the previous 1W compared to the last 5W average close price',
    'Low_W02': 'Low price in the previous 2W compared to the last 5W average close price',
    'Low_W03': 'Low price in the previous 3W compared to the last 5W average close price',
    'Low_W04': 'Low price in the previous 4W compared to the last 5W average close price',
    'Low_W05': 'Low price in the previous 5W compared to the last 5W average close price',
    'Close_W01': 'Close price in the previous 1W compared to the last 5W average close price',
    'Close_W02': 'Close price in the previous 2W compared to the last 5W average close price',
    'Close_W03': 'Close price in the previous 3W compared to the last 5W average close price',
    'Close_W04': 'Close price in the previous 4W compared to the last 5W average close price',
    'Close_W05': 'Close price in the previous 5W compared to the last 5W average close price',
    'Volume_W00': 'Volume in the previous 0W compared to the last 5W average volume',
    'Volume_W01': 'Volume in the previous 1W compared to the last 5W average volume',
    'Volume_W02': 'Volume in the previous 2W compared to the last 5W average volume',
    'Volume_W03': 'Volume in the previous 3W compared to the last 5W average volume',
    'Volume_W04': 'Volume in the previous 4W compared to the last 5W average volume',
    'Volume_W05': 'Volume in the previous 5W compared to the last 5W average volume',
    'Open_M00': 'Open price in the previous 0M compared to the last 5M average close price',
    'Open_M01': 'Open price in the previous 1M compared to the last 5M average close price',
    'Open_M02': 'Open price in the previous 2M compared to the last 5M average close price',
    'Open_M03': 'Open price in the previous 3M compared to the last 5M average close price',
    'Open_M04': 'Open price in the previous 4M compared to the last 5M average close price',
    'Open_M05': 'Open price in the previous 5M compared to the last 5M average close price',
    'High_M00': 'High price in the previous 0M compared to the last 5M average close price',
    'High_M01': 'High price in the previous 1M compared to the last 5M average close price',
    'High_M02': 'High price in the previous 2M compared to the last 5M average close price',
    'High_M03': 'High price in the previous 3M compared to the last 5M average close price',
    'High_M04': 'High price in the previous 4M compared to the last 5M average close price',
    'High_M05': 'High price in the previous 5M compared to the last 5M average close price',
    'Low_M00': 'Low price in the previous 0M compared to the last 5M average close price',
    'Low_M01': 'Low price in the previous 1M compared to the last 5M average close price',
    'Low_M02': 'Low price in the previous 2M compared to the last 5M average close price',
    'Low_M03': 'Low price in the previous 3M compared to the last 5M average close price',
    'Low_M04': 'Low price in the previous 4M compared to the last 5M average close price',
    'Low_M05': 'Low price in the previous 5M compared to the last 5M average close price',
    'Close_M01': 'Close price in the previous 1M compared to the last 5M average close price',
    'Close_M02': 'Close price in the previous 2M compared to the last 5M average close price',
    'Close_M03': 'Close price in the previous 3M compared to the last 5M average close price',
    'Close_M04': 'Close price in the previous 4M compared to the last 5M average close price',
    'Close_M05': 'Close price in the previous 5M compared to the last 5M average close price',
    'Volume_M00': 'Volume in the previous 0M compared to the last 5M average volume',
    'Volume_M01': 'Volume in the previous 1M compared to the last 5M average volume',
    'Volume_M02': 'Volume in the previous 2M compared to the last 5M average volume',
    'Volume_M03': 'Volume in the previous 3M compared to the last 5M average volume',
    'Volume_M04': 'Volume in the previous 4M compared to the last 5M average volume',
    'Volume_M05': 'Volume in the previous 5M compared to the last 5M average volume',
    'ICB_Code': 'CT, NH, BH, CK: Industry Classification Benchmark of ticker',
    'CT_Code': 'Company type of ticker',
    'Exchange': 'Stock exchange of ticker',
    'PE_ICB': 'PE of stock industry group is calculated by mean',
    'PE_ICB_Weight': 'PE of stock industry group is calculated by weight',
    'PE_MA5Y': '5 years moving average of PE',
    'PE_MA1Y': '1 years moving average of PE',
    'PE_MA3M': '3 months moving average of PE',
    'PE_SD5Y': '5 years standard deviation of PE',
    'PE_SD1Y': '1 years standard deviation of PE',
    'PE_SD3M': '3 months standard deviation of PE',
    'PB_MA5Y': '5 years moving average of PB',
    'PB_MA1Y': '1 years moving average of PB',
    'PB_MA3M': '3 months moving average of PB',
    'PB_SD5Y': '5 years standard deviation of PB',
    'PB_SD1Y': '1 years standard deviation of PB',
    'PB_SD3M': '3 months standard deviation of PB',
    'EVEB_MA5Y': '5 years moving average of EV/EBITDA',
    'EVEB_MA1Y': '1 years moving average of EV/EBITDA',
    'EVEB_MA3M': '3 months moving average of EV/EBITDA',
    'EVEB_SD5Y': '5 years standard deviation of EV/EBITDA',
    'EVEB_SD1Y': '1 years standard deviation of EV/EBITDA',
    'EVEB_SD3M': '3 months standard deviation of EV/EBITDA',
    "Beta": "Beta measures the sensitivity of a stock’s returns relative to the market. A Beta > 1 indicates higher volatility than the market.",
    "D_Beta": "Downside Beta measures the stock’s sensitivity to market movements during only market downturns. It reflects risk in bear markets.",
    "Dev": "Standard Deviation measures the overall volatility of a stock’s returns over time, regardless of direction (up or down).",
    "D_Dev": "Downside Deviation measures the volatility of negative returns only, capturing the risk of losses and ignoring upside volatility.",
    "Risk_Rating": "A composite risk score derived from the Beta and Deviation risk bins",
}
