import datetime
import time

import numpy as np
import pandas as pd
import requests
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry
from vnstock import Vnstock
from requests import exceptions as req_exc

TRANSIENT_EXC = (
    req_exc.ConnectionError,
    req_exc.Timeout,
    req_exc.ChunkedEncodingError,
)

class StockBase:
    def _handle_error(self, error):
        """General error handling function."""
        if isinstance(error, TRANSIENT_EXC):
            print("Connection error to the API.")
        else:
            print(f"Unknown error: {str(error)}")
        # Logging, alert, etc. can go here

    # def execute_query(self, func, *args, **kwargs):
    #     """
    #     Chỉ xử lý lỗi; KHÔNG tự retry ở đây.
    #     - Retry ngắn hạn để cho HTTPAdapter xử lý.
    #     - Retry dài hạn để Celery (autoretry_for) xử lý.
    #     """
    #     try:
    #         result = func(*args, **kwargs)
    #         if result is None:
    #             raise RuntimeError("Empty response")
    #         return result
    #     except req_exc.HTTPError as e:
    #         # Nếu là 429/5xx lọt qua tới đây thì để Celery retry
    #         raise ConnectionError(str(e))  # builtin cho Celery pickle
    #     except (req_exc.ConnectionError, req_exc.Timeout, req_exc.ChunkedEncodingError) as e:
    #         # Chuẩn hóa về builtin để Celery autoretry
    #         raise ConnectionError(str(e))
    #     except Exception:
    #         # Lỗi nghiệp vụ khác: ném nguyên xi
    #         raise

    def execute_query(self, func, *args, max_retries=2, retry_delay=2, **kwargs):
        """
        Execute function with retry and error handling.

        Args:
            func: Function to execute.
            *args, **kwargs: Arguments for the function.
            max_retries (int): Number of retry attempts.
            retry_delay (int): Seconds to wait before retrying.
        """
        attempt = 0
        while True:
            try:
                result = func(*args, **kwargs)
                return result

            except req_exc.HTTPError as e:
                print(f"[Retry {attempt + 1}/{max_retries}] Error executing {func.__name__}: {str(e)}")
                self._handle_error(e)

                status = getattr(e.response, "status_code", None)
                if status not in (429, 500, 502, 503, 504):
                    raise

                if attempt > max_retries:
                    print("Max retry attempts reached. Raising error.")
                    raise

                attempt += 1
                time.sleep(retry_delay)  # wait before next retry

            except TRANSIENT_EXC as e:
                if attempt > max_retries:
                    raise Exception(str(e))  # builtin Celery pickle

                attempt += 1
                time.sleep(retry_delay)
            except Exception as e:
                self._handle_error(e)
                raise


    def _create_retry_session(
            self,
            retries: int = 3,
            backoff_factor: float = 0.5,
            status_forcelist=(429, 500, 502, 503, 504),
    ):
        # sleep = backoff_factor * (2 ** (retry_number - 1))
        session = requests.Session()
        retry = Retry(
            total=retries,
            read=retries,
            connect=retries,
            status=retries,
            backoff_factor=backoff_factor,
            status_forcelist=status_forcelist,
            allowed_methods=frozenset(["GET", "POST"]),
            respect_retry_after_header=True,
            raise_on_status=False,
        )
        adapter = HTTPAdapter(max_retries=retry)
        session.mount("https://", adapter)
        session.mount("http://", adapter)
        return session




# Example of inheriting this base class
class StockQuery(StockBase):
    def __init__(self, start_date='2000-01-01', end_date=None):
        self.stock_tcbs = Vnstock(show_log=False).stock(source='TCBS', symbol='HPG')
        self.stock_vci = Vnstock(show_log=False).stock(source='VCI', symbol='HPG')
        self.start_date = start_date
        self.session = self._create_retry_session()

        if end_date:
            self.end_date = end_date
        else:
            self.end_date = (datetime.datetime.today() + datetime.timedelta(days=1)).strftime('%Y-%m-%d')

    def update_ticker(self, ticker):
        """Fetch stock data using the vnstock3 library."""
        self.stock_vci.update_symbol(ticker)
        self.stock_tcbs.update_symbol(ticker)

    def symbols_by_exchange(self):
        return self.execute_query(self.stock_vci.listing.symbols_by_exchange)

    def symbols_by_industries(self):
        return self.execute_query(self.stock_vci.listing.symbols_by_industries)

    def symbols_by_group(self, group):
        return self.execute_query(self.stock_vci.listing.symbols_by_group, group)

    def get_historical_ticker(self, ticker, start_date=None) -> pd.DataFrame:
        if start_date is None:
            start_date = self.start_date
        df = self.execute_query(self.stock_vci.quote.history, symbol=ticker, start=start_date, end=self.end_date,
                                interval='1D', count_back=None)
        df['time'] = df['time'].dt.strftime('%Y-%m-%d')
        df = df.drop_duplicates(subset=['time'], keep='first').reset_index(drop=True)
        return df

    def get_historical_index(self, index) -> pd.DataFrame:
        df = self.execute_query(self.stock_tcbs.quote.history, symbol=index, start=self.start_date, end=self.end_date,
                                interval='1D', count_back=None)
        if df is None or len(df) == 0:
            df = self.execute_query(self.stock_vci.quote.history, symbol=index, start=self.start_date,
                                    end=self.end_date,
                                    interval='1D', count_back=None)
        df['time'] = df['time'].dt.strftime('%Y-%m-%d')
        df = df.drop_duplicates(subset=['time'], keep='first').reset_index(drop=True)
        return df

    def get_historical_symbol(self, symbol, start_date=None) -> pd.DataFrame:
        index_symbols = ["VNINDEX", "VN30", "HNXINDEX", "HNX30", "UPCOMINDEX"]
        is_index = symbol.upper() in index_symbols

        if is_index:
            df = self.get_historical_index(symbol)
        else:
            df = self.get_historical_ticker(symbol, start_date)

        return df

    def get_finance_ratio_vci(self, period='quarter') -> pd.DataFrame:
        df = self.execute_query(self.stock_vci.finance.ratio, period=period, lang='en', dropna=False)
        df.columns = df.columns.droplevel(0)
        if period == 'year':
            df['lengthReport'] = 5
        df.sort_values(['yearReport', 'lengthReport'], ascending=[False, False], inplace=True)
        return df

    def get_finance_cashflow_vci(self, period='quarter') -> pd.DataFrame:
        df = self.execute_query(self.stock_vci.finance.cash_flow, period=period, lang='en', dropna=False)
        if period == 'year':
            df['lengthReport'] = 5
        df.sort_values(['yearReport', 'lengthReport'], ascending=[False, False], inplace=True)
        return df

    def get_finance_balance_vci(self, period='quarter') -> pd.DataFrame:
        df = self.execute_query(self.stock_vci.finance.balance_sheet, period=period, lang='en', dropna=False)
        if period == 'year':
            df['lengthReport'] = 5
        df.sort_values(['yearReport', 'lengthReport'], ascending=[False, False], inplace=True)
        return df

    def get_finance_income_vci(self, period='quarter') -> pd.DataFrame:
        df = self.execute_query(self.stock_vci.finance.income_statement, period=period, lang='en', dropna=False)
        if period == 'year':
            df['lengthReport'] = 5
        df.sort_values(['yearReport', 'lengthReport'], ascending=[False, False], inplace=True)
        return df

    def get_finance_ratio_tcbs(self, period='quarter') -> pd.DataFrame:
        df = self.execute_query(self.stock_tcbs.finance.ratio, period=period, dropna=False)
        if period == 'year':
            df['year'] = df.index
            df['quarter'] = 5

        df[['year', 'quarter']] = df[['year', 'quarter']].astype(int)
        return df

    def get_finance_cashflow_tcbs(self, period='quarter') -> pd.DataFrame:
        df = self.execute_query(self.stock_tcbs.finance.cash_flow, period=period)
        if period == 'year':
            df['year'] = df.index
            df['quarter'] = 5

        df[['year', 'quarter']] = df[['year', 'quarter']].astype(int)
        return df

    @staticmethod
    def _fill_finance_by_map(df: pd.DataFrame) -> pd.DataFrame:
        """
        Check each row in the DataFrame.
        If the value in the key column is NaN, fill it with the value from the mapped column.

        Args:
            df (pd.DataFrame): original data
            mapping (dict): {key: value} mapping

        Returns:
            pd.DataFrame: DataFrame after filling
        """
        mapping = {
            'P/E': "price_to_earning",
            "P/B": "price_to_book",
            "EV/EBITDA": "value_before_ebitda",

            "ROE (%)": "roe",
            "ROA (%)": "roa",
            "EBIT Margin (%)": "ebit_on_revenue",

            "Days Sales Outstanding": "days_receivable",
            "Days Inventory Outstanding": "days_inventory",
            "Interest Coverage": "ebit_on_interest",  # (negative)
            "Current Ratio": "current_payment",
            "Quick Ratio": "quick_payment",

            "Asset Turnover": "revenue_on_asset",
            "Debt/Equity": "payable_on_equity",
            "Gross Profit Margin (%)": "gross_profit_margin",
        }

        # Normalize NaN for empty strings
        for col in df.columns:
            if df[col].dtype == object:
                df[col] = df[col].replace(r"^\s*$", np.nan, regex=True)

        # Fill key column with value column when missing
        for k, v in mapping.items():
            if k in df.columns and v in df.columns:
                df[k] = df[k].fillna(df[v])
        return df

    def get_finance_data(self, ticker, period='quarter') -> pd.DataFrame:
        self.update_ticker(ticker)

        df_ratio = self.get_finance_ratio_vci(period)
        df_income = self.get_finance_income_vci(period)
        df_balance = self.get_finance_balance_vci(period)
        df_cashflow = self.get_finance_cashflow_vci(period)

        pd_fa = pd.merge(df_ratio, df_income, on=['ticker', 'yearReport', 'lengthReport'], how='left') \
            .merge(df_balance, on=['ticker', 'yearReport', 'lengthReport'], how='left') \
            .merge(df_cashflow, on=['ticker', 'yearReport', 'lengthReport'], how='left')
        pd_fa['periods'] = pd_fa['yearReport'].astype(str) + '-' + pd_fa['lengthReport'].astype(str)

        try:
            ratio_tcbs = self.get_finance_ratio_tcbs(period)
            cash_flow_tcbs = self.get_finance_cashflow_tcbs(period)

            pd_tcbs = pd.merge(ratio_tcbs, cash_flow_tcbs, on=['year', 'quarter'], how='outer')
            pd_tcbs['periods'] = pd_tcbs['year'].astype(str) + '-' + pd_tcbs['quarter'].astype(str)
            pd_tcbs.drop_duplicates(subset=['periods'], keep='first', inplace=True)

            pd_fa = pd.merge(pd_fa, pd_tcbs, on='periods', how='outer') \
                .sort_values('periods', ascending=False) \
                .reset_index(drop=True)
            pd_fa['lengthReport'] = pd_fa[['lengthReport', 'quarter']].max(axis=1).astype(int).astype(str)
            pd_fa['yearReport'] = pd_fa[['yearReport', 'year']].max(axis=1).astype(int).astype(str)
            pd_fa['ticker'] = ticker
            pd_fa.drop(['quarter', 'year'], axis=1, inplace=True)
        except Exception as e:
            print(f'Wrong ticker with TCBS: {ticker} - {e}')
        pd_fa.columns.str.strip()
        return self._fill_finance_by_map(pd_fa)


    def get_enrich_vnindex(self):
        url = 'https://s.cafef.vn/Ajax/PageNew/FinanceData/GetDataChartPE.ashx'
        try:
            response = self.session.get(url, timeout=20)
            response.raise_for_status()
        except requests.exceptions.RequestException as e:
            raise RuntimeError(f"Failed to fetch PE data: {e}")

        response = response.json()['Data']['DataChart']
        df_00 = pd.json_normalize(response)

        df_00['time'] = df_00['TimeStamp'].agg(lambda x: datetime.datetime.fromtimestamp(x))
        df_00['time'] = df_00['time'].dt.strftime('%Y-%m-%d')
        df_00 = df_00.drop_duplicates(subset=['time'], keep='first').reset_index(drop=True)

        # get trading_session
        df_01 = self.get_unadjust_price('VNINDEX')
        df_01['trading_session'] = df_01['GiaTriKhopLenh'].copy()
        df_01['volume_session'] = df_01['KhoiLuongKhopLenh'].copy()

        df = df_01[['time', 'trading_session', 'volume_session']].merge(df_00, on='time', how='left')
        return df

    def get_unadjust_price(self, ticker):
        """Get price date isn't adjusted"""

        url = 'https://s.cafef.vn/Ajax/PageNew/DataHistory/PriceHistory.ashx'
        try:
            response = self.session.get(
                url,
                params={
                    'Symbol': ticker,
                    'StartDate': self.start_date,
                    'PageSize': 10000
                },
                timeout=20
            )
            response.raise_for_status()
        except requests.exceptions.RequestException as e:
            raise RuntimeError(f"Failed to fetch unadjusted price data for {ticker}: {e}")

        response = response.json()['Data']['Data']
        df = pd.json_normalize(response)

        df['time'] = pd.to_datetime(df['Ngay'], format='%d/%m/%Y', errors='coerce')
        df = df.dropna(subset=['time'])
        df['time'] = df['time'].dt.strftime('%Y-%m-%d')
        df = df.sort_values('time', ascending=True)
        df = df.drop_duplicates(subset=['time'], keep='first').reset_index(drop=True)

        return df


if __name__ == "__main__":
    # Using the inherited class
    # now_time = datetime.datetime.now()
    # vnsto = StockQuery()
    # df = vnsto.get_unadjust_price('HPG')
    # vnsto.get_finance_data(ticker='HPG')
    # a = vnsto.symbols_by_industries()
    # # a = vnsto.get_finance_data(ticker='MSB', period='year')
    # # a = vnsto.get_historical_ticker(ticker='ELC')
    #
    # # df = vnsto.get_historical_index('VNINDEX')
    # df['trading_session'] = df['GiaTriKhopLenh'].copy()
    # df.to_csv('trading_session.csv', index=False)
    #
    # df['time'] = pd.to_datetime(df['time'], format='%Y-%m-%d')
    # additonal_df = pd.read_csv(f'../data/fa_latest/ADD_VNINDEX.csv')
    # additonal_df['time'] = pd.to_datetime(additonal_df['time'], format='%m/%d/%Y')
    # df = pd.concat([df, additonal_df], ignore_index=True, axis=0).drop_duplicates(subset=['time'],
    #                                                                               keep='first').sort_values(
    #     'time', ascending=True).reset_index(drop=True)
    #
    # df.to_csv(f'VNINDEX.csv', index=False)
    # industry = vnsto.get_historical_index('VNINDEX')
    # stock = Vnstock().stock(symbol='VNINDEX', source='VCI')
    # stock.quote.history(start='2024-01-01', end='2025-03-19', interval='1D')
    # stock.finance.balance_sheet(period='year', lang='vi', dropna=True)
    # induyearstry = vnsto.get_finance_data('AAS', period='year')
    pass
    # industrsy = vnsto.symbols_by_group('CT')

    # list_index = ["VNINDEX", "VN30", "HNXINDEX", "HNX30", "UPCOMINDEX"]

    # data = vnstock.get_historical_ticker('HPG')
    # now = now_time.strftime("%Y-%m")
    # df = data.query(f'time.str.startswith("{now}")').reset_index(drop=True)
    # now = now_time.strftime("%Y-%m")
    # df_q = df.query(f'time.str.startswith("{now}")').reset_index(drop=True)

    # vnstock.update_ticker("HPG")
    # stock_data = vnstock.get_finance_ratio_vci()
    # s = vnstock.get_finance_cashflow("ABC")
    # pass
