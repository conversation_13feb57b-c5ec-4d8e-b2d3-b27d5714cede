import os
import sys
import traceback
from datetime import timedelta

import numpy as np
import pandas as pd
from joblib import Memory

from core_utils.base_eval import BaseEval
from core_utils.constant import JOBLIB_CACHE_DIR, REDIS_HOST
from core_utils.redis_cache import EvalRedis

memory = Memory(location=f'{JOBLIB_CACHE_DIR}_tuning', verbose=0)
memory.reduce_size(bytes_limit=3e9, age_limit=timedelta(days=1))
redis_cache = EvalRedis(host=REDIS_HOST, db=1)

current_dir = '/home/<USER>/dev/ta/kaffa_v2'
os.chdir(current_dir)
sys.path.insert(0, current_dir)

# 1. Load filter.json
FILTER_PATH = 'report/filter.json'
TICKER_PATH = 'ticker_v1a/'
SAVE_PATH = 'deeplearning/dl_train.csv'


class WeightEval(BaseEval):
    def __init__(self, stock, data_frame, dict_filter, weight, threshold_buy, threshold_sell, cutloss=0.15, lookback=5,
                 k_exp=0.0, cache_service=None):
        super().__init__(stock=stock, data_frame=data_frame, dict_filter=dict_filter, cutloss=cutloss,
                         cache_service=cache_service)

        self.weight = self._init_weight(weight, dict_filter, threshold_sell, threshold_buy)
        self.threshold_buy = threshold_buy
        self.threshold_sell = threshold_sell
        self.lookback = lookback
        self.exp = k_exp
        self.percentiles = self._init_percentiles()
        self.overheat_df = self._overheat()

    @staticmethod
    def _init_weight(w, dict_filter, threshold_sell, threshold_buy):
        if isinstance(w, dict) and w != {}:
            w['Hold'] = threshold_sell
        else:
            w = {'Hold': threshold_sell}
            for k, _ in dict_filter.items():
                if k.startswith('~'):
                    w[k[1:]] = threshold_sell
                if k.startswith('_'):
                    w[k[1:]] = threshold_buy
        return w

    def _init_percentiles(self):
        df_vnindex = self.df_all.copy()
        p3m = 100 * (df_vnindex['O3M'] - 1)

        percentiles = [0.1, 0.2, 0.8, 0.85, 0.9, 0.95]
        dict_percentiles = {
            'P3M': p3m.quantile(percentiles).values,
            'VNINDEX_PE': df_vnindex['VNINDEX_PE'].quantile(percentiles).values,
            'PE_PE_MA5Y': (df_vnindex['VNINDEX_PE'] / df_vnindex['VNINDEX_PE_MA5Y']).quantile(percentiles).values,
        }

        for i in range(60, 720, 5):
            colname = f'P{i}'
            ser = 100 * (df_vnindex['Open_1D'].shift(-i) / df_vnindex['Open_1D'] - 1)
            dict_percentiles[f'~{colname}'] = ser.quantile(percentiles).values

        df_percentiles = pd.DataFrame(dict_percentiles, index=[f'{int(p * 100)}%' for p in percentiles])

        return df_percentiles

    def _overheat(self):
        df_vni = self.df_all.copy()
        df_vni['P3M'] = 100 * (df_vni['O3M'] - 1)
        df_vni['s_time'] = pd.to_datetime(df_vni['time'])
        df_vni['max_High_2Y'] = df_vni['High'].rolling(240 * 2, min_periods=240).max()

        try:
            pd_all = df_vni.query(f"P3M >= {self.percentiles.loc['90%', 'P3M']}").copy()

            pd_all['start_group'] = pd_all['s_time']
            pd_all['end_group'] = pd_all['s_time']
            # Chuẩn hoá df_vnindex để tra cứu
            df_base = df_vni[['s_time', 'time', 'Open_1D', 'Close', 'max_High_2Y']].copy()
            df_base = df_base.sort_values('s_time').reset_index(drop=True)

            for i_group in range(pd_all.shape[0]):
                row_a = pd_all.iloc[i_group]
                start_group = row_a['start_group']
                try:
                    start_index = df_base.index[df_base['s_time'] == start_group][0]
                except:
                    start_index = pd.merge_asof(
                        pd.DataFrame({'s_time': [start_group]}).sort_values('s_time'),
                        df_base[['s_time']].sort_values('s_time'),
                        on='s_time', direction='backward'
                    ).index[0]

                break_count = 0

                for per in self.percentiles.columns:
                    if not per.startswith('~'):
                        continue

                    session = per.split('P')[1]
                    try:
                        cur = df_base.iloc[start_index + int(session)]['Open_1D']
                        base = df_base.iloc[start_index]['Open_1D']
                        ret = (cur / base - 1) * 100

                        if ret <= self.percentiles.loc['90%', per]:
                            break_count += 1
                        else:
                            break_count = 0

                        if break_count == 4:
                            break
                    except Exception as error:
                        break

                try:
                    session_break = int(session) - break_count * 5
                    offset_dates = df_base.iloc[start_index + int(session_break)]['time']
                except:
                    offset_dates = df_base.iloc[-1]['time']

                pd_all.at[pd_all.index[i_group], 'end_group'] = offset_dates

            pd_all['month'] = pd.to_datetime(pd_all['s_time'].dt.strftime('%Y-%m'))

            # Group consecutive months (month_diff <= 1)
            pd_all['month_diff'] = round(pd_all['month'].diff().dt.days / 31).fillna(0)
            pd_all['group'] = (pd_all['month_diff'] > 1).cumsum()
            pd_all['start_group'] = pd_all.groupby('group')['start_group'].transform('min')
            pd_all['end_group'] = pd_all.groupby('group')['end_group'].transform('max')
            pd_all = pd_all.drop_duplicates(subset=['group'], keep='first')

            # # Group consecutive group (group_diff <= 1)
            pd_all['group_diff'] = round((pd_all['start_group'] - pd_all['end_group'].shift(1)).dt.days / 31).fillna(0)
            pd_all['group'] = (pd_all['group_diff'] > 1).cumsum()
            pd_all['start_group'] = pd_all.groupby('group')['start_group'].transform('min')
            pd_all['end_group'] = pd_all.groupby('group')['end_group'].transform('max')
            pd_all = pd_all.drop_duplicates(subset=['group'], keep='first').reset_index(drop=True)

            ###########
            price_t = df_vni[['s_time', 'Close']].sort_values('s_time').reset_index(drop=True)
            times = price_t['s_time'].to_numpy()
            closes = price_t['Close'].to_numpy()

            def range_max(t1, t2):
                i = np.searchsorted(times, np.datetime64(t1), side='left')
                j = np.searchsorted(times, np.datetime64(t2), side='right') - 1
                if j < i:
                    return np.nan
                return np.nanmax(closes[i:j + 1])

            pd_all['max_Close'] = [range_max(s, e) for s, e in zip(pd_all['start_group'], pd_all['end_group'])]

            # exact map
            map_series = df_base.set_index('s_time')['max_High_2Y']
            pd_all['max_High_2Y'] = pd_all['start_group'].map(map_series)
            pd_all['start_close'] = pd_all['start_group'].map(df_base.set_index('s_time')['Close'])
            pd_all['end_close'] = pd_all['end_group'].map(df_base.set_index('s_time')['Close'])

            # fallback merge_asof (lấy giá trị trước đó gần nhất nếu không khớp exact)
            na_mask = pd_all['max_High_2Y'].isna()
            if na_mask.any():
                base_ch = df_base[['s_time', 'max_High_2Y', 'Close']].dropna().drop_duplicates('s_time',
                                                                                               keep='last').sort_values(
                    's_time')
                filled = pd.merge_asof(
                    pd_all.loc[na_mask, ['start_group']].sort_values('start_group'),
                    base_ch, left_on='start_group', right_on='s_time', direction='backward'
                )
                filled.index = pd_all.loc[na_mask].sort_values('start_group').index
                pd_all.loc[na_mask, 'max_High_2Y'] = filled['max_High_2Y'].values
                pd_all.loc[na_mask, 'start_close'] = filled['Close'].values

            # ==== Tính Cmax_H2Y ====
            pd_all['Cmax_H2Y'] = pd_all['max_Close'] / pd_all['max_High_2Y']

            return pd_all[['start_group', 'end_group', 'start_close', 'end_close', 'Cmax_H2Y']]
        except Exception as error:
            print(traceback.format_exc())
            pass

    def block_score(self, df, block_len: int = 30) -> pd.DataFrame:
        # Normalize time types for safe comparison

        df = df.copy()
        df['time'] = pd.to_datetime(df['time'])
        df_sell = self.df_sell.copy()
        df_sell['time'] = pd.to_datetime(df_sell['time'])
        df_all = self.df_all.copy()
        df_all['time'] = pd.to_datetime(df_all['time'])
        overheat_df = self.overheat_df.copy()
        if not overheat_df.empty:
            for col in ['start_group', 'end_group']:
                overheat_df[col] = pd.to_datetime(overheat_df[col])

        # 1 Filter sell signals endwith "~". and get first day of each month
        df_sell_filtered = (
            df_sell[df_sell["Sell_filter"].str.endswith("~", na=False)]
            .sort_values("time")
            .assign(month=lambda x: x['time'].dt.to_period('M'))
            .drop_duplicates(subset=['month'], keep='first')
            .drop(columns=['month'])
        )

        # Merge VNINDEX_PE to df_sell_filtered
        df_sell_filtered = df_sell_filtered.merge(
            df_all[['time', 'VNINDEX_PE']],
            on='time',
            how='left',
            validate='one_to_one'
        ).rename(columns={'VNINDEX_PE': 'index_pe'})

        # Merge overheat_df to df_sell_filtered
        df_sell_filtered = pd.merge_asof(
            df_sell_filtered.sort_values('time'),
            overheat_df[['start_group', 'end_group', 'start_close', 'end_close']].sort_values('end_group'),
            left_on='time', right_on='start_group', direction='backward'
        ).sort_values('time')

        try:
            pe_85 = float(self.percentiles.loc['85%', 'VNINDEX_PE'])
            # block_pe = float(self.percentiles.loc['20%', 'VNINDEX_PE'])
            block_pe = float(self.percentiles.loc['10%', 'VNINDEX_PE'])
        except Exception:
            pe_85 = 16.87
            # block_pe = 13.07
            block_pe = 12.3

        df_all_pe = (
            df_all[['time', 'VNINDEX_PE']]
            .dropna(subset=['time', 'VNINDEX_PE'])
            .sort_values('time')
        )
        time_all = df_all_pe['time'].to_numpy(dtype='datetime64[ns]')
        pe_all = df_all_pe['VNINDEX_PE'].to_numpy(dtype=float)

        def extend_mask(base_start: pd.Timestamp, k_blocks: int = 1):
            # Default mask
            end_time = base_start + pd.Timedelta(days=block_len * k_blocks)
            if k_blocks == 1:
                print(base_start, end_time)
                return (df["time"] >= base_start) & (df["time"] < end_time)

            # Find PE <= pe_20 in [base_start, end_time)
            if len(time_all) > 0:
                # Find id of base_start and end_time
                left = np.searchsorted(time_all,
                                       np.datetime64(base_start.tz_localize(None) if base_start.tzinfo else base_start),
                                       side='left')
                right = np.searchsorted(time_all, np.datetime64(end_time), side='left')

                if left < right:
                    window_pe = pe_all[left:right]
                    hit_idx_rel = np.nonzero(window_pe <= block_pe)[0]
                    if hit_idx_rel.size > 0:
                        # Find first time PE <= pe_20
                        cut_time = pd.to_datetime(time_all[left + hit_idx_rel[0]])
                        end_time = cut_time  # [base_start, cut_time)
            print(base_start, end_time)
            return (df["time"] >= base_start) & (df["time"] < end_time)

        for _, sell_row in df_sell_filtered.iterrows():
            sell_time = sell_row['time']
            sell_close = sell_row['Close']
            index_pe = sell_row['index_pe']
            start_group = sell_row['start_group']
            end_group = sell_row['end_group']
            start_close = sell_row['start_close']
            # Default mask
            mask = extend_mask(sell_time, k_blocks=1)

            if pd.notna(index_pe) and index_pe >= pe_85:
                if pd.notna(start_group) and pd.notna(end_group):
                    delta_date = (sell_time - end_group).days

                    # In case within overheat period
                    if start_group <= sell_time <= end_group:
                        mask = extend_mask(sell_time, 9)

                    # In case within 1 year after overheating
                    elif 0 < delta_date < 365 and pd.notna(start_close) and start_close > 0:
                        profit_hit2start = 100.0 * (sell_close / start_close - 1.0)

                        delta_days = (sell_time - start_group).days
                        delta_time = int(round(delta_days / 5) * 5)
                        colname = f'~P{delta_time}'

                        if ('90%' in self.percentiles.index) and (colname in self.percentiles.columns):
                            p90_val = self.percentiles.loc['90%', colname]
                            if pd.notna(p90_val) and profit_hit2start >= float(p90_val):
                                mask = extend_mask(sell_time, 9)

            df.loc[mask, 'score'] = self.threshold_sell

        return df


if __name__ == "__main__":
    vnindex_df = pd.read_csv('ticker_v1a/VNINDEX.csv')
    d_filters = {
        "~BearDvgVNI1~": "(time>='2014-01-01') & (time<='2026-01-01') & (ticker=='VNINDEX') & (D_RSI_Max1W/D_RSI > 1.044)  & (D_RSI_Max3M > 0.74) & (D_RSI_Max1W < 0.72) & (D_RSI_Max1W>0.61) & (D_RSI_Max1W_Close/D_RSI_Max3M_Close > 1.028) & (D_RSI_Max3M_MACD/D_RSI_Max1W_MACD>1.11) & (D_MACDdiff < 0)  & ( Close/D_RSI_Max3M_Close > 0.96) & (D_RSI_MinT3 > 0.43) & (D_CMF < 0.13)",
        "~BearDvgVNI2~": "(time>='2014-01-01') & (time<='2026-01-01') & (ticker=='VNINDEX') & (D_RSI_Max1W/D_RSI > 1.016)  & (D_RSI_Max3M > 0.77) & (D_RSI_Max1W < 0.79) & (D_RSI_Max1W>0.6) & (D_RSI_Max1W_Close/D_RSI_Max3M_Close > 1.008) & (D_RSI_Max3M_MACD/D_RSI_Max1W_MACD>1.1) & (D_MACDdiff < 0)  & ( Close/D_RSI_Max3M_Close > 0.97) & (D_RSI_MinT3 > 0.5) & (D_CMF < 0.15)"
    }

    evaluator = WeightEval(
        stock='VNINDEX',
        data_frame=vnindex_df.copy(),
        dict_filter=d_filters,
        weight={},
        threshold_buy=1,
        threshold_sell=-1,
        cutloss=0.15,
        lookback=10,
        k_exp=0.1,
        cache_service=redis_cache
    )

    pd_weight_block = evaluator.block_score(pd_weight)
    mask = (pd_weight_block['time'] == '2025-08-15')
    pd_weight_block.loc[mask, 'score'] = -1
    pd_weight_block.to_csv('core_utils/test_weight_score_deal_block_9m_10.csv', index=False)
