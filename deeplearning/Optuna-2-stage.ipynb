#%%
import os
import sys
from pathlib import Path

# from tuning.sell.exp_sell import threshold

current_dir = '/home/<USER>/dev/ta/kaffa_v2'
os.chdir(current_dir)
sys.path.insert(0, current_dir)
outdir = Path('deeplearning/outputs')
(outdir / "models").mkdir(parents=True, exist_ok=True)
(outdir / "metrics").mkdir(exist_ok=True)
(outdir / "predictions").mkdir(exist_ok=True)
#%%
# Load the necessary libraries
import random

random.seed(123)
from joblib import Memory

from core_utils.constant import JOBLIB_CACHE_DIR

from datetime import timedelta
import pandas as pd
import seaborn as sns
%matplotlib inline
import warnings

warnings.simplefilter(action='ignore')
memory = Memory(location=f'{JOBLIB_CACHE_DIR}_tuning', verbose=0)
memory.reduce_size(bytes_limit=3e9, age_limit=timedelta(days=1))
# redis_cache = EvalRedis(host=REDIS_HOST, db=1)

# exec(open('deeplearning/map_financial.py', 'r', encoding='utf-8').read())
from deeplearning.utils import *

#%%
def label_strength_4(row, label, thres_2=10, thres_1=0, thres_0=-10):
    """
    Determine the strength of a label based on profit thresholds.
    Args:
        row (pd.Series): A row from the DataFrame containing profit data.
        label (str): The label to evaluate (e.g., '2W', '1M', '3M').
        strong_th (float): Threshold for strong label.
        weak_th (float): Threshold for weak label.
        fail_th (float): Threshold for fail label.

    Returns:
        str: 0:3
        - 0: Fail
        - 1: Weak
        - 2: Strong
        - 3: Very Strong

    """
    v = row[f'profit_{label}']
    if v is None:
        return np.nan
    if v >= thres_2:
        return 3
    elif v >= thres_1:
        return 2
    elif v >= thres_0:
        return 1
    else:
        return 0


def label_strength_3(row, label, strong_th=10, weak_th=0):
    """
    Determine the strength of a label based on profit thresholds.
    Args:
        row (pd.Series): A row from the DataFrame containing profit data.
        label (str): The label to evaluate (e.g., '2W', '1M', '3M').
        strong_th (float): Threshold for strong label.
        weak_th (float): Threshold for weak label.
        fail_th (float): Threshold for fail label.

    Returns:
        str: 0:2
        - 0: Fail
        - 1: Weak
        - 2: Strong

    """
    v = row[f'profit_{label}']
    if v is None:
        return np.nan
    if v >= strong_th:
        return 2
    elif v >= weak_th:
        return 1
    else:
        return 0


def label_binary(row, label, strong_th=5, ceil=True):
    if ceil:
        v = np.ceil(row[f'profit_{label}'])
    else:
        v = row[f'profit_{label}']

    if v is None:
        return np.nan
    if v >= strong_th:
        return 1
    else:
        return 0


def label_binary_v1(row, label, strong_th=5, ceil=True):
    center_dict = {
        '2W': 5,
        '1M': 10,
        '3M': 20
    }
    if ceil:
        v = np.ceil(row[f'profit_{label}_center_{center_dict[label]}'])
    else:
        v = row[f'profit_{label}_center_{center_dict[label]}']

    if v is None:
        return np.nan
    if v >= strong_th:
        return 1
    else:
        return 0


def label_binary_v2(row, label, strong_th=5, ceil=True):
    center_dict = {
        '2W': 3,
        '1M': 7,
        '3M': 15
    }
    if ceil:
        v = np.ceil(row[f'profit_{label}_center_{center_dict[label]}'])
    else:
        v = row[f'profit_{label}_center_{center_dict[label]}']

    if v is None:
        return np.nan
    if v >= strong_th:
        return 1
    else:
        return 0

#%%
def ensure_types(df, labels_tag):
    df = df.copy()
    for c in df.columns:
        if c in labels_tag:
            continue
        if pd.api.types.is_numeric_dtype(df[c]):
            df[c] = df[c].astype(np.float32)
    return df


chunks = []
for chunk in pd.read_csv("deeplearning/dl_train.csv", chunksize=50000):
    chunks.append(chunk)
df_data_all = pd.concat(chunks, ignore_index=True)

# df_data_all = pd.read_parquet(path_parquet)
profit_cols = [col for col in df_data_all.columns if col.startswith('profit_')]
cname_tvt = 'tvt'

df_data_all['week'] = pd.to_datetime(df_data_all['time']).dt.strftime('%Y-%W')
df_data_all['month'] = df_data_all['time'].map(lambda x: x[:7])
labels_tag = [cname_tvt, 'time', 'ticker', 'week', 'month'] + profit_cols
df_data_all = ensure_types(df_data_all, labels_tag)

df_data_all = df_data_all.sort_values(by=['ticker', 'time']).reset_index(drop=True)

# Load data and labels
thres_hold = 10

# for label in ['2W', '1M', '3M']:
for label in ['1M']:
    df_data_all[f'label_binary_{label}_ceil'] = df_data_all.apply(
        lambda row: label_binary(row, label, strong_th=thres_hold),
        axis=1)
    labels_tag.append(f'label_binary_{label}_ceil')

    df_data_all[f'label_binary_{label}_center_v1_ceil'] = df_data_all.apply(
        lambda row: label_binary_v1(row, label, strong_th=thres_hold), axis=1)
    labels_tag.append(f'label_binary_{label}_center_v1_ceil')

    df_data_all[f'label_binary_{label}_center_v2_ceil'] = df_data_all.apply(
        lambda row: label_binary_v2(row, label, strong_th=thres_hold), axis=1)
    labels_tag.append(f'label_binary_{label}_center_v2_ceil')

    df_data_all[f'label_binary_{label}'] = df_data_all.apply(
        lambda row: label_binary(row, label, strong_th=thres_hold, ceil=False),
        axis=1)
    labels_tag.append(f'label_binary_{label}')

    df_data_all[f'label_binary_{label}_center_v1'] = df_data_all.apply(
        lambda row: label_binary_v1(row, label, strong_th=thres_hold, ceil=False), axis=1)
    labels_tag.append(f'label_binary_{label}_center_v1')

    df_data_all[f'label_binary_{label}_center_v2'] = df_data_all.apply(
        lambda row: label_binary_v2(row, label, strong_th=thres_hold, ceil=False), axis=1)
    labels_tag.append(f'label_binary_{label}_center_v2')

    # # multi 3
    # df_data_all[f'label_{label}_8'] = df_data_all.apply(lambda row: label_strength_3(row, label, strong_th=8), axis=1)
    # labels_tag.append(f'label_{label}_8')
    # df_data_all[f'label_{label}_12'] = df_data_all.apply(lambda row: label_strength_3(row, label,strong_th=12), axis=1)
    # labels_tag.append(f'label_{label}_12')
    # df_data_all[f'label_{label}_26'] = df_data_all.apply(lambda row: label_strength_3(row, label, strong_th=26), axis=1)
    # labels_tag.append(f'label_{label}_26')
    # #multi 4
    # df_data_all[f'label_{label}_-6_1_9'] = df_data_all.apply(lambda row: label_strength_4(row, label, thres_2=9, thres_1=1, thres_0=-6), axis=1)
    # labels_tag.append(f'label_{label}_-6_1_9')
    # df_data_all[f'label_{label}_-9_2_15'] = df_data_all.apply(lambda row: label_strength_4(row, label,thres_2=15, thres_1=2, thres_0=-9), axis=1)
    # labels_tag.append(f'label_{label}_-9_2_15')
    # df_data_all[f'label_{label}_-15_7_33'] = df_data_all.apply(lambda row: label_strength_4(row, label, thres_2=33, thres_1=2, thres_0=-9), axis=1)
    # labels_tag.append(f'label_{label}_-15_7_33')


# drops_1 = ['MA20_T1', 'MA200_T1', 'MA10', 'D_RSI_Max1W_MACD', 'D_CMB', 'Close_T1W', 'D_MACDdiff', 'D_CMB_XFast', 'D_RSI/D_RSI_T1', 'D_RSI/D_RSI_T1W']
# labels_tag = labels_tag + drops
# df_data_all.drop_duplicates(subset=['week', 'ticker'], keep='first', inplace=True)
#%%
print("Data shape:", df_data_all.shape)
data_is_null = df_data_all.isnull()
mean_null = data_is_null.mean()

drop_cols = []
for col in df_data_all.columns:
    if mean_null[col] > 0.15 and col not in labels_tag:
        drop_cols.append(col)

df_data_all.drop(columns=drop_cols, inplace=True)
print("Data shape:", df_data_all.shape)

#%%
# df_data_all = df_data_all.drop(drop_cols, axis=1)
df_data_all = df_data_all.replace([np.inf, -np.inf], np.nan).dropna()

df_data_all = df_data_all.dropna(axis=0, how='any')
df_data_all = split_tvt(df_data_all, test_size=10, time_col='time', train_cutoff='2022-06-01', cal_cutoff='2023-01-01',
                        val_cutoff='2023-06-01')


#%%
df_data = df_data_all.drop_duplicates(subset=['month', 'ticker'], keep='first').copy()
# df_data = df_data_all.drop_duplicates(subset=['week', 'ticker'], keep='first').copy()
# df_data = df_data_all.copy()
# df_data.to_csv('deeplearning/dl_train.csv', index=False)
#%% md

#%%
data_train, data_test1, data_test2, data_cal, data_val = (
    df_data[df_data[cname_tvt] == 'train'].reset_index(drop=True), \
    df_data[df_data[cname_tvt] == 'test1'].reset_index(drop=True),
    df_data[df_data[cname_tvt] == 'test2'].reset_index(drop=True),
    df_data[df_data[cname_tvt] == 'cal'].reset_index(drop=True),
    df_data[df_data[cname_tvt] == 'val'].reset_index(drop=True)
)
y_train_m, y_test1_m, y_test2_m, y_cal_m, y_val_m = data_train[labels_tag], data_test1[labels_tag], data_test2[
    labels_tag], data_cal[labels_tag], data_val[labels_tag]

X_train, X_test1, X_test2, X_cal, X_val = (
    data_train.drop(labels_tag, axis=1),
    data_test1.drop(labels_tag, axis=1),
    data_test2.drop(labels_tag, axis=1),
    data_cal.drop(labels_tag, axis=1),
    data_val.drop(labels_tag, axis=1)
)
X_train_final = X_train
X_val_final = X_val
X_cal_final = X_cal
X_test1_final = X_test1
X_test2_final = X_test2

print(X_train_final.shape, X_val_final.shape, X_cal_final.shape, X_test1_final.shape, X_test2_final.shape)


# Normalize
# scaler = StandardScaler()
# scaler.fit(X_train)

# X_train_scaled = scaler.transform(X_train)
# X_cal_scaled = scaler.transform(X_cal)
# X_val_scaled = scaler.transform(X_val)
# X_test1_scaled = scaler.transform(X_test1)
# X_test2_scaled = scaler.transform(X_test2)

# X_train_final = X_train_scaled
# X_val_final = X_val_scaled
# X_cal_final = X_cal_scaled
# X_test1_final = X_test1_scaled
# X_test2_final = X_test2_scaled
#%%
# ==== HPO với Optuna cho XGBoost + metric không cần threshold ====
import optuna
from optuna.samplers import TPESampler
from optuna.pruners import MedianPruner

import xgboost as xgb  # <-- nhớ import
from sklearn.metrics import (
    average_precision_score, roc_auc_score, log_loss, brier_score_loss
)

METRIC_MODE = "ap"  # "ap" | "roc_auc" | "neg_logloss" | "neg_brier" | "combo"


# def lr_schedule(iter_):
#     # iter_ tính theo số cây bổ sung ở giai đoạn 2
#     return 0.05 if iter_ < 200 else 0.01
# xgb.callback.LearningRateScheduler(lr_schedule)

def prob_metric(y_true, proba, mode="ap"):
    if mode == 'ap':
        return average_precision_score(y_true, proba)
    if mode == 'roc_auc':
        return roc_auc_score(y_true, proba)
    if mode == 'neg_logloss':
        return -log_loss(y_true, proba)
    if mode == 'neg_brier':
        return -brier_score_loss(y_true, proba)
    if mode == 'combo':
        ap = average_precision_score(y_true, proba)
        roc = roc_auc_score(y_true, proba)
        return 0.7 * ap + 0.3 * roc
    raise ValueError("METRIC_MODE không hợp lệ")


label_list_mc = ['label_binary_1M', 'label_binary_1M_center_v1', 'label_binary_1M_center_v2']

for lb in label_list_mc:
    print('total data distribution', df_data[f'{lb}'].value_counts())

    y_train = y_train_m[lb]
    y_val = y_val_m[lb]
    y_cal = y_cal_m[lb]
    y_test1 = y_test1_m[lb]
    y_test2 = y_test2_m[lb]

    print(f"Train {lb} distribution:", np.bincount(y_train))
    sns.countplot(x=y_train)
    plt.title("Label distribution")
    plt.show()

    # ======= sample_weight (imbalance) =======
    sample_weight = compute_sample_weight(class_weight='balanced', y=y_train)


    # ======= Optuna objective (DÙNG xgb.train để gắn callbacks) =======
    def objective(trial: optuna.trial.Trial):
        lr_grid = [0.0001, 0.001, 0.003, 0.005, 0.007, 0.01, 0.015, 0.02, 0.03, 0.05, 0.07, 0.10, 0.15, 0.20, 0.30]
        reg_grid = [1e-8, 1e-6, 1e-4, 1e-3, 0.01, 0.03, 0.1, 0.3, 1, 3, 10]
        mcw_grid = [2, 3, 4, 5, 7, 10, 12, 14]

        params = {
            "objective": "binary:logistic",
            "tree_method": trial.suggest_categorical("tree_method", ["hist"]),
            "learning_rate": trial.suggest_categorical("learning_rate", lr_grid),
            "max_depth": trial.suggest_int("max_depth", 3, 10, step=1),
            "min_child_weight": int(trial.suggest_categorical("min_child_weight", mcw_grid)),
            "subsample": trial.suggest_float("subsample", 0.55, 0.95, step=0.05),
            "colsample_bytree": trial.suggest_float("colsample_bytree", 0.55, 0.95, step=0.05),
            "reg_alpha": float(trial.suggest_categorical("reg_alpha", reg_grid)),
            "reg_lambda": float(trial.suggest_categorical("reg_lambda", reg_grid)),
            "max_bin": trial.suggest_categorical("max_bin", [128, 256, 512]),
            "gamma": float(trial.suggest_categorical("gamma", [0, 0.5, 1, 2, 3, 5, 7, 10])),

            # train params
            "eval_metric": "logloss",
            "seed": 42,
            "nthread": 20,  # cho xgb.train
            "verbosity": 0,
        }

        # DMatrix để gắn weight
        dtrain = xgb.DMatrix(X_train_final, label=y_train, weight=sample_weight)
        dval = xgb.DMatrix(X_val_final, label=y_val)
        evallist = [(dtrain, "train"), (dval, "validation_0")]

        bst = xgb.train(
            params=params,
            dtrain=dtrain,
            num_boost_round=10000,  # n_estimators lớn + early stopping cắt
            evals=evallist,
            callbacks=[
                XGBoostPruningCallback(trial, "validation_0-logloss"),
                EarlyStopping(rounds=200, save_best=True)
            ],
            verbose_eval=False
        )

        # predict proba cho VAL, chấm điểm theo METRIC_MODE
        proba_val = bst.predict(dval)
        score = prob_metric(y_val, proba_val, METRIC_MODE)
        return float(score)


    study = optuna.create_study(
        direction="maximize",
        sampler=TPESampler(seed=42),
        pruner=MedianPruner(n_warmup_steps=5)
    )
    study.optimize(objective, n_trials=200, n_jobs=20, show_progress_bar=False)

    print("\nBest score:", study.best_value)
    print("Best params:", study.best_trial.params)

    # ======= Train final với best params (sklearn API) =======
    best_params = study.best_trial.params.copy()
    best_params.update({
        'objective': 'binary:logistic',
        'n_estimators': 10000,
        'eval_metric': 'logloss',
        'random_state': 42,
        'n_jobs': 20,
        'verbosity': 0,
        'early_stopping_rounds': 200
    })

    # Lưu ý: KHÔNG truyền callbacks vào XGBClassifier.fit()
    final_model = xgb.XGBClassifier(**best_params)
    final_model.fit(
        X_train_final, y_train,
        sample_weight=sample_weight,
        eval_set=[(X_val_final, y_val)],
        verbose=False
    )

    # ======= Đánh giá trên TRAIN/VAL (proba metrics, no threshold) =======
    print('=' * 80, f'[{lb}] TRAIN/VAL EVALUATION (proba metrics)', '=' * 80)
    for X_, y_, tag in [
        (X_train_final, y_train, "TRAIN"),
        (X_val_final, y_val, "VAL")
    ]:
        p_ = final_model.predict_proba(X_)[:, 1]
        ap = average_precision_score(y_, p_)
        auc = roc_auc_score(y_, p_)
        nll = -log_loss(y_, p_)
        nbri = -brier_score_loss(y_, p_)
        print(f"{tag}: AP={ap:.4f} | AUC={auc:.4f} | -LogLoss={nll:.4f} | -Brier={nbri:.4f}")

    # ======= Báo cáo phân phối + AUC (như cũ) =======
    base = len(y_train)
    target = y_train.sum()
    percent_target = 100 * target / base
    tag = '2M' if '2M' in lb else '3M' if '3M' in lb else '1M'
    auc_train = roc_auc_score(y_train, final_model.predict_proba(X_train_final)[:, 1])
    report = [{
        "Threshold": f"{lb}",
        "Base": f"{base / 1000:.0f}K",
        "Target": f"{target / 1000:.0f}K",
        "%target": f"{percent_target:.0f}%",
        "AUC": f"{auc_train * 100:.0f}%"
    }]
    report_df = pd.DataFrame(report)
    print(report_df.to_markdown(index=False))

    report_lift_table(data_train, y_train, final_model.predict_proba(X_train_final)[:, 1], profit_col=f'profit_{tag}')

    # ======= Feature importance + lưu model =======
    imp = feature_importance_df(final_model)
    joblib.dump(final_model, outdir / "models" / f"xgb_model_{lb}.joblib")
    final_model.get_booster().save_model(str(outdir / "models" / f"xgb_model_{lb}.json"))
    imp.to_csv(outdir / "models" / f"feature_importance_{lb}.csv", index=False)

    # ======= Calibration (sigmoid) trên CAL set =======
    print(f"[INFO] Calibrate ({lb})...")
    calibrator = CalibratedClassifierCV(final_model, method="sigmoid", cv="prefit")
    calibrator.fit(X_cal, y_cal)
    joblib.dump(calibrator, outdir / "models" / f"calibrator_{lb}.joblib")

    print("++++++++++++++++++++++++ TEST EVALUATION (no threshold) ++++++++++++++++++++++++")
    for X_test_final, y_test, data_test, tag_eval in [
        (X_test1_final, y_test1, data_test1, "TEST1"),
        (X_test2_final, y_test2, data_test2, "TEST2")
    ]:
        # p_test = calibrator.predict_proba(X_test_final)[:, 1]  # nếu muốn dùng calibrate
        p_test = final_model.predict_proba(X_test_final)[:, 1]

        ap = average_precision_score(y_test, p_test)
        auc = roc_auc_score(y_test, p_test)
        nll = -log_loss(y_test, p_test)
        nbri = -brier_score_loss(y_test, p_test)

        ks, ks_threshold = ks_score(y_test, p_test)
        print(
            f"{tag_eval}: AP={ap:.4f} | AUC={auc:.4f} | -LogLoss={nll:.4f} | -Brier={nbri:.4f} | KS={ks:.4f} @thr={ks_threshold:.4f}")

        base = len(y_test);
        target = y_test.sum();
        percent_target = 100 * target / base
        rep = [{"Threshold": f"{lb}", "Base": f"{base / 1000:.0f}K", "Target": f"{target / 1000:.0f}K",
                "%target": f"{percent_target:.0f}%", "AUC": f"{auc * 100:.0f}%"}]
        print(pd.DataFrame(rep).to_markdown(index=False))

        report_lift_table(data_test, y_test, p_test, profit_col=f'profit_{tag}')

#%%
# ==== HPO với Optuna cho XGBoost + metric không cần threshold ====
from optuna.integration import XGBoostPruningCallback
from xgboost.callback import EarlyStopping

import xgboost as xgb  # <-- nhớ import
from sklearn.metrics import (
    average_precision_score, roc_auc_score, log_loss, brier_score_loss
)

METRIC_MODE = "ap"  # "ap" | "roc_auc" | "neg_logloss" | "neg_brier" | "combo"


def prob_metric(y_true, proba, mode="ap"):
    if mode == 'ap':
        return average_precision_score(y_true, proba)
    if mode == 'roc_auc':
        return roc_auc_score(y_true, proba)
    if mode == 'neg_logloss':
        return -log_loss(y_true, proba)
    if mode == 'neg_brier':
        return -brier_score_loss(y_true, proba)
    if mode == 'combo':
        ap = average_precision_score(y_true, proba)
        roc = roc_auc_score(y_true, proba)
        return 0.7 * ap + 0.3 * roc
    raise ValueError("METRIC_MODE không hợp lệ")


label_list_mc = ['label_binary_1M', 'label_binary_1M_center_v1', 'label_binary_1M_center_v2']

for lb in label_list_mc:
    print(f"{'_--' * 50}")
    print('total data distribution', df_data[f'{lb}'].value_counts())

    y_train = y_train_m[lb]
    y_val = y_val_m[lb]
    y_cal = y_cal_m[lb]
    y_test1 = y_test1_m[lb]
    y_test2 = y_test2_m[lb]

    print(f"Train {lb} distribution:", np.bincount(y_train))
    sns.countplot(x=y_train)
    plt.title("Label distribution")
    plt.show()

    # ======= sample_weight (imbalance) =======
    sample_weight = compute_sample_weight(class_weight='balanced', y=y_train)

    print("\nBest score:", study.best_value)
    print("Best params:", study.best_trial.params)

    # ======= Train final với best params (sklearn API) =======
    best_params = study.best_trial.params.copy()
    best_params.update({
        'objective': 'binary:logistic',
        'n_estimators': 10000,
        'eval_metric': 'logloss',
        'random_state': 42,
        'n_jobs': 20,
        'verbosity': 0,
        'early_stopping_rounds': 200
    })

    # Lưu ý: KHÔNG truyền callbacks vào XGBClassifier.fit()
    final_model = xgb.XGBClassifier(**best_params)
    final_model.fit(
        X_train_final, y_train,
        sample_weight=sample_weight,
        eval_set=[(X_val_final, y_val)],
        verbose=False
    )

    # ======= Đánh giá trên TRAIN/VAL (proba metrics, no threshold) =======
    print('=' * 80, f'[{lb}] TRAIN/VAL EVALUATION (proba metrics)', '=' * 80)
    for X_, y_, tag in [
        (X_train_final, y_train, "TRAIN"),
        (X_val_final, y_val, "VAL")
    ]:
        p_ = final_model.predict_proba(X_)[:, 1]
        ap = average_precision_score(y_, p_)
        auc = roc_auc_score(y_, p_)
        nll = -log_loss(y_, p_)
        nbri = -brier_score_loss(y_, p_)
        print(f"{tag}: AP={ap:.4f} | AUC={auc:.4f} | -LogLoss={nll:.4f} | -Brier={nbri:.4f}")

    # ======= Báo cáo phân phối + AUC (như cũ) =======
    base = len(y_train)
    target = y_train.sum()
    percent_target = 100 * target / base
    tag = '2M' if '2M' in lb else '3M' if '3M' in lb else '1M'
    auc_train = roc_auc_score(y_train, final_model.predict_proba(X_train_final)[:, 1])
    report = [{
        "Threshold": f"{lb}",
        "Base": f"{base / 1000:.0f}K",
        "Target": f"{target / 1000:.0f}K",
        "%target": f"{percent_target:.0f}%",
        "AUC": f"{auc_train * 100:.0f}%"
    }]
    report_df = pd.DataFrame(report)
    print(report_df.to_markdown(index=False))

    report_lift_table(data_train, y_train, final_model.predict_proba(X_train_final)[:, 1], profit_col=f'profit_{tag}')

    # ======= Feature importance + lưu model =======
    imp = feature_importance_df(final_model)
    joblib.dump(final_model, outdir / "models" / f"xgb_model_{lb}.joblib")
    final_model.get_booster().save_model(str(outdir / "models" / f"xgb_model_{lb}.json"))
    imp.to_csv(outdir / "models" / f"feature_importance_{lb}.csv", index=False)

    # ======= Calibration (sigmoid) trên CAL set =======
    print(f"[INFO] Calibrate ({lb})...")
    calibrator = CalibratedClassifierCV(final_model, method="sigmoid", cv="prefit")
    calibrator.fit(X_cal, y_cal)
    joblib.dump(calibrator, outdir / "models" / f"calibrator_{lb}.joblib")

    print("++++++++++++++++++++++++ TEST EVALUATION (no threshold) ++++++++++++++++++++++++")
    for X_test_final, y_test, data_test, tag_eval in [
        (X_test1_final, y_test1, data_test1, "TEST1"),
        (X_test2_final, y_test2, data_test2, "TEST2")
    ]:
        # p_test = calibrator.predict_proba(X_test_final)[:, 1]  # nếu muốn dùng calibrate
        p_test = final_model.predict_proba(X_test_final)[:, 1]

        ap = average_precision_score(y_test, p_test)
        auc = roc_auc_score(y_test, p_test)
        nll = -log_loss(y_test, p_test)
        nbri = -brier_score_loss(y_test, p_test)

        ks, ks_threshold = ks_score(y_test, p_test)
        print(
            f"{tag_eval}: AP={ap:.4f} | AUC={auc:.4f} | -LogLoss={nll:.4f} | -Brier={nbri:.4f} | KS={ks:.4f} @thr={ks_threshold:.4f}")

        base = len(y_test);
        target = y_test.sum();
        percent_target = 100 * target / base
        rep = [{"Threshold": f"{lb}", "Base": f"{base / 1000:.0f}K", "Target": f"{target / 1000:.0f}K",
                "%target": f"{percent_target:.0f}%", "AUC": f"{auc * 100:.0f}%"}]
        print(pd.DataFrame(rep).to_markdown(index=False))

        report_lift_table(data_test, y_test, p_test, profit_col=f'profit_{tag}')

#%%
# 1) Hai pha HPO → HPO refine (coarse → fine)
#
# Mục tiêu: khoanh vùng nhanh, rồi mài kỹ trên window gần hiện tại.
# Dùng khi: dữ liệu dài nhiều năm, thị trường đổi regime.
#
# Stage 1 – Coarse (history dài):
#
# Train/val: dùng window dài hơn (ví dụ 2014–2021 train, 2022 val).
#
# Optuna TPE + pruning, space rộng (eta, depth, subsample, colsample, reg, gamma, max_bin).
#
# 100–200 trials để tìm vùng tốt (không phải điểm tốt nhất).
#
# Stage 2 – Fine (recent regime):
#
# Train/val: recent (ví dụ 2018–2022 train, 2023 val) hoặc rolling gần đây.
#
# Thu hẹp space quanh best params stage 1 (± delta nhỏ), tập trung vài tham số nhạy: learning_rate, reg_alpha/lambda, subsample/colsample, đôi khi max_depth.
#
# 50–150 trials tìm best hiện tại.
#
# Final:
#
# Train lại trên train_recent (gộp val_recent nếu muốn) + early stopping bằng 1 holdout nhỏ hoặc CV theo time-series.
#
# Calibrate trên cal như code của bạn.
#
# Đánh giá test1/test2.
#
# Ghép vào code:
#
# Giữ nguyên hàm Optuna ở trên. Chạy study1 = ... với (X_train_hist, y_train_hist, X_val_hist, y_val_hist).
#
# Lấy p1 = study1.best_trial.params. Tạo space stage 2: mỗi tham số đặt quanh p1 (ví dụ learning_rate ± 2–3x theo log-scale, max_depth ±1, etc.).
#
# Chạy study2 trên (X_train_recent, X_val_recent). Dùng study2.best_trial.params cho training final.
#%%
# # giả sử đã có X_train_hist, y_train_hist, X_val_hist, y_val_hist
# study1 = run_optuna_on_split(X_train_hist, y_train_hist, X_val_hist, y_val_hist, space="wide", n_trials=200)
#
# p1 = study1.best_trial.params
# def narrow_space(trial, base):
#     return {
#         "tree_method": trial.suggest_categorical("tree_method", [base["tree_method"]]),
#         "learning_rate": trial.suggest_float(
#             "learning_rate",
#             max(1e-3, base["learning_rate"]/3),
#             min(0.3,  base["learning_rate"]*3),
#             log=True
#         ),
#         "max_depth": trial.suggest_int("max_depth", max(3, base["max_depth"]-1), min(12, base["max_depth"]+1)),
#         "min_child_weight": trial.suggest_float("min_child_weight",
#             max(1.0, base["min_child_weight"]/2), min(10.0, base["min_child_weight"]*2), log=True),
#         "subsample": trial.suggest_float("subsample",
#             max(0.5, base["subsample"]-0.2), min(1.0, base["subsample"]+0.2)),
#         "colsample_bytree": trial.suggest_float("colsample_bytree",
#             max(0.5, base["colsample_bytree"]-0.2), min(1.0, base["colsample_bytree"]+0.2)),
#         "reg_alpha": trial.suggest_float("reg_alpha",
#             max(1e-8, base["reg_alpha"]/5), min(10.0, base["reg_alpha"]*5), log=True),
#         "reg_lambda": trial.suggest_float("reg_lambda",
#             max(1e-8, base["reg_lambda"]/5), min(10.0, base["reg_lambda"]*5), log=True),
#         "gamma": trial.suggest_float("gamma",
#             max(0.0, base["gamma"]-2.0), min(10.0, base["gamma"]+2.0)),
#         "max_bin": trial.suggest_categorical("max_bin", [128, 256, 512])
#     }
#
# # chạy stage 2 trên recent
# study2 = run_optuna_on_split(
#     X_train_recent, y_train_recent, X_val_recent, y_val_recent,
#     space=("narrow", p1, narrow_space), n_trials=120
# )
#
# best_params = study2.best_trial.params
# # train final trên recent (+ early stopping), calibrate, test như bạn đang làm

#%%
def flow1_coarse_fine(
        X_hist_tr, y_hist_tr, X_hist_va, y_hist_va,  # history window (dài)
        X_recent_tr, y_recent_tr, X_recent_va, y_recent_va,  # recent window (gần hiện tại)
        X_cal, y_cal,
        X_tests, y_tests, data_tests, test_tags,
        label_name, outdir,
        metric_mode="ap",
        n_trials_stage1=200,
        n_trials_stage2=120,
        use_calibrated=True
):
    # weight theo history (dùng cho HPO stage 1)
    sw_hist = compute_sample_weight('balanced', y_hist_tr)

    # === Stage 1: HPO space rộng trên history ===
    study1 = run_optuna_on_split(
        X_hist_tr, y_hist_tr, X_hist_va, y_hist_va,
        metric_mode=metric_mode, n_trials=n_trials_stage1, n_jobs=8,
        space="wide", sample_weight=sw_hist
    )
    p1 = study1.best_trial.params
    print("[Stage1] best params:", p1)

    # === Stage 2: HPO space hẹp quanh best params trên recent ===
    sw_recent = compute_sample_weight('balanced', y_recent_tr)
    study2 = run_optuna_on_split(
        X_recent_tr, y_recent_tr, X_recent_va, y_recent_va,
        metric_mode=metric_mode, n_trials=n_trials_stage2, n_jobs=8,
        space=("narrow", p1, default_narrow_space),
        sample_weight=sw_recent
    )
    best_params = study2.best_trial.params
    print("[Stage2] best params:", best_params)

    # === Train final trên recent + eval + save ===
    model = train_eval_save(
        best_params,
        X_recent_tr, y_recent_tr, X_recent_va, y_recent_va,
        X_cal, y_cal,
        X_tests, y_tests, data_tests, test_tags,
        label_name, outdir,
        metric_mode=metric_mode,
        use_calibrated=use_calibrated
    )
    return {"study1": study1, "study2": study2, "model": model}


# ví dụ tách train thành history & recent theo thời gian của team
# Tạo history split (sử dụng 70% đầu của train data)
lb = 'label_binary_1M_center_v2'
y_train = y_train_m[lb]
y_val = y_val_m[lb]
y_cal = y_cal_m[lb]
y_test1 = y_test1_m[lb]
y_test2 = y_test2_m[lb]

train_size = len(X_train_final)
hist_split = int(train_size * 0.7)

X_hist_tr, X_recent_tr = X_train_final.iloc[:hist_split], X_train_final.iloc[hist_split:]
y_hist_tr, y_recent_tr = y_train.iloc[:hist_split], y_train.iloc[hist_split:]

# Sử dụng val làm validation cho cả history và recent
X_hist_va, y_hist_va = X_val_final, y_val
X_recent_va, y_recent_va = X_val_final, y_val

# Example usage cho flow1_coarse_fine (uncomment để chạy)
print(f"\n{'=' * 60}")
print(f"EXAMPLE: 2-STAGE WORKFLOW - COARSE TO FINE")
print(f"{'=' * 60}")
print(f"History data shape: {X_hist_tr.shape}, Recent data shape: {X_recent_tr.shape}")
print(f"Label: {lb}")
print("Workflow: Stage 1 (wide search on history) → Stage 2 (narrow search on recent)")

# res1 = flow1_coarse_fine(
#     X_hist_tr, y_hist_tr, X_hist_va, y_hist_va,
#     X_recent_tr, y_recent_tr, X_recent_va, y_recent_va,
#     X_cal, y_cal,
#     [X_test1_final, X_test2_final], [y_test1, y_test2], [data_test1, data_test2], ["TEST1","TEST2"],
#     label_name=lb, outdir=outdir,
#     metric_mode="ap", n_trials_stage1=200, n_trials_stage2=120, use_calibrated=True
# )
#%%
# 2) Hai pha Fit tham số → Fine-tune theo thời gian (regime-aware)
#
# Mục tiêu: học “hình dạng” model từ lịch sử, sau đó tái khớp nhẹ theo dữ liệu mới.
# Dùng khi: shift mạnh, muốn ưu tiên recent mà vẫn giữ kiến thức cũ.
#
# Stage 1 – Fit tham số tổng quát (history dài):
#
# Chạy Optuna (space rộng) để chọn hyperparams ổn định.
#
# Train model với n_estimators lớn + early stopping.
#
# Stage 2 – Fine-tune recent:
#
# Option A (phổ biến, sạch): giữ nguyên hyperparams stage 1, re-tune rất ít tham số trên recent: learning_rate, reg_alpha, reg_lambda, subsample/colsample — hoặc chỉ giảm learning_rate rồi train lại từ đầu trên recent (không dùng threshold).
#
# Option B (tiếp tục boosting): dùng xgb_model=final_stage1.get_booster() để append thêm cây trên recent (sklearn API hỗ trợ). Phù hợp khi muốn “thêm trí nhớ” recent, nhưng cẩn thận overfit.
#
# model2 = xgb.XGBClassifier(**params_stage1, n_estimators=best_ntree + 200)
# model2.fit(X_recent, y_recent, xgb_model=final_stage1.get_booster(),
#            eval_set=[(X_val_recent, y_val_recent)],
#            early_stopping_rounds=100, verbose=False)
#
#
# Giữ cal cho calibration sau fine-tune.
#
# Final: dùng model sau Stage 2 để predict, rồi calibrate (Platt/Isotonic) trên cal.
#%%
# Option A (khuyên dùng): re-tune nhẹ vài tham số trên recent, train từ đầu.
def flow2_finetune_A(
        X_hist_tr, y_hist_tr, X_hist_va, y_hist_va,  # stage 1: history
        X_recent_tr, y_recent_tr, X_recent_va, y_recent_va,  # stage 2: recent
        X_cal, y_cal,
        X_tests, y_tests, data_tests, test_tags,
        label_name, outdir,
        metric_mode="ap",
        n_trials_stage1=200, n_trials_stage2=80,
        use_calibrated=True
):
    # Stage 1: HPO rộng trên history → params ổn định
    sw_hist = compute_sample_weight('balanced', y_hist_tr)
    study1 = run_optuna_on_split(
        X_hist_tr, y_hist_tr, X_hist_va, y_hist_va,
        metric_mode=metric_mode, n_trials=n_trials_stage1, n_jobs=8,
        space="wide", sample_weight=sw_hist
    )
    base_params = study1.best_trial.params
    print("[Stage1] base params:", base_params)

    # Stage 2: chỉ re-tune một nhóm nhỏ tham số trên recent
    def narrow_fn_small(trial, base):
        return {
            "tree_method": trial.suggest_categorical("tree_method", [base.get("tree_method", "hist")]),
            "learning_rate": trial.suggest_float("learning_rate",
                                                 max(1e-3, base["learning_rate"] / 3),
                                                 min(0.1, base["learning_rate"] * 2), log=True),
            "subsample": trial.suggest_float("subsample",
                                             max(0.5, base["subsample"] - 0.15), min(1.0, base["subsample"] + 0.15)),
            "colsample_bytree": trial.suggest_float("colsample_bytree",
                                                    max(0.5, base["colsample_bytree"] - 0.15),
                                                    min(1.0, base["colsample_bytree"] + 0.15)),
            "reg_alpha": trial.suggest_float("reg_alpha",
                                             max(1e-8, base["reg_alpha"] / 3), min(10.0, base["reg_alpha"] * 3),
                                             log=True),
            "reg_lambda": trial.suggest_float("reg_lambda",
                                              max(1e-8, base["reg_lambda"] / 3), min(10.0, base["reg_lambda"] * 3),
                                              log=True),
            "max_depth": trial.suggest_int("max_depth",
                                           max(3, base["max_depth"] - 1), min(12, base["max_depth"] + 1)),
            "min_child_weight": trial.suggest_float("min_child_weight",
                                                    max(1.0, base["min_child_weight"] / 2),
                                                    min(10.0, base["min_child_weight"] * 2), log=True),
            "gamma": trial.suggest_float("gamma",
                                         max(0.0, base["gamma"] - 1.0), min(10.0, base["gamma"] + 1.0)),
            "max_bin": trial.suggest_categorical("max_bin", [128, 256, 512])
        }

    sw_recent = compute_sample_weight('balanced', y_recent_tr)
    study2 = run_optuna_on_split(
        X_recent_tr, y_recent_tr, X_recent_va, y_recent_va,
        metric_mode=metric_mode, n_trials=n_trials_stage2, n_jobs=8,
        space=("narrow", base_params, narrow_fn_small),
        sample_weight=sw_recent
    )
    best_params = study2.best_trial.params
    print("[Stage2] tuned params:", best_params)

    # Train final trên recent + eval + save
    model = train_eval_save(
        best_params,
        X_recent_tr, y_recent_tr, X_recent_va, y_recent_va,
        X_cal, y_cal,
        X_tests, y_tests, data_tests, test_tags,
        label_name, outdir,
        metric_mode=metric_mode,
        use_calibrated=use_calibrated
    )
    return {"study1": study1, "study2": study2, "model": model}

#%%
# Option B (nâng cấp dần – cẩn thận overfit): tiếp tục boosting từ model stage 1 trên dữ liệu recent.
def flow2_finetune_B_continue_boosting(
        X_hist_tr, y_hist_tr, X_hist_va, y_hist_va,
        X_recent_tr, y_recent_tr, X_recent_va, y_recent_va,
        X_cal, y_cal,
        X_tests, y_tests, data_tests, test_tags,
        label_name, outdir,
        metric_mode="ap",
        n_trials_stage1=200,
        extra_trees=300,
        use_calibrated=True
):
    # Stage 1: HPO rộng → train final 1
    sw_hist = compute_sample_weight('balanced', y_hist_tr)
    study1 = run_optuna_on_split(
        X_hist_tr, y_hist_tr, X_hist_va, y_hist_va,
        metric_mode=metric_mode, n_trials=n_trials_stage1, n_jobs=8,
        space="wide", sample_weight=sw_hist
    )
    p1 = study1.best_trial.params.copy()
    p1.update({'objective': 'binary:logistic', 'n_estimators': 10000, 'eval_metric': 'logloss', 'random_state': 42,
               'n_jobs': -1})
    model1 = xgb.XGBClassifier(**p1)
    model1.fit(X_hist_tr, y_hist_tr, eval_set=[(X_hist_va, y_hist_va)], early_stopping_rounds=200, verbose=False)
    best_ntree = model1.best_ntree_limit

    # Stage 2: thêm cây trên recent
    p2 = p1.copy()
    p2['n_estimators'] = int(best_ntree + extra_trees)
    model2 = xgb.XGBClassifier(**p2)
    model2.fit(
        X_recent_tr, y_recent_tr,
        xgb_model=model1.get_booster(),  # tiếp tục từ model1
        eval_set=[(X_recent_va, y_recent_va)],
        early_stopping_rounds=100,
        verbose=False
    )

    # Calibration + test
    if use_calibrated:
        calibrator = CalibratedClassifierCV(model2, method="sigmoid", cv="prefit")
        calibrator.fit(X_cal, y_cal)
        pred_fn = lambda X: calibrator.predict_proba(X)[:, 1]
        joblib.dump(calibrator, outdir / "models" / f"calibrator_{label_name}.joblib")
    else:
        pred_fn = lambda X: model2.predict_proba(X)[:, 1]

    for tag, X_, y_ in [("TRAIN_RECENT", X_recent_tr, y_recent_tr), ("VAL_RECENT", X_recent_va, y_recent_va)]:
        p_ = pred_fn(X_)
        print(f"[{label_name}] {tag}: AP={average_precision_score(y_, p_):.4f} | "
              f"AUC={roc_auc_score(y_, p_):.4f} | -LogLoss={-log_loss(y_, p_):.4f} | "
              f"-Brier={-brier_score_loss(y_, p_):.4f}")

    joblib.dump(model2, outdir / "models" / f"xgb_model_{label_name}.joblib")
    model2.get_booster().save_model(str(outdir / "models" / f"xgb_model_{label_name}.json"))

    period_tag = '2M' if '2M' in label_name else '3M' if '3M' in label_name else '1M'
    for X_te, y_te, data_te, tg in zip(X_tests, y_tests, data_tests, test_tags):
        p_te = pred_fn(X_te)
        ap = average_precision_score(y_te, p_te)
        auc = roc_auc_score(y_te, p_te)
        nll = -log_loss(y_te, p_te)
        bri = -brier_score_loss(y_te, p_te)
        print(f"[{label_name}] {tg}: AP={ap:.4f} | AUC={auc:.4f} | -LogLoss={nll:.4f} | -Brier={bri:.4f}")
        report_lift_table(data_te, y_te, p_te, profit_col=f'profit_{period_tag}')

    return {"study1": study1, "model2": model2}

#%%
# ============================================================================
# HELPER FUNCTION ĐỂ CHẠY 2-STAGE WORKFLOWS DỄ DÀNG
# ============================================================================

def run_2stage_workflow(workflow_type="coarse_fine",
                        X_train_full=None, y_train_full=None,
                        X_val=None, y_val=None, X_cal=None, y_cal=None,
                        X_tests=None, y_tests=None, data_tests=None, test_tags=None,
                        label_name="", outdir=None,
                        history_ratio=0.7, n_trials_stage1=100, n_trials_stage2=50):
    """
    Helper function để chạy các 2-stage workflows một cách dễ dàng

    Args:
        workflow_type: "coarse_fine", "finetune_A", hoặc "continue_boosting"
        history_ratio: Tỷ lệ data dùng cho history (0.7 = 70% đầu)
        n_trials_stage1/2: Số trials cho mỗi stage (giảm để test nhanh)
    """
    # Tách history vs recent
    train_size = len(X_train_full)
    hist_split = int(train_size * history_ratio)

    X_hist_tr = X_train_full.iloc[:hist_split]
    y_hist_tr = y_train_full.iloc[:hist_split]
    X_recent_tr = X_train_full.iloc[hist_split:]
    y_recent_tr = y_train_full.iloc[hist_split:]

    # Validation chung
    X_hist_va, y_hist_va = X_val, y_val
    X_recent_va, y_recent_va = X_val, y_val

    print(f"\n🚀 Running {workflow_type.upper()} workflow")
    print(f"📊 Data split: History {X_hist_tr.shape} | Recent {X_recent_tr.shape}")
    print(f"🎯 Label: {label_name}")
    print(f"⚙️  Trials: Stage1={n_trials_stage1}, Stage2={n_trials_stage2}")

    if workflow_type == "coarse_fine":
        return flow1_coarse_fine(
            X_hist_tr, y_hist_tr, X_hist_va, y_hist_va,
            X_recent_tr, y_recent_tr, X_recent_va, y_recent_va,
            X_cal, y_cal, X_tests, y_tests, data_tests, test_tags,
            label_name, outdir, n_trials_stage1=n_trials_stage1, n_trials_stage2=n_trials_stage2
        )
    elif workflow_type == "finetune_A":
        return flow2_finetune_A(
            X_hist_tr, y_hist_tr, X_hist_va, y_hist_va,
            X_recent_tr, y_recent_tr, X_recent_va, y_recent_va,
            X_cal, y_cal, X_tests, y_tests, data_tests, test_tags,
            label_name, outdir, n_trials_stage1=n_trials_stage1, n_trials_stage2=n_trials_stage2
        )
    elif workflow_type == "continue_boosting":
        return flow2_finetune_B_continue_boosting(
            X_hist_tr, y_hist_tr, X_hist_va, y_hist_va,
            X_recent_tr, y_recent_tr, X_recent_va, y_recent_va,
            X_cal, y_cal, X_tests, y_tests, data_tests, test_tags,
            label_name, outdir, n_trials_stage1=n_trials_stage1
        )
    else:
        raise ValueError(f"Unknown workflow_type: {workflow_type}")

#%%
# ============================================================================
# EXAMPLE USAGE CHO CÁC 2-STAGE WORKFLOWS
# ============================================================================

# 🎯 CÁCH SỬ DỤNG DỄ DÀNG VỚI HELPER FUNCTION:

# 1) Coarse-to-Fine workflow (khuyên dùng cho most cases)
# result1 = run_2stage_workflow(
#     workflow_type="coarse_fine",
#     X_train_full=X_train_final, y_train_full=y_train,
#     X_val=X_val_final, y_val=y_val, X_cal=X_cal_final, y_cal=y_cal,
#     X_tests=[X_test1_final, X_test2_final],
#     y_tests=[y_test1, y_test2],
#     data_tests=[data_test1, data_test2],
#     test_tags=["TEST1", "TEST2"],
#     label_name=lb, outdir=outdir,
#     history_ratio=0.7, n_trials_stage1=50, n_trials_stage2=30  # Giảm trials để test nhanh
# )

# 2) Finetune workflow (cho regime shift mạnh)
# result2 = run_2stage_workflow(
#     workflow_type="finetune_A",
#     X_train_full=X_train_final, y_train_full=y_train,
#     X_val=X_val_final, y_val=y_val, X_cal=X_cal_final, y_cal=y_cal,
#     X_tests=[X_test1_final, X_test2_final],
#     y_tests=[y_test1, y_test2],
#     data_tests=[data_test1, data_test2],
#     test_tags=["TEST1", "TEST2"],
#     label_name=lb, outdir=outdir,
#     history_ratio=0.6, n_trials_stage1=50, n_trials_stage2=25
# )

# 3) Continue Boosting workflow (experimental)
# result3 = run_2stage_workflow(
#     workflow_type="continue_boosting",
#     X_train_full=X_train_final, y_train_full=y_train,
#     X_val=X_val_final, y_val=y_val, X_cal=X_cal_final, y_cal=y_cal,
#     X_tests=[X_test1_final, X_test2_final],
#     y_tests=[y_test1, y_test2],
#     data_tests=[data_test1, data_test2],
#     test_tags=["TEST1", "TEST2"],
#     label_name=lb, outdir=outdir,
#     history_ratio=0.7, n_trials_stage1=50
# )

print("✅ 2-stage workflows ready! Uncomment above code để chạy.")

#%%
# ============================================================================
# DATA SPLITTING VALIDATION & IMPROVEMENTS
# ============================================================================

def validate_time_series_split(df, time_col='time', tvt_col='tvt'):
    """
    Kiểm tra tính hợp lệ của time-series split để đảm bảo không có data leakage
    """
    print("🔍 VALIDATING TIME-SERIES SPLIT...")

    # 1. Kiểm tra overlap thời gian giữa các split
    splits = ['train', 'cal', 'val', 'test1', 'test2']
    time_ranges = {}

    for split in splits:
        if split in df[tvt_col].values:
            subset = df[df[tvt_col] == split]
            if len(subset) > 0:
                time_ranges[split] = {
                    'min': subset[time_col].min(),
                    'max': subset[time_col].max(),
                    'count': len(subset)
                }

    print("\n📅 TIME RANGES BY SPLIT:")
    for split, info in time_ranges.items():
        print(f"  {split:8}: {info['min']} → {info['max']} ({info['count']:,} samples)")

    # 2. Kiểm tra overlap
    print("\n⚠️  CHECKING FOR TIME OVERLAPS:")
    overlaps_found = False

    # Train vs Cal/Val
    if 'train' in time_ranges and 'cal' in time_ranges:
        if time_ranges['train']['max'] >= time_ranges['cal']['min']:
            print(f"  ❌ OVERLAP: train max ({time_ranges['train']['max']}) >= cal min ({time_ranges['cal']['min']})")
            overlaps_found = True

    if 'cal' in time_ranges and 'val' in time_ranges:
        if time_ranges['cal']['max'] >= time_ranges['val']['min']:
            print(f"  ❌ OVERLAP: cal max ({time_ranges['cal']['max']}) >= val min ({time_ranges['val']['min']})")
            overlaps_found = True

    if not overlaps_found:
        print("  ✅ No time overlaps detected!")

    # 3. Kiểm tra ticker overlap
    print("\n🏢 CHECKING TICKER SEPARATION:")
    train_tickers = set(df[df[tvt_col] == 'train']['ticker'].unique()) if 'train' in df[tvt_col].values else set()
    test_tickers = set()
    for test_split in ['test1', 'test2']:
        if test_split in df[tvt_col].values:
            test_tickers.update(df[df[tvt_col] == test_split]['ticker'].unique())

    ticker_overlap = train_tickers.intersection(test_tickers)
    if ticker_overlap:
        print(f"  ❌ TICKER OVERLAP: {len(ticker_overlap)} tickers in both train and test")
        print(f"     Examples: {list(ticker_overlap)[:5]}")
    else:
        print("  ✅ No ticker overlap between train and test!")

    # 4. Summary
    print(f"\n📊 SPLIT SUMMARY:")
    print(f"  Total samples: {len(df):,}")
    for split in splits:
        if split in df[tvt_col].values:
            count = len(df[df[tvt_col] == split])
            pct = count / len(df) * 100
            print(f"  {split:8}: {count:6,} ({pct:5.1f}%)")

    return not overlaps_found and len(ticker_overlap) == 0


# Chạy validation
is_valid = validate_time_series_split(df_data_all, time_col='time', tvt_col='tvt')
print(f"\n🎯 OVERALL VALIDATION: {'✅ PASSED' if is_valid else '❌ FAILED'}")

#%%
# ============================================================================
# ADVANCED TIME-SERIES SPLITTING FOR 2-STAGE OPTIMIZATION
# ============================================================================

def create_rolling_splits(df, time_col='time', window_months=12, step_months=3):
    """
    Tạo rolling time-series splits cho robust validation
    Useful cho việc test stability của 2-stage optimization
    """
    df = df.copy()
    df[time_col] = pd.to_datetime(df[time_col])

    # Tìm time range
    min_time = df[time_col].min()
    max_time = df[time_col].max()

    splits = []
    current_start = min_time

    while current_start + pd.DateOffset(months=window_months) <= max_time:
        train_end = current_start + pd.DateOffset(months=window_months)
        val_start = train_end
        val_end = val_start + pd.DateOffset(months=3)  # 3 months validation

        if val_end <= max_time:
            splits.append({
                'train_start': current_start,
                'train_end': train_end,
                'val_start': val_start,
                'val_end': val_end,
                'name': f"split_{current_start.strftime('%Y%m')}_{train_end.strftime('%Y%m')}"
            })

        current_start += pd.DateOffset(months=step_months)

    return splits


def create_regime_aware_split(df, time_col='time', regime_change_dates=None):
    """
    Tạo splits dựa trên regime changes (market crashes, policy changes, etc.)
    Useful cho 2-stage optimization khi có structural breaks
    """
    if regime_change_dates is None:
        # Default regime changes cho VN market
        regime_change_dates = [
            '2020-03-01',  # COVID crash
            '2021-07-01',  # Post-COVID recovery
            '2022-01-01',  # Fed tightening
            '2023-01-01'  # Current regime
        ]

    df = df.copy()
    df[time_col] = pd.to_datetime(df[time_col])
    regime_dates = [pd.to_datetime(d) for d in regime_change_dates]

    regimes = []
    for i, date in enumerate(regime_dates):
        if i == 0:
            start_date = df[time_col].min()
        else:
            start_date = regime_dates[i - 1]

        end_date = date if i < len(regime_dates) else df[time_col].max()

        regime_data = df[(df[time_col] >= start_date) & (df[time_col] < end_date)]
        if len(regime_data) > 0:
            regimes.append({
                'regime_id': i,
                'start_date': start_date,
                'end_date': end_date,
                'data': regime_data,
                'name': f"regime_{i}_{start_date.strftime('%Y%m')}_{end_date.strftime('%Y%m')}"
            })

    return regimes


# Example usage:
print("🔄 CREATING ROLLING SPLITS...")
rolling_splits = create_rolling_splits(df_data_all, time_col='time', window_months=18, step_months=6)
print(f"Created {len(rolling_splits)} rolling splits")
for split in rolling_splits[:3]:  # Show first 3
    print(f"  {split['name']}: {split['train_start'].strftime('%Y-%m')} → {split['val_end'].strftime('%Y-%m')}")

print("\n📈 CREATING REGIME-AWARE SPLITS...")
regime_splits = create_regime_aware_split(df_data_all, time_col='time')
print(f"Created {len(regime_splits)} regime-based splits")
for regime in regime_splits:
    print(f"  {regime['name']}: {len(regime['data']):,} samples")

#%%
# ============================================================================
# CONFIGURATION MANAGEMENT CHO 2-STAGE OPTIMIZATION
# ============================================================================

import json
from datetime import datetime
from pathlib import Path


class OptimizationConfig:
    """
    Configuration manager cho 2-stage Optuna optimization
    Giúp track experiments và reproduce results
    """

    def __init__(self, config_name="default"):
        self.config_name = config_name
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # Default configurations
        self.configs = {
            "fast_test": {
                "description": "Quick test với ít trials",
                "stage1_trials": 20,
                "stage2_trials": 10,
                "history_ratio": 0.7,
                "metric_mode": "ap",
                "use_calibrated": True,
                "n_jobs": 4
            },
            "production": {
                "description": "Production setup với đầy đủ trials",
                "stage1_trials": 200,
                "stage2_trials": 120,
                "history_ratio": 0.7,
                "metric_mode": "ap",
                "use_calibrated": True,
                "n_jobs": 20
            },
            "regime_aware": {
                "description": "Regime-aware optimization cho market shifts",
                "stage1_trials": 150,
                "stage2_trials": 80,
                "history_ratio": 0.6,  # Ít history hơn, focus vào recent
                "metric_mode": "combo",  # AP + AUC combo
                "use_calibrated": True,
                "n_jobs": 16
            },
            "conservative": {
                "description": "Conservative setup cho stable models",
                "stage1_trials": 300,
                "stage2_trials": 150,
                "history_ratio": 0.8,  # Nhiều history hơn
                "metric_mode": "roc_auc",
                "use_calibrated": True,
                "n_jobs": 12
            }
        }

    def get_config(self, config_name):
        """Lấy configuration theo tên"""
        if config_name not in self.configs:
            raise ValueError(f"Unknown config: {config_name}. Available: {list(self.configs.keys())}")
        return self.configs[config_name].copy()

    def save_experiment_log(self, config_name, results, outdir):
        """Lưu log experiment để track"""
        log_data = {
            "timestamp": self.timestamp,
            "config_name": config_name,
            "config": self.get_config(config_name),
            "results": results,
            "git_commit": "unknown",  # Có thể thêm git hash
            "data_version": "v1.0"  # Version của data
        }

        log_file = Path(outdir) / "experiments" / f"exp_{self.timestamp}_{config_name}.json"
        log_file.parent.mkdir(exist_ok=True)

        with open(log_file, 'w') as f:
            json.dump(log_data, f, indent=2, default=str)

        print(f"💾 Experiment log saved: {log_file}")
        return log_file


# Initialize config manager
opt_config = OptimizationConfig()

# Show available configs
print("⚙️  AVAILABLE OPTIMIZATION CONFIGS:")
for name, config in opt_config.configs.items():
    print(f"  📋 {name:15}: {config['description']}")
    print(f"     Trials: {config['stage1_trials']}/{config['stage2_trials']}, "
          f"History: {config['history_ratio']:.1%}, "
          f"Metric: {config['metric_mode']}")

#%%
# ============================================================================
# PERFORMANCE MONITORING & COMPARISON TOOLS
# ============================================================================

def compare_optimization_strategies(results_dict, metric_name="AP"):
    """
    So sánh hiệu quả của các 2-stage strategies

    Args:
        results_dict: Dict với key là strategy name, value là results
        metric_name: Metric để so sánh (AP, AUC, etc.)
    """
    print(f"📊 COMPARING OPTIMIZATION STRATEGIES - {metric_name}")
    print("=" * 80)

    comparison_data = []

    for strategy_name, results in results_dict.items():
        # Extract metrics từ results (giả định structure)
        if isinstance(results, dict) and 'test_metrics' in results:
            test_metrics = results['test_metrics']
            train_time = results.get('train_time', 0)

            comparison_data.append({
                'Strategy': strategy_name,
                f'Test_{metric_name}': test_metrics.get(metric_name, 0),
                f'Val_{metric_name}': results.get('val_metrics', {}).get(metric_name, 0),
                'Train_Time_Min': train_time / 60,
                'Stage1_Trials': results.get('stage1_trials', 0),
                'Stage2_Trials': results.get('stage2_trials', 0),
                'Total_Trials': results.get('stage1_trials', 0) + results.get('stage2_trials', 0)
            })

    if comparison_data:
        comparison_df = pd.DataFrame(comparison_data)
        comparison_df = comparison_df.sort_values(f'Test_{metric_name}', ascending=False)

        print(comparison_df.to_markdown(index=False, floatfmt=".4f"))

        # Tính efficiency ratio
        if len(comparison_df) > 1:
            best_score = comparison_df[f'Test_{metric_name}'].max()
            comparison_df['Efficiency'] = comparison_df[f'Test_{metric_name}'] / comparison_df['Train_Time_Min']
            comparison_df['Score_Ratio'] = comparison_df[f'Test_{metric_name}'] / best_score

            print(f"\n🏆 EFFICIENCY ANALYSIS:")
            print(f"Best {metric_name}: {best_score:.4f}")
            print(f"Most Efficient: {comparison_df.loc[comparison_df['Efficiency'].idxmax(), 'Strategy']}")

        return comparison_df
    else:
        print("No results to compare")
        return None


def create_optimization_report(study1, study2, model, label_name, config_used):
    """
    Tạo comprehensive report cho 2-stage optimization
    """
    report = {
        'label_name': label_name,
        'config_used': config_used,
        'timestamp': datetime.now().isoformat(),

        # Stage 1 results
        'stage1': {
            'best_score': study1.best_value,
            'best_params': study1.best_trial.params,
            'n_trials': len(study1.trials),
            'optimization_time': sum(t.duration.total_seconds() for t in study1.trials if t.duration)
        },

        # Stage 2 results
        'stage2': {
            'best_score': study2.best_value,
            'best_params': study2.best_trial.params,
            'n_trials': len(study2.trials),
            'optimization_time': sum(t.duration.total_seconds() for t in study2.trials if t.duration)
        },

        # Model info
        'final_model': {
            'n_features': model.n_features_in_ if hasattr(model, 'n_features_in_') else 'unknown',
            'best_iteration': getattr(model, 'best_iteration', 'unknown')
        }
    }

    # Convergence analysis
    stage1_scores = [t.value for t in study1.trials if t.value is not None]
    stage2_scores = [t.value for t in study2.trials if t.value is not None]

    if stage1_scores:
        report['stage1']['convergence'] = {
            'improvement_rate': (max(stage1_scores) - stage1_scores[0]) / len(stage1_scores) if len(
                stage1_scores) > 1 else 0,
            'stability': np.std(stage1_scores[-10:]) if len(stage1_scores) >= 10 else np.std(stage1_scores)
        }

    if stage2_scores:
        report['stage2']['convergence'] = {
            'improvement_rate': (max(stage2_scores) - stage2_scores[0]) / len(stage2_scores) if len(
                stage2_scores) > 1 else 0,
            'stability': np.std(stage2_scores[-10:]) if len(stage2_scores) >= 10 else np.std(stage2_scores)
        }

    return report


print("📈 Performance monitoring tools ready!")

#%%
# ============================================================================
# FINAL RECOMMENDATIONS & BEST PRACTICES
# ============================================================================

def print_optimization_guide():
    """
    In ra hướng dẫn sử dụng 2-stage optimization
    """
    guide = """
🎯 2-STAGE OPTUNA OPTIMIZATION - BEST PRACTICES GUIDE
================================================================

📋 WHEN TO USE EACH WORKFLOW:

1️⃣  COARSE-TO-FINE (flow1_coarse_fine)
   ✅ Use when: Stable market conditions, long history available
   ✅ Best for: General purpose optimization
   ⚙️  Config: 'production' or 'conservative'

2️⃣  FINETUNE-A (flow2_finetune_A)
   ✅ Use when: Market regime shifts, need recent adaptation
   ✅ Best for: Volatile markets, structural breaks
   ⚙️  Config: 'regime_aware'

3️⃣  CONTINUE-BOOSTING (flow2_finetune_B)
   ✅ Use when: Want to preserve old knowledge + add new
   ⚠️  Warning: Risk of overfitting, use carefully
   ⚙️  Config: 'conservative' with extra monitoring

📊 RECOMMENDED WORKFLOW SELECTION:

Market Condition          | Recommended Workflow    | Config
---------------------------|------------------------|------------------
Stable, long history      | Coarse-to-Fine        | 'production'
Recent regime change       | Finetune-A            | 'regime_aware'
High volatility           | Finetune-A            | 'regime_aware'
Limited compute budget     | Coarse-to-Fine        | 'fast_test'
Research/experimentation   | All three + compare   | 'fast_test'

⚙️  HYPERPARAMETER TUNING TIPS:

Stage 1 (Coarse Search):
- Focus on: learning_rate, max_depth, regularization
- Trials: 150-300 (depending on budget)
- Data: Use longer history for stability

Stage 2 (Fine Search):
- Focus on: learning_rate, reg_alpha/lambda, subsample
- Trials: 50-150 (fewer than stage 1)
- Data: Use recent data for adaptation

🔍 VALIDATION STRATEGY:

1. Always use time-series split (no shuffle!)
2. Validate on multiple time periods if possible
3. Check for regime changes in your data
4. Monitor overfitting with early stopping
5. Use calibration for probability outputs

📈 PERFORMANCE MONITORING:

- Track both stages' convergence
- Compare multiple workflows on same data
- Monitor training time vs performance trade-off
- Save experiment logs for reproducibility

⚠️  COMMON PITFALLS TO AVOID:

❌ Using random split instead of time-series split
❌ Too many trials in stage 2 (overfitting risk)
❌ Ignoring market regime changes
❌ Not validating on out-of-sample data
❌ Forgetting to calibrate probabilities
❌ Not tracking experiment configurations

🚀 QUICK START COMMANDS:

# Fast test (5 minutes)
result = run_2stage_workflow("coarse_fine", X_train_final, y_train,
                            X_val_final, y_val, X_cal_final, y_cal,
                            [X_test1_final], [y_test1], [data_test1], ["TEST1"],
                            lb, outdir, n_trials_stage1=20, n_trials_stage2=10)

# Production run (30-60 minutes)
config = opt_config.get_config("production")
result = run_2stage_workflow("coarse_fine", X_train_final, y_train,
                            X_val_final, y_val, X_cal_final, y_cal,
                            [X_test1_final, X_test2_final], [y_test1, y_test2],
                            [data_test1, data_test2], ["TEST1", "TEST2"],
                            lb, outdir, **config)
"""
    print(guide)


# Print the guide
print_optimization_guide()

#Add loggings: Intergate voiws MLflow/ Wandb
#%%
