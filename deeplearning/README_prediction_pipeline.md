# Stock Prediction Pipeline

This pipeline automatically loads ticker data, creates features, loads prediction models, converts predictions to scores using ScoreMapperSimple, and saves results with multiprocessing for efficiency.

## Overview

The prediction pipeline consists of:

1. **Data Loading**: Automatically loads ticker CSV files from `ticker_v1a/`
2. **Feature Engineering**: Applies the same feature engineering as used in training
3. **Model Loading**: Loads trained XGBoost model from `deeplearning/outputs/models/`
4. **Score Conversion**: Uses ScoreMapperSimple to convert probabilities to scores [-1, 3]
5. **Multiprocessing**: Processes multiple tickers in parallel for efficiency
6. **Output**: Saves results with columns ['time', 'ticker', 'score']

## Files

- `prediction_pipeline.py` - Main pipeline implementation
- `run_prediction_example.py` - Example script showing how to use the pipeline
- `README_prediction_pipeline.md` - This documentation

## Requirements

### Prerequisites

1. **Trained Model**: You need a trained XGBoost model saved as `deeplearning/outputs/models/xgb_model.joblib`
   - Train a model using `exp_ml_dl.ipynb` first
   
2. **Ticker Data**: CSV files in `ticker_v1a/` directory with required columns:
   - Basic OHLCV data: time, Open, High, Low, Close, Volume
   - Technical indicators and fundamental data as used in training
   
3. **Dependencies**: 
   - pandas, numpy, scikit-learn, xgboost, joblib
   - pathos (for multiprocessing)
   - Core utilities from the project

### Data Format

Ticker CSV files should have columns matching those used during model training, including:
- time, ticker, Close, Volume, PE, PB, ROIC_Trailing, etc.
- The pipeline will handle missing columns gracefully

## Usage

### Basic Usage

```python
from deeplearning.prediction_pipeline import PredictionPipeline

# Initialize pipeline
pipeline = PredictionPipeline()

# Run prediction pipeline
pipeline.run_pipeline(output_file='my_predictions.csv')
```

### Command Line Usage

```bash
# Basic usage
python deeplearning/prediction_pipeline.py

# With custom parameters
python deeplearning/prediction_pipeline.py \
    --model-path deeplearning/outputs/models/my_model.joblib \
    --output-file custom_predictions.csv \
    --num-processes 10
```

### Example Script

```bash
# Run the example script
python deeplearning/run_prediction_example.py
```

## Configuration

### Pipeline Parameters

- `model_path`: Path to trained XGBoost model (default: `deeplearning/outputs/models/xgb_model.joblib`)
- `score_mapper_path`: Optional path to pre-fitted score mapper
- `num_processes`: Number of parallel processes (default: 20)

### Paths

- `TICKER_PATH`: Directory containing ticker CSV files (default: `ticker_v1a/`)
- `MODEL_PATH`: Directory containing model files (default: `deeplearning/outputs/models/`)
- `OUTPUT_PATH`: Directory for output files (default: `deeplearning/predictions/`)

## Output Format

The pipeline generates a CSV file with three columns:

| Column | Type | Description |
|--------|------|-------------|
| time | string | Date/time of the prediction |
| ticker | string | Stock ticker symbol |
| score | float | Prediction score in range [-1, 3] |

### Score Interpretation

- **Score < 0**: Negative outlook
- **Score ≈ 1**: Neutral outlook  
- **Score > 2**: Positive outlook
- **Score > 2.5**: Strong positive outlook

## Performance

- **Multiprocessing**: Uses configurable number of processes for parallel ticker processing
- **Memory Management**: Includes memory management and caching optimizations
- **Error Handling**: Graceful handling of missing data and processing errors

## Troubleshooting

### Common Issues

1. **Model Not Found**
   - Ensure you've trained a model using `exp_ml_dl.ipynb`
   - Check the model path is correct

2. **No Ticker Data**
   - Verify ticker CSV files exist in `ticker_v1a/`
   - Check file format and required columns

3. **Feature Engineering Errors**
   - Ensure ticker data has required columns for feature computation
   - Check for missing fundamental data

4. **Memory Issues**
   - Reduce `num_processes` parameter
   - Process tickers in smaller batches

### Debug Mode

For debugging, you can process a single ticker:

```python
pipeline = PredictionPipeline()
pipeline.load_model()
pipeline.setup_score_mapper()
result = pipeline.process_ticker('VNM')  # Replace with actual ticker
print(result)
```

## Integration

This pipeline is designed to integrate with:

- **Training Pipeline**: Uses same feature engineering as `prepare_data.py`
- **Model Training**: Compatible with models from `exp_ml_dl.ipynb`
- **Score Mapping**: Uses ScoreMapperSimple for consistent scoring
- **Production Systems**: Can be scheduled for regular prediction updates

## Example Output

```
time,ticker,score
2024-01-01,VNM,1.85
2024-01-01,VIC,2.31
2024-01-01,VHM,0.92
2024-01-02,VNM,1.78
...
```

This pipeline provides a complete solution for automated stock prediction scoring with the same methodology used in model training.
