{"label_column": "label_binary_1M", "cv_scores": {"roc_auc": [0.6196514506985547], "pr_auc": [0.18994236862061586], "log_loss": [0.4391127073923797], "brier_score": [0.1375380286386937]}, "cv_mean_std": {"roc_auc": {"mean": 0.6196514506985547, "std": 0.0}, "pr_auc": {"mean": 0.18994236862061586, "std": 0.0}, "log_loss": {"mean": 0.4391127073923797, "std": 0.0}, "brier_score": {"mean": 0.1375380286386937, "std": 0.0}}, "model_params": {"objective": "binary:logistic", "eval_metric": "logloss", "n_estimators": 800, "max_depth": 6, "learning_rate": 0.05, "subsample": 0.8, "colsample_bytree": 0.8, "reg_lambda": 1.0, "min_child_weight": 3, "random_state": 42, "n_jobs": -1, "verbosity": 0}, "training_time_avg": 31.4291934967041, "inference_time_avg": 0.05936598777770996, "n_features": 102, "n_samples": 50478}