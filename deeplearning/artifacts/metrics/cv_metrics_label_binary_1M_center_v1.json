{"label_column": "label_binary_1M_center_v1", "cv_scores": {"roc_auc": [0.6448471981131932], "pr_auc": [0.19269316977725012], "log_loss": [0.42335355921774837], "brier_score": [0.13174129681802732]}, "cv_mean_std": {"roc_auc": {"mean": 0.6448471981131932, "std": 0.0}, "pr_auc": {"mean": 0.19269316977725012, "std": 0.0}, "log_loss": {"mean": 0.42335355921774837, "std": 0.0}, "brier_score": {"mean": 0.13174129681802732, "std": 0.0}}, "model_params": {"objective": "binary:logistic", "eval_metric": "logloss", "n_estimators": 800, "max_depth": 6, "learning_rate": 0.05, "subsample": 0.8, "colsample_bytree": 0.8, "reg_lambda": 1.0, "min_child_weight": 3, "random_state": 42, "n_jobs": -1, "verbosity": 0}, "training_time_avg": 55.927032232284546, "inference_time_avg": 0.05546998977661133, "n_features": 102, "n_samples": 50478}