#%%
import os
import sys
from pathlib import Path

# from tuning.sell.exp_sell import threshold

current_dir = '/home/<USER>/dev/ta/kaffa_v2'
os.chdir(current_dir)
sys.path.insert(0, current_dir)
outdir = Path('deeplearning/outputs')
(outdir / "models").mkdir(parents=True, exist_ok=True)
(outdir / "metrics").mkdir(exist_ok=True)
(outdir / "predictions").mkdir(exist_ok=True)
#%%
# Load the necessary libraries
import random

random.seed(123)
from joblib import Memory
import joblib

from core_utils.constant import JOBLIB_CACHE_DIR, REDIS_HOST

from core_utils.redis_cache import EvalRedis
from datetime import timedelta
import pandas as pd
import seaborn as sns
%matplotlib inline
from sklearn.preprocessing import StandardScaler
import numpy as np
import xgboost as xgb
from sklearn.metrics import classification_report
from sklearn.model_selection import RandomizedSearchCV
from sklearn.utils.class_weight import compute_sample_weight
from sklearn.metrics import accuracy_score, f1_score, roc_auc_score
# from ydata_profiling import ProfileReport
from sklearn.metrics import confusion_matrix
# import tensorflow as tf
# print(f'TensorFlow version: {tf.__version__}')
from sklearn.calibration import CalibratedClassifierCV
# from sklearn.utils._estimator_html_repr import FrozenEstimator
import warnings

warnings.simplefilter(action='ignore')
memory = Memory(location=f'{JOBLIB_CACHE_DIR}_tuning', verbose=0)
memory.reduce_size(bytes_limit=3e9, age_limit=timedelta(days=1))
redis_cache = EvalRedis(host=REDIS_HOST, db=1)


#%%

def proba_to_score(p, score0=600.0, odds0=20.0, pdo=50.0, eps=1e-12):
    """
    Map xác suất (0..1) sang score theo công thức scorecard:
        factor = PDO / ln(2)
        offset = Score0 - factor * ln(odds0)
        score = offset + factor * ln(p / (1-p))
    - score0: điểm tại odds0 (P0 = odds0 / (1+odds0))
    - pdo: Points to Double the Odds (mỗi +pdo điểm => odds x2)
    - eps: để tránh log(0)
    """
    p = np.clip(np.asarray(p, dtype=float), eps, 1.0 - eps)
    factor = pdo / np.log(2.0)
    offset = score0 - factor * np.log(odds0)
    return offset + factor * np.log(p / (1.0 - p))


def score_to_proba(score, score0=600.0, odds0=20.0, pdo=50.0):
    """
    Hàm nghịch đảo: score -> proba
    """
    factor = pdo / np.log(2.0)
    offset = score0 - factor * np.log(odds0)
    logit = (np.asarray(score, dtype=float) - offset) / factor
    odds = np.exp(logit)
    return odds / (1.0 + odds)
#%%
def show_confusion_matrix(y_true, y_pred_prob, class_names=None, normalize=None, figsize=(6, 6)):
    import numpy as np
    import matplotlib.pyplot as plt
    from sklearn.metrics import confusion_matrix, ConfusionMatrixDisplay
    if len(y_pred_prob.shape) > 1:
        y_pred = np.argmax(y_pred_prob, axis=1)
    else:
        y_pred = y_pred_prob
    cm = confusion_matrix(y_true, y_pred, normalize=normalize)
    print("Confusion Matrix:")
    print(cm)
    plt.figure(figsize=figsize)
    disp = ConfusionMatrixDisplay(confusion_matrix=cm, display_labels=class_names)
    disp.plot(cmap='Blues', values_format='.2f' if normalize else 'd', ax=plt.gca(), colorbar=False)
    plt.title('Confusion Matrix' + (f' (normalized: {normalize})' if normalize else ''))
    plt.show()


def ks_score(y_true, y_proba):
    from sklearn.metrics import roc_curve
    """
    Tính và hiển thị KS score
    y_true  : Ground truth labels (0 or 1)
    y_proba : Xác suất mô hình dự đoán cho class 1
    KS Score	Mức phân tách	Diễn giải
    0.00–0.20	Rất yếu 😢	Mô hình gần như không phân biệt được class 0 và 1
    0.20–0.40	Trung bình 😐	Mô hình có chút khả năng phân biệt
    0.40–0.70	Tốt 💪	Mô hình phân tách tốt, áp dụng thực tế được
    > 0.70	Rất tốt 🚀	Hiếm khi gặp, cần kiểm tra overfitting
    """
    fpr, tpr, thresholds = roc_curve(y_true, y_proba)
    ks = max(tpr - fpr)
    ks_threshold = thresholds[np.argmax(tpr - fpr)]

    return ks, ks_threshold


def report_lift_table(df_test, y_test, y_pred_proba, profit_col='profit_3M'):
    df_lift = df_test.copy()
    # Thêm label và score
    df_lift['y_true'] = y_test
    df_lift['y_prob'] = y_pred_proba

    df_lift = df_lift.sort_values('y_prob', ascending=False).reset_index(drop=True)

    total_positives = df_lift['y_true'].sum()
    baseline_rate = total_positives / df_lift.shape[0]

    # Thêm profit và ticker nếu có, nếu không thì tạo cột dummy
    if profit_col not in df_lift.columns:
        df_lift[profit_col] = 0
    if 'ticker' not in df_lift.columns:
        df_lift['ticker'] = 'UNK'

    # Binning
    df_lift['bin'] = pd.qcut(df_lift['y_prob'], q=5, labels=['0-20', '20-40', '40-60', '60-80', '80-100'])

    # Group và tính toán
    lift_table = df_lift.groupby('bin', observed=False).agg(
        base=('y_true', 'count'),
        target=('y_true', 'sum'),
        pct_target=('y_true', 'mean'),
        average_Profit=(profit_col, 'mean'),
        ticker_nums=('ticker', pd.Series.nunique)
    ).reset_index()

    lift_table['%target'] = (lift_table['pct_target'] * 100).round(2)
    # lift_table = lift_table.drop('pct_target', axis=1)

    lift_table = lift_table.rename(columns={'bin': 'Bin score', 'average_Profit': 'average Profit'})

    lift_table['lift'] = lift_table['pct_target'] / baseline_rate

    print(lift_table[['Bin score', 'base', 'target', '%target', 'average Profit', 'ticker_nums', 'lift']].to_markdown(
        index=False))


def feature_importance_df(model):
    booster = model.get_booster()
    gain = booster.get_score(importance_type="gain")
    cover = booster.get_score(importance_type="cover")
    weight = booster.get_score(importance_type="weight")
    keys = set(gain.keys()) | set(cover.keys()) | set(weight.keys())
    rows = []
    for k in keys:
        rows.append({
            "feature": k,
            "gain": gain.get(k, 0.0),
            "cover": cover.get(k, 0.0),
            "weight": weight.get(k, 0.0),
        })
    return pd.DataFrame(rows).sort_values("gain", ascending=False)


def get_list_eval(pdxy, cname_tvt='tvt'):
    list_tvt = list(pdxy[cname_tvt].unique())
    i_train = pdxy[cname_tvt] == 'train'
    list_eval = [('train', i_train)]
    for tvt in list_tvt:
        if tvt.startswith('test'):
            i_test = pdxy[cname_tvt] == tvt
            list_eval.append((tvt, i_test))
    i_val = pdxy[cname_tvt] == 'val'
    list_eval.append(('val', i_val))
    return list_eval


def split_tvt(df, cname_tvt='tvt', time_col='time', test_size=10,
              train_cutoff='2022-06-01', val_cutoff='2023-01-01'):
    """
    Split into train/val/test sets.
    - 10% of tickers are assigned to test (any time within test_cutoff range)
    - train: time < train_cutoff
    - val: time in (train_cutoff + 100 days) to val_cutoff
    - test: test tickers in range (train_cutoff, test_cutoff]
    """
    df[time_col] = pd.to_datetime(df[time_col])
    train_cutoff = pd.to_datetime(train_cutoff)
    val_cutoff = pd.to_datetime(val_cutoff)

    list_ticker = list(df['ticker'].unique())
    random.seed(42)
    random.shuffle(list_ticker)

    test_tickers = [list_ticker[i] for i in range(len(list_ticker)) if i % 100 < test_size]

    is_test_ticker = df['ticker'].isin(test_tickers)
    is_train_val_ticker = ~is_test_ticker

    is_train_time = df[time_col] < train_cutoff
    is_cal_time = (df[time_col] >= train_cutoff) & (df[time_col] < val_cutoff)
    is_val_time = (df[time_col] > val_cutoff)

    # Assign tvt
    df[cname_tvt] = 'other'
    df.loc[is_train_val_ticker & is_train_time, cname_tvt] = 'train'
    df.loc[is_train_val_ticker & is_cal_time, cname_tvt] = 'cal'
    df.loc[is_train_val_ticker & is_val_time, cname_tvt] = 'val'
    df.loc[is_test_ticker & is_train_time, cname_tvt] = 'test_1'
    df.loc[is_test_ticker & is_val_time, cname_tvt] = 'test_2'

    print(df[cname_tvt].value_counts())
    return df


def split_tvt_v1(df, time_col='time', cname_tvt='tvt',
                 train_end='2022-06-01',
                 val_end='2022-12-31',
                 test_start='2023-01-01'):
    """
    split_time_only
    """
    df = df.copy()
    df[time_col] = pd.to_datetime(df[time_col])
    train_end = pd.to_datetime(train_end)
    val_end = pd.to_datetime(val_end)
    test_start = pd.to_datetime(test_start)

    df[cname_tvt] = 'other'
    df.loc[df[time_col] <= train_end, cname_tvt] = 'train'
    df.loc[(df[time_col] > train_end) & (df[time_col] <= val_end), cname_tvt] = 'val'
    df.loc[df[time_col] >= test_start, cname_tvt] = 'test'  # 2023+ làm test đúng bài
    return df


def walk_forward_split(df, time_col='time',
                      train_window_months=18,
                      val_window_months=3,
                      purge_months=1,
                      embargo_months=1,
                      step_months=3,
                      start_date='2020-01-01',
                      end_date='2023-12-31'):
    """
    Walk-forward validation with purge and embargo for time series data.

    Parameters:
    -----------
    df : pd.DataFrame
        Input dataframe with time series data
    time_col : str
        Name of the time column
    train_window_months : int
        Number of months to use for training
    val_window_months : int
        Number of months to use for validation
    purge_months : int
        Number of months to purge between train and validation (avoid look-ahead bias)
    embargo_months : int
        Number of months to embargo after validation (simulate trading delays)
    step_months : int
        Number of months to step forward for each fold
    start_date : str
        Start date for walk-forward validation
    end_date : str
        End date for walk-forward validation

    Returns:
    --------
    list of tuples: [(train_start, train_end, val_start, val_end, fold_id), ...]
    """

    df = df.copy()
    df[time_col] = pd.to_datetime(df[time_col])

    start_date = pd.to_datetime(start_date)
    end_date = pd.to_datetime(end_date)

    folds = []
    fold_id = 0

    current_train_start = start_date

    while True:
        # Calculate fold dates
        train_end = current_train_start + pd.DateOffset(months=train_window_months)
        purge_end = train_end + pd.DateOffset(months=purge_months)
        val_start = purge_end
        val_end = val_start + pd.DateOffset(months=val_window_months)
        embargo_end = val_end + pd.DateOffset(months=embargo_months)

        # Check if we have enough data for this fold
        if val_end > end_date:
            break

        # Check if we have enough data in the validation period
        val_data = df[(df[time_col] >= val_start) & (df[time_col] < val_end)]
        if len(val_data) < 100:  # Minimum validation samples
            break

        folds.append({
            'fold_id': fold_id,
            'train_start': current_train_start,
            'train_end': train_end,
            'purge_start': train_end,
            'purge_end': purge_end,
            'val_start': val_start,
            'val_end': val_end,
            'embargo_end': embargo_end
        })

        fold_id += 1
        current_train_start += pd.DateOffset(months=step_months)

    return folds


def apply_walk_forward_split(df, folds, time_col='time'):
    """
    Apply walk-forward splits to dataframe and return train/val sets for each fold.

    Parameters:
    -----------
    df : pd.DataFrame
        Input dataframe
    folds : list
        List of fold dictionaries from walk_forward_split
    time_col : str
        Name of the time column

    Returns:
    --------
    list of tuples: [(train_df, val_df, fold_info), ...]
    """

    df = df.copy()
    df[time_col] = pd.to_datetime(df[time_col])

    fold_data = []

    for fold in folds:
        # Get training data
        train_mask = (df[time_col] >= fold['train_start']) & (df[time_col] < fold['train_end'])
        train_df = df[train_mask].copy()

        # Get validation data (after purge period)
        val_mask = (df[time_col] >= fold['val_start']) & (df[time_col] < fold['val_end'])
        val_df = df[val_mask].copy()

        fold_info = {
            'fold_id': fold['fold_id'],
            'train_period': f"{fold['train_start'].strftime('%Y-%m')} to {fold['train_end'].strftime('%Y-%m')}",
            'val_period': f"{fold['val_start'].strftime('%Y-%m')} to {fold['val_end'].strftime('%Y-%m')}",
            'train_samples': len(train_df),
            'val_samples': len(val_df),
            'purge_months': (fold['val_start'] - fold['train_end']).days // 30,
        }

        fold_data.append((train_df, val_df, fold_info))

    return fold_data


def label_strength_4(row, label, thres_2=10, thres_1=0, thres_0=-10):
    """
    Determine the strength of a label based on profit thresholds.
    Args:
        row (pd.Series): A row from the DataFrame containing profit data.
        label (str): The label to evaluate (e.g., '2W', '1M', '3M').
        strong_th (float): Threshold for strong label.
        weak_th (float): Threshold for weak label.
        fail_th (float): Threshold for fail label.

    Returns:
        str: 0:3
        - 0: Fail
        - 1: Weak
        - 2: Strong
        - 3: Very Strong

    """
    v = row[f'profit_{label}']
    if v is None:
        return np.nan
    if v >= thres_2:
        return 3
    elif v >= thres_1:
        return 2
    elif v >= thres_0:
        return 1
    else:
        return 0


def label_strength_3(row, label, strong_th=10, weak_th=0):
    """
    Determine the strength of a label based on profit thresholds.
    Args:
        row (pd.Series): A row from the DataFrame containing profit data.
        label (str): The label to evaluate (e.g., '2W', '1M', '3M').
        strong_th (float): Threshold for strong label.
        weak_th (float): Threshold for weak label.
        fail_th (float): Threshold for fail label.

    Returns:
        str: 0:2
        - 0: Fail
        - 1: Weak
        - 2: Strong

    """
    v = row[f'profit_{label}']
    if v is None:
        return np.nan
    if v >= strong_th:
        return 2
    elif v >= weak_th:
        return 1
    else:
        return 0


def label_binary(row, label, strong_th=5):
    v = row[f'profit_{label}']
    if v is None:
        return np.nan
    if v >= strong_th:
        return 1
    else:
        return 0


def label_binary_v1(row, label, strong_th=5):
    center_dict = {
        '2W': 5,
        '1M': 10,
        '3M': 20
    }
    v = row[f'profit_{label}_center_{center_dict[label]}']
    if v is None:
        return np.nan
    if v >= strong_th:
        return 1
    else:
        return 0


def label_binary_v2(row, label, strong_th=5):
    center_dict = {
        '2W': 3,
        '1M': 7,
        '3M': 15
    }
    v = row[f'profit_{label}_center_{center_dict[label]}']
    if v is None:
        return np.nan
    if v >= strong_th:
        return 1
    else:
        return 0


def evaluate_walk_forward(df, folds, label_col, feature_cols, model_params=None):
    """
    Evaluate model using walk-forward validation with purge/embargo.

    Parameters:
    -----------
    df : pd.DataFrame
        Input dataframe with features and labels
    folds : list
        List of fold dictionaries from walk_forward_split
    label_col : str
        Name of the target label column
    feature_cols : list
        List of feature column names
    model_params : dict
        XGBoost model parameters

    Returns:
    --------
    dict: Results containing fold-wise and aggregated metrics
    """

    if model_params is None:
        model_params = {
            'tree_method': 'hist',
            'objective': 'binary:logistic',
            'n_jobs': -1,
            'eval_metric': ['auc'],
            'random_state': 42,
            'learning_rate': 0.05,
            'max_depth': 5,
            'min_child_weight': 5,
            'subsample': 0.8,
            'colsample_bytree': 0.8,
            'gamma': 0.05,
            'reg_lambda': 4,
            'reg_alpha': 0.5,
            'n_estimators': 400,
            'early_stopping_rounds': 40,
        }

    fold_results = []
    all_predictions = []
    all_true_labels = []

    scaler = StandardScaler()

    for fold_idx, (train_df, val_df, fold_info) in enumerate(apply_walk_forward_split(df, folds)):
        print(f"\n=== Fold {fold_info['fold_id']} ===")
        print(f"Train period: {fold_info['train_period']}")
        print(f"Val period: {fold_info['val_period']}")
        print(f"Train samples: {fold_info['train_samples']}, Val samples: {fold_info['val_samples']}")
        print(f"Purge period: {fold_info['purge_months']} months")

        # Prepare data
        X_train = train_df[feature_cols].copy()
        y_train = train_df[label_col].copy()
        X_val = val_df[feature_cols].copy()
        y_val = val_df[label_col].copy()

        # Remove any remaining NaN values
        train_mask = ~(X_train.isnull().any(axis=1) | y_train.isnull())
        val_mask = ~(X_val.isnull().any(axis=1) | y_val.isnull())

        X_train = X_train[train_mask]
        y_train = y_train[train_mask]
        X_val = X_val[val_mask]
        y_val = y_val[val_mask]

        if len(X_train) < 100 or len(X_val) < 50:
            print(f"Skipping fold {fold_info['fold_id']} due to insufficient data")
            continue

        # Scale features
        X_train_scaled = scaler.fit_transform(X_train)
        X_val_scaled = scaler.transform(X_val)

        # Handle class imbalance
        sample_weight = compute_sample_weight(class_weight='balanced', y=y_train)

        # Train model
        model = xgb.XGBClassifier(**model_params)
        model.fit(
            X_train_scaled, y_train,
            sample_weight=sample_weight,
            eval_set=[(X_val_scaled, y_val)],
            verbose=False
        )

        # Make predictions
        y_pred_proba = model.predict_proba(X_val_scaled)[:, 1]
        y_pred = (y_pred_proba >= 0.5).astype(int)

        # Calculate metrics
        acc = accuracy_score(y_val, y_pred)
        f1 = f1_score(y_val, y_pred)
        roc = roc_auc_score(y_val, y_pred_proba)
        ks, ks_threshold = ks_score(y_val, y_pred_proba)

        fold_result = {
            'fold_id': fold_info['fold_id'],
            'train_period': fold_info['train_period'],
            'val_period': fold_info['val_period'],
            'train_samples': fold_info['train_samples'],
            'val_samples': fold_info['val_samples'],
            'accuracy': acc,
            'f1_score': f1,
            'roc_auc': roc,
            'ks_score': ks,
            'ks_threshold': ks_threshold,
            'target_rate': y_val.mean()
        }

        fold_results.append(fold_result)
        all_predictions.extend(y_pred_proba)
        all_true_labels.extend(y_val)

        print(f"ACC={acc*100:.2f}% | F1={f1*100:.2f}% | ROC={roc*100:.2f}% | KS={ks:.3f}")

    # Calculate aggregated metrics
    if fold_results:
        aggregated_metrics = {
            'mean_accuracy': np.mean([r['accuracy'] for r in fold_results]),
            'mean_f1_score': np.mean([r['f1_score'] for r in fold_results]),
            'mean_roc_auc': np.mean([r['roc_auc'] for r in fold_results]),
            'mean_ks_score': np.mean([r['ks_score'] for r in fold_results]),
            'std_accuracy': np.std([r['accuracy'] for r in fold_results]),
            'std_f1_score': np.std([r['f1_score'] for r in fold_results]),
            'std_roc_auc': np.std([r['roc_auc'] for r in fold_results]),
            'std_ks_score': np.std([r['ks_score'] for r in fold_results]),
            'overall_roc_auc': roc_auc_score(all_true_labels, all_predictions),
            'num_folds': len(fold_results)
        }

        print(f"\n=== AGGREGATED RESULTS ===")
        print(f"Mean ROC-AUC: {aggregated_metrics['mean_roc_auc']:.3f} ± {aggregated_metrics['std_roc_auc']:.3f}")
        print(f"Mean F1: {aggregated_metrics['mean_f1_score']:.3f} ± {aggregated_metrics['std_f1_score']:.3f}")
        print(f"Mean KS: {aggregated_metrics['mean_ks_score']:.3f} ± {aggregated_metrics['std_ks_score']:.3f}")
        print(f"Overall ROC-AUC: {aggregated_metrics['overall_roc_auc']:.3f}")
        print(f"Number of folds: {aggregated_metrics['num_folds']}")
    else:
        aggregated_metrics = {}

    return {
        'fold_results': fold_results,
        'aggregated_metrics': aggregated_metrics,
        'all_predictions': all_predictions,
        'all_true_labels': all_true_labels
    }

#%%
def ensure_types(df, labels_tag):
    df = df.copy()
    for c in df.columns:
        if c in labels_tag:
            continue
        if pd.api.types.is_numeric_dtype(df[c]):
            df[c] = df[c].astype(np.float32)
    return df


chunks = []
for chunk in pd.read_csv("deeplearning/dl_train.csv", chunksize=50000):
    chunks.append(chunk)
df_data_all = pd.concat(chunks, ignore_index=True)

# df_data_all = pd.read_parquet(path_parquet)
profit_cols = [col for col in df_data_all.columns if col.startswith('profit_')]
cname_tvt = 'tvt'

df_data_all['week'] = pd.to_datetime(df_data_all['time']).dt.strftime('%Y-%W')
df_data_all['month'] = df_data_all['time'].map(lambda x: x[:7])
labels_tag = [cname_tvt, 'time', 'ticker', 'week', 'month'] + profit_cols
df_data_all = ensure_types(df_data_all, labels_tag)

df_data_all = df_data_all.sort_values(by=['ticker', 'time']).reset_index(drop=True)

# Load data and labels
thres_hold = 10

# for label in ['2W', '1M', '3M']:
for label in ['1M']:
    df_data_all[f'label_binary_{label}'] = df_data_all.apply(lambda row: label_binary(row, label, strong_th=thres_hold),
                                                             axis=1)
    labels_tag.append(f'label_binary_{label}')

    df_data_all[f'label_binary_{label}_center_v1'] = df_data_all.apply(
        lambda row: label_binary_v1(row, label, strong_th=thres_hold), axis=1)
    labels_tag.append(f'label_binary_{label}_center_v1')

    df_data_all[f'label_binary_{label}_center_v2'] = df_data_all.apply(
        lambda row: label_binary_v1(row, label, strong_th=thres_hold), axis=1)
    labels_tag.append(f'label_binary_{label}_center_v2')

    # # multi 3
    # df_data_all[f'label_{label}_8'] = df_data_all.apply(lambda row: label_strength_3(row, label, strong_th=8), axis=1)
    # labels_tag.append(f'label_{label}_8')
    # df_data_all[f'label_{label}_12'] = df_data_all.apply(lambda row: label_strength_3(row, label,strong_th=12), axis=1)
    # labels_tag.append(f'label_{label}_12')
    # df_data_all[f'label_{label}_26'] = df_data_all.apply(lambda row: label_strength_3(row, label, strong_th=26), axis=1)
    # labels_tag.append(f'label_{label}_26')
    # #multi 4
    # df_data_all[f'label_{label}_-6_1_9'] = df_data_all.apply(lambda row: label_strength_4(row, label, thres_2=9, thres_1=1, thres_0=-6), axis=1)
    # labels_tag.append(f'label_{label}_-6_1_9')
    # df_data_all[f'label_{label}_-9_2_15'] = df_data_all.apply(lambda row: label_strength_4(row, label,thres_2=15, thres_1=2, thres_0=-9), axis=1)
    # labels_tag.append(f'label_{label}_-9_2_15')
    # df_data_all[f'label_{label}_-15_7_33'] = df_data_all.apply(lambda row: label_strength_4(row, label, thres_2=33, thres_1=2, thres_0=-9), axis=1)
    # labels_tag.append(f'label_{label}_-15_7_33')


# drops_1 = ['MA20_T1', 'MA200_T1', 'MA10', 'D_RSI_Max1W_MACD', 'D_CMB', 'Close_T1W', 'D_MACDdiff', 'D_CMB_XFast', 'D_RSI/D_RSI_T1', 'D_RSI/D_RSI_T1W']
# labels_tag = labels_tag + drops
# df_data_all.drop_duplicates(subset=['week', 'ticker'], keep='first', inplace=True)
#%%
print("Data shape:", df_data_all.shape)
data_is_null = df_data_all.isnull()
mean_null = data_is_null.mean()

drop_cols = []
for col in df_data_all.columns:
    if mean_null[col] > 0.15 and col not in labels_tag:
        drop_cols.append(col)

df_data_all.drop(columns=drop_cols, inplace=True)
print("Data shape:", df_data_all.shape)

#%%
df_data_all = df_data_all.drop(drop_cols, axis=1)
df_data_all = df_data_all.replace([np.inf, -np.inf], np.nan).dropna()

df_data_all = df_data_all.dropna(axis=0, how='any')
# Keep the original split for final holdout test
df_data_all = split_tvt(df_data_all, test_size=10, time_col='time', train_cutoff='2022-06-01', cal_cutoff='2023-01-01',
                        val_cutoff='2023-06-01')

# Create walk-forward validation folds for robust evaluation
print("\n=== Setting up Walk-Forward Validation ===")
wf_folds = walk_forward_split(
    df_data_all,
    time_col='time',
    train_window_months=18,  # 18 months of training data
    val_window_months=3,     # 3 months of validation data
    purge_months=1,          # 1 month purge to avoid look-ahead bias
    embargo_months=1,        # 1 month embargo to simulate trading delays
    step_months=3,           # Move forward 3 months for each fold
    start_date='2020-01-01', # Start walk-forward from 2020
    end_date='2022-06-01'    # End before final test period
)

print(f"Created {len(wf_folds)} walk-forward folds:")
for fold in wf_folds:
    print(f"Fold {fold['fold_id']}: Train {fold['train_start'].strftime('%Y-%m')} to {fold['train_end'].strftime('%Y-%m')}, "
          f"Val {fold['val_start'].strftime('%Y-%m')} to {fold['val_end'].strftime('%Y-%m')}")

# Prepare final holdout test set (completely unseen data)
final_test_data = df_data_all[df_data_all['tvt'].isin(['test1', 'test2', 'val'])].copy()
print(f"\nFinal holdout test set: {len(final_test_data)} samples")


#%%
# df_data = df_data_all.drop_duplicates(subset=['month', 'ticker'], keep='first').copy()
df_data = df_data_all.drop_duplicates(subset=['week', 'ticker'], keep='first').copy()
# df_data = df_data_all.copy()
# df_data.to_csv('deeplearning/dl_train.csv', index=False)
#%%
# # Preprocess data
# N_JOBS = 10
# df_data = df_data_all.dropna(axis=0, how='any').copy()
#
# df_data = df_data.set_index(['ticker', 'time'])
#
# num_cols = df_data.select_dtypes(include=np.number).columns
# for col in ['profit_2W', 'profit_1M', 'profit_3M']:
#     df_data = df_data[(df_data[col] > -90) & (df_data[col] < 200)]
#
# skew_threshold = 1.0
# log_applied_cols = []
# for col in num_cols:
#     if col in labels_tag:
#         continue
#     skew_val = df_data[col].skew()
#     if abs(skew_val) > skew_threshold and df_data[col].min() >= 0:
#         df_data[col] = np.log1p(df_data[col])
#         log_applied_cols.append(col)
#
# for col in num_cols:
#     low = df_data[col].quantile(0.005)
#     high = df_data[col].quantile(0.995)
#     df_data[col] = df_data[col].clip(lower=low, upper=high)
#
# df_data = df_data.sample(frac=1).reset_index(drop=True)
#%%
# Preprocess data
# N_JOBS = 10
#
# df_data = df_data_all.dropna(axis=0, how='any').copy()
#
# df_data = df_data.set_index(['ticker', 'time'])

# df_data = df_data.unstack('ticker').stack('ticker')

# Loại outlier
# for col in ['profit_2W', 'profit_1M', 'profit_3M']:
#     df_data = df_data[(df_data[col] > -90) & (df_data[col] < 200)]

# # 3. Remove cổ phiếu có missing rate > 30%
# threshold = 0.3
# missing_rate = df_data.groupby('ticker').apply(lambda x: x.isna().mean().mean())
# keep_symbols = missing_rate[missing_rate < threshold].index
# df_data = df_data.loc[df_data.index.get_level_values('ticker').isin(keep_symbols)]

# # 6. Remove features tương quan cao >0.95
# num_cols = df_data.select_dtypes(include=np.number).columns
# corr_matrix = df_data[num_cols].corr().abs()
# upper = corr_matrix.where(np.triu(np.ones(corr_matrix.shape), k=1).astype(bool))
# to_drop = [column for column in upper.columns if any(upper[column] > 0.95)]
# df_data = df_data.drop(columns=to_drop)
#
# skew_threshold = 2.0
# log_applied_cols = []
# for col in num_cols and col not in labels_tag:
#     skew_val = df_data[col].skew()
#     if abs(skew_val) > skew_threshold and df_data[col].min() >= 0:
#         # df_data[col] = np.log1p(df_data[col])
#         log_applied_cols.append(col)
#
#  # 13. Loại extreme value từng feature (clip 1st-99th percentile)
# for col in num_cols:
#     low = df_data[col].quantile(0.005)
#     high = df_data[col].quantile(0.995)
#     df_data[col] = df_data[col].clip(lower=low, upper=high)

#%% md

#%%
df_data['Volume'].skew()
#%%
# Investigate the dataimport pandas as pd
import matplotlib.pyplot as plt

df = df_data_all.dropna(axis=0, how='any').copy()
df = df_data_all.drop_duplicates(subset=['month', 'ticker'], keep='first')
# 1. Vẽ phân phối profit cho từng time frame
plt.figure(figsize=(15, 5))
for idx, col in enumerate(['profit_2W', 'profit_1M', 'profit_3M']):
    plt.subplot(1, 3, idx + 1)
    plt.hist(df[col], bins=50)
    plt.title(col)
plt.tight_layout()
plt.show()

# 2. Loại outlier (tuỳ thuộc vào chart bên trên, tạm thời cut ở -50% và +50%)
for col in ['profit_2W', 'profit_1M', 'profit_3M']:
    df = df[(df[col] > -70) & (df[col] < 200)]


# 3. Chia label absolute profit cho từng time frame
def label_profit(x):
    if x < -15:
        return 0
    elif x < 7:
        return 1
    elif x < 33:
        return 2  # lời khá
    else:
        return 3  # lời mạnh


for col in ['profit_2W', 'profit_1M', 'profit_3M']:
    df[f'{col}_class'] = df[col].apply(label_profit)
    print(f"Phân phối label {col}_class:")
    print(df[f'{col}_class'].value_counts(), "\n")

# 4. Chia label theo percentile (quartile) cho từng time frame
for col in ['profit_2W', 'profit_1M', 'profit_3M']:
    # Chạy thử qcut không truyền label để biết số lượng bin thực tế
    try:
        quartile, bins = pd.qcut(df[col], q=2, retbins=True, duplicates='drop')
        num_bin = len(bins) - 1
        df[f'{col}_quartile'] = pd.qcut(df[col], q=2, labels=list(range(num_bin)), duplicates='drop')
        print(f"Phân phối label {col}_quartile:")
        print(df[f'{col}_quartile'].value_counts(), "\n")
        print(bins, "\n")
        # print(quartile)

    except Exception as e:
        print(f"Lỗi chia {col}: {e}")

# # Find insight
# # df_data = df_data_all.dropna(axis=0, how='any')
# label = ['label_2W', 'label_1M', 'label_3M']
# vi_count_unit = (df_data.nunique())
# vi_df_describe = (df_data.describe())
# vi_profit_2W_othernull = (df_data_all[df_data_all['label_2W'].notnull()].isnull().sum())
# vi_profit_1M_othernull = (df_data_all[df_data_all['label_1M'].notnull()].isnull().sum())
# vi_profit_4M_othernull = (df_data_all[df_data_all['label_3M'].notnull()].isnull().sum())
#
# for la in label:
#     # print(df_data_all[f'{la}'].value_counts())
#     print(df_data[f'{la}'].value_counts())
#%%
data_train, data_test1, data_test2, data_cal, data_val = (
    df_data[df_data[cname_tvt] == 'train'].reset_index(drop=True), \
    df_data[df_data[cname_tvt] == 'test1'].reset_index(drop=True),
    df_data[df_data[cname_tvt] == 'test2'].reset_index(drop=True),
    df_data[df_data[cname_tvt] == 'cal'].reset_index(drop=True),
    df_data[df_data[cname_tvt] == 'val'].reset_index(drop=True)
)
y_train_m, y_test1_m, y_test2_m, y_cal_m, y_val_m = data_train[labels_tag], data_test1[labels_tag], data_test2[
    labels_tag], data_cal[labels_tag], data_val[labels_tag]

X_train, X_test1, X_test2, X_cal, X_val = (
    data_train.drop(labels_tag, axis=1),
    data_test1.drop(labels_tag, axis=1),
    data_test2.drop(labels_tag, axis=1),
    data_cal.drop(labels_tag, axis=1),
    data_val.drop(labels_tag, axis=1)
)

# Normalize
scaler = StandardScaler()
scaler.fit(X_train)

X_train_scaled = scaler.transform(X_train)
X_cal_scaled = scaler.transform(X_cal)
X_val_scaled = scaler.transform(X_val)
X_test1_scaled = scaler.transform(X_test1)
X_test2_scaled = scaler.transform(X_test2)

X_train_final = X_train_scaled
X_val_final = X_val_scaled
X_cal_final = X_cal_scaled
X_test1_final = X_test1_scaled
X_test2_final = X_test2_scaled
#%%

#%%
# Walk-Forward Validation Training
print("\n" + "="*60)
print("WALK-FORWARD VALIDATION WITH PURGE/EMBARGO")
print("="*60)

label_list_mc = ['label_binary_1M', 'label_binary_1M_center_v1', 'label_binary_1M_center_v2']

# Get feature columns (exclude label and metadata columns)
feature_cols = [col for col in df_data.columns if col not in labels_tag]
print(f"Using {len(feature_cols)} features for training")

for lb in label_list_mc:
    print(f"\n{'='*50}")
    print(f"EVALUATING LABEL: {lb}")
    print(f"{'='*50}")

    # Check label distribution
    print('Total data distribution:', df_data[lb].value_counts())

    # Prepare data for walk-forward validation
    # Use data before the final test period for walk-forward validation
    wf_data = df_data_all[df_data_all['tvt'].isin(['train', 'cal'])].copy()
    wf_data = wf_data.dropna(subset=[lb])  # Remove rows with missing labels

    print(f"Walk-forward data shape: {wf_data.shape}")
    print(f"Walk-forward label distribution: {wf_data[lb].value_counts()}")

    # Run walk-forward validation
    wf_results = evaluate_walk_forward(
        df=wf_data,
        folds=wf_folds,
        label_col=lb,
        feature_cols=feature_cols,
        model_params={
            'tree_method': 'hist',
            'objective': 'binary:logistic',
            'n_jobs': -1,
            'eval_metric': ['auc'],
            'random_state': 42,
            'learning_rate': 0.05,
            'max_depth': 5,
            'min_child_weight': 5,
            'subsample': 0.8,
            'colsample_bytree': 0.8,
            'gamma': 0.05,
            'reg_lambda': 4,
            'reg_alpha': 0.5,
            'n_estimators': 400,
            'early_stopping_rounds': 40,
        }
    )

    # Train final model on all available training data for holdout test
    print(f"\n{'='*40}")
    print("TRAINING FINAL MODEL FOR HOLDOUT TEST")
    print(f"{'='*40}")

    # Use all training data (train + cal) for final model
    train_data = df_data_all[df_data_all['tvt'].isin(['train', 'cal'])].copy()
    train_data = train_data.dropna(subset=[lb])

    X_train_final = train_data[feature_cols]
    y_train_final = train_data[lb]

    # Remove any remaining NaN values
    train_mask = ~(X_train_final.isnull().any(axis=1) | y_train_final.isnull())
    X_train_final = X_train_final[train_mask]
    y_train_final = y_train_final[train_mask]

    # Scale features
    scaler_final = StandardScaler()
    X_train_final_scaled = scaler_final.fit_transform(X_train_final)

    # Handle class imbalance
    sample_weight = compute_sample_weight(class_weight='balanced', y=y_train_final)
    scale_pos_weight = sample_weight.max() / sample_weight.min()

    print(f"Final training data shape: {X_train_final_scaled.shape}")
    print(f"Final training label distribution: {np.bincount(y_train_final)}")

    # Use best parameters from walk-forward validation or default good parameters
    best_params = {
        'tree_method': 'hist',
        'objective': 'binary:logistic',
        'n_jobs': -1,
        'eval_metric': ['auc'],
        'random_state': 42,
        'learning_rate': 0.05,
        'max_depth': 5,
        'min_child_weight': 5,
        'subsample': 0.8,
        'colsample_bytree': 0.8,
        'gamma': 0.05,
        'reg_lambda': 4,
        'reg_alpha': 0.5,
        'n_estimators': 400,
        'early_stopping_rounds': 40,
    }

    # Train final model
    final_model = xgb.XGBClassifier(**best_params)
    final_model.fit(
        X_train_final_scaled, y_train_final,
        sample_weight=sample_weight,
        verbose=False
    )

    # ======= Test on training data (check for overfitting) ============
    y_pred_proba_train = final_model.predict_proba(X_train_final_scaled)[:, 1]
    y_pred_train = (y_pred_proba_train >= 0.5).astype(int)

    acc_train = accuracy_score(y_train_final, y_pred_train)
    f1_train = f1_score(y_train_final, y_pred_train)
    roc_train = roc_auc_score(y_train_final, y_pred_proba_train)
    ks_train, ks_threshold_train = ks_score(y_train_final, y_pred_proba_train)

    print(f"\nTraining Set Performance (Overfitting Check):")
    print(f"ACC={acc_train * 100:.2f}% | F1={f1_train * 100:.2f}% | ROC={roc_train * 100:.2f}%")
    print(f"KS_SCORE={ks_train:.2f} | KS_THRESHOLD={ks_threshold_train:.2f}")

    # Save model and feature importance
    imp = feature_importance_df(final_model)
    joblib.dump(final_model, outdir / "models" / f"xgb_model_{lb}.joblib")
    final_model.get_booster().save_model(str(outdir / "models" / f"xgb_model_{lb}.json"))
    imp.to_csv(outdir / "models" / f"feature_importance_{lb}.csv", index=False)

    print(f"Model and feature importance saved for {lb}")

    # ======= FINAL HOLDOUT TEST (COMPLETELY UNSEEN DATA) =======
    print(f"\n{'='*50}")
    print("FINAL HOLDOUT TEST - COMPLETELY UNSEEN DATA")
    print(f"{'='*50}")

    # Prepare holdout test data
    test_data = final_test_data.dropna(subset=[lb]).copy()
    X_test_holdout = test_data[feature_cols]
    y_test_holdout = test_data[lb]

    # Remove any remaining NaN values
    test_mask = ~(X_test_holdout.isnull().any(axis=1) | y_test_holdout.isnull())
    X_test_holdout = X_test_holdout[test_mask]
    y_test_holdout = y_test_holdout[test_mask]
    test_data_clean = test_data[test_mask]

    if len(X_test_holdout) > 0:
        # Scale test features using the same scaler
        X_test_holdout_scaled = scaler_final.transform(X_test_holdout)

        # Make predictions
        y_pred_proba_holdout = final_model.predict_proba(X_test_holdout_scaled)[:, 1]
        y_pred_holdout = (y_pred_proba_holdout >= 0.5).astype(int)

        # Calculate metrics
        acc_holdout = accuracy_score(y_test_holdout, y_pred_holdout)
        f1_holdout = f1_score(y_test_holdout, y_pred_holdout)
        roc_holdout = roc_auc_score(y_test_holdout, y_pred_proba_holdout)
        ks_holdout, ks_threshold_holdout = ks_score(y_test_holdout, y_pred_proba_holdout)

        print(f"Holdout Test Results:")
        print(f"Test samples: {len(y_test_holdout)}")
        print(f"ACC={acc_holdout * 100:.2f}% | F1={f1_holdout * 100:.2f}% | ROC={roc_holdout * 100:.2f}%")
        print(f"KS_SCORE={ks_holdout:.2f} | KS_THRESHOLD={ks_threshold_holdout:.2f}")
        print(classification_report(y_test_holdout, y_pred_holdout, digits=4))

        # Confusion matrix
        cm_holdout = confusion_matrix(y_test_holdout, y_pred_holdout, normalize='true')
        print(f"Confusion Matrix (normalized):\n{cm_holdout}")

        # Performance summary
        base_holdout = len(y_test_holdout)
        target_holdout = y_test_holdout.sum()
        percent_target_holdout = 100 * target_holdout / base_holdout

        tag = '2M' if '2M' in lb else '3M' if '3M' in lb else '1M'
        holdout_report = [{
            "Dataset": "Holdout Test",
            "Label": lb,
            "Base": f"{base_holdout / 1000:.1f}K",
            "Target": f"{target_holdout / 1000:.1f}K",
            "%target": f"{percent_target_holdout:.1f}%",
            "AUC": f"{roc_holdout * 100:.1f}%",
            "F1": f"{f1_holdout * 100:.1f}%",
            "KS": f"{ks_holdout:.3f}"
        }]
        holdout_report_df = pd.DataFrame(holdout_report)
        print(f"\n{holdout_report_df.to_markdown(index=False)}")

        # Lift table for holdout test
        if f'profit_{tag}' in test_data_clean.columns:
            print(f"\nLift Table for Holdout Test:")
            report_lift_table(test_data_clean, y_test_holdout, y_pred_proba_holdout, profit_col=f'profit_{tag}')

        # Save holdout predictions
        holdout_results = test_data_clean.copy()
        holdout_results['y_true'] = y_test_holdout
        holdout_results['y_pred_proba'] = y_pred_proba_holdout
        holdout_results['y_pred'] = y_pred_holdout
        holdout_results.to_csv(outdir / "predictions" / f"holdout_predictions_{lb}.csv", index=False)
        print(f"Holdout predictions saved to holdout_predictions_{lb}.csv")

    else:
        print("No valid holdout test data available!")

    print(f"\n{'='*60}")
    print(f"COMPLETED EVALUATION FOR {lb}")
    print(f"{'='*60}")

#%%
# Summary of Walk-Forward Validation Results
print("\n" + "="*80)
print("WALK-FORWARD VALIDATION SUMMARY")
print("="*80)

print("""
METHODOLOGY IMPROVEMENTS:
1. ✅ Walk-Forward Validation: Multiple time-based train/validation splits
2. ✅ Purge Period: 1-month gap between train and validation to avoid look-ahead bias
3. ✅ Embargo Period: 1-month gap after validation to simulate trading delays
4. ✅ Robust Evaluation: Multiple folds provide stable performance estimates
5. ✅ Final Holdout Test: Completely unseen data for final model evaluation

BENEFITS:
- Prevents overfitting to specific time periods
- Simulates realistic trading conditions
- Provides confidence intervals for performance metrics
- Ensures model can predict future unseen periods
- Eliminates data leakage between train/validation/test sets

NEXT STEPS:
1. Analyze fold-wise performance to identify time periods where model struggles
2. Consider ensemble methods combining multiple fold models
3. Implement online learning for continuous model updates
4. Add more sophisticated feature engineering based on time-series patterns
""")

print("="*80)
