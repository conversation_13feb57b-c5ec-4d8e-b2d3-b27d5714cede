1. Data Engineering (cực quan trọng)

Feature đa chiều hơn:
# Ngoài indicator truyền thống (RSI, MACD, EMA…), mày thêm:

Feature liên quan volume: OBV, VWAP, accumulation/distribution.

Feature liên quan biến động: ATR, volatility bands.

# Cross-stock features: dữ liệu sector hoặc index (VN30, S&P500 nếu global).

# Window features: rolling mean/median/volatility trong 5, 10, 20 ngày.

# Lag features: thêm các cột giá/indicator lùi 1-5 ngày để model học được momentum.

2. Label Engineering

# Đừng chỉ binary ↑/↓. C<PERSON> thể thử:

# Multi-class (giảm mạnh, giảm nhẹ, đi ngang, tăng nhẹ, tăng mạnh).

# Regression: dự đoán % thay đổi trong N ngày → dễ capture mức độ hơn.

# Custom scoring: tối ưu metric gần với trading hơn (profit factor, Sharpe ratio) thay vì accuracy/F1.

# Target horizon khác nhau: thay vì chỉ predict ngày mai ↑/↓, mày tạo thêm label 3 ngày, 5 ngày, 10 ngày → model học được multi-horizon.

Profit-driven labels:

Label dựa trên excess return so với index/sector (alpha).

Label dựa trên risk-adjusted return (ví dụ return > k × volatility).

Soft label: thay vì 0/1 cứng, dùng xác suất dựa trên mức tăng/giảm (%change chuẩn hoá về [0,1]) → giúp model smooth hơn.

3. Model Tuning

Hyperparameter tuning: dùng Optuna/BayesOpt thay vì grid search.

Ensemble: stacking thêm LightGBM/CatBoost hoặc kết hợp với linear model (blend signal).

Temporal CV: dùng time-series split để tránh leak.

*Stacking/Blending:

Train XGBoost, LightGBM, CatBoost riêng → meta-model combine.

Thêm logistic regression/meta-MLP ở tầng cuối để blend score.

# *Regularization với time series:

# Dùng early stopping + high learning_rate warmup.

# Feature selection bằng SHAP/Permutation để tránh noise.

*Custom loss function:

Thay vì logistic loss, dùng loss mô phỏng Sharpe ratio hoặc profit per trade.

Có paper gọi là “Utility-based Loss” cho trading signals.

2. Data Handling & Training Tricks
*Time-based validation:

# Dùng Purged K-fold Cross Validation (loại bỏ leakage giữa train/test).

##########Dùng walk-forward validation (train trên past → predict next).

*Sample weighting:

### Weight các sample gần hiện tại cao hơn quá khứ.

Weight theo volatility (phiên nào biến động mạnh thì quan trọng hơn).

#####*Class imbalance handling: nếu data imbalance (tăng nhiều hơn giảm), dùng focal loss hoặc custom objective.

4. Post-processing tín hiệu

Probability calibration: Platt scaling/Isotonic regression để score ra sắc nét hơn.

Signal smoothing: rolling average trên output score để tránh nhiễu.

Threshold tuning: thay vì cut-off 0.5, tối ưu threshold theo metric trading (maximize CAGR/Sharpe).

# Bayesian optimization cho threshold: thay vì fixed threshold, tối ưu cut-off để maximize CAGR.

Markov smoothing: áp dụng HMM hoặc filter để smooth tín hiệu từ model.

Meta labeling (theo López de Prado):

Dùng model chính dự đoán direction.

Dùng model phụ (meta-model) dự đoán trade đó có đáng hold không → giảm false signal.

5. Beyond XGBoost

Thử deep learning nhẹ:

TabNet hoặc MLP với time features.

Nếu muốn thử nâng tầm: LSTM/Transformer cho time series (nhưng cần xử lý tốt data leakage).

5. Data Expansion

Alternative data (nếu được):

Tin tức, sentiment từ báo, mạng xã hội.

Macro indicator (lãi suất, tỷ giá).

Cross-sectional features:

Thêm factor từ hàng trăm cổ phiếu → học kiểu “relative strength”.

Ví dụ: performance của cổ phiếu A so với ngành hoặc thị trường.

6. Backtest & Evaluation

# Đánh giá không chỉ accuracy mà:

# Profit simulation (backtest).

# Sharpe ratio, max drawdown.

# Có thể thấy model accuracy cao nhưng trade lỗ, hoặc accuracy vừa vừa nhưng trading lại lời.



# Context Pack — Dự án Alpha EOD (Cross-Sectional, EOD)

## 1) Tổng quan
- **Mục tiêu:** Tối ưu **EV sau phí** & **Sharpe** cho chiến lược EOD cross-sectional trên >1000 mã (2014→nay), latency EOD.
- **Data:** Giá + indicators cuối ngày; đã FE tốt.
- **Label hiện tại:** Nhị phân theo **profit 1 tháng** (H≈21 phiên): profit > threshold ⇒ 1, else 0. (Có kế hoạch mở rộng triple-barrier/soft label.)
- **Validation:** Có **Walk-Forward Validation (WFV)** theo tháng kèm **purge/embargo** (anti-leak).
- **Pipeline cũ:** Train nhiều XGBoost → chọn theo **lift** → **calibrate** → map **score [-1..3]**.

---

## 2) Pipeline "chuẩn chiến"

### 2.1 Research / OOF (để chọn mô hình & rule)
1. **Ingest & Events**
   - Chuẩn hoá giá/indicators; tính vol/ATR; tạo **event window**: `start=date`, `end=date+H (≈21 phiên)`.
2. **WFV + Purge/Embargo (theo tháng)**
   - Train ≤ tháng m−1, Val = tháng m; **purge/embargo** loại mọi train-sample có `[start,end]` overlap với `[val_start−E, val_end+E]`.
3. **Training per-fold (2-stage)**
   - Stage-1 (nhẹ) → ước lượng **p₀** (in-sample hoặc **OOF nội bộ** time-aware).
   - Tính **sample_weight_train = time-decay × uniqueness × cost-aware × focal(p₀)**.
   - Stage-2 (final) train với `sample_weight_train`.
   - Predict val → lưu **OOF**: `date, ticker, p_raw, future_ret, cost, sector, vol_bucket, fold_id`.
4. **Model selection (live-like)**
   - Từ **OOF nhiều cấu hình/model**, chấm theo **EV@θ (sau phí)**, **Sharpe**, **AUC-PR**, **IC & ICIR** **theo tháng**.
   - Chọn **top-K** (mean − λ·std), **ensemble** bằng trung bình **logit** (hoặc weight theo ICIR).
5. **Rolling calibration (no-leak)**
   - Mỗi tháng m: fit **Platt/Isotonic** trên **OOF ≤ m−1**, apply cho **tháng m** → `OOF_calib`.
6. **Score mapping (sector-neutral)**
   - Map **logit đã calibrate** → **quintile theo sector** (freeze bin edges bằng rolling 12–24m) → **score [-1..3]**.
7. **Threshold & execution rules**
   - Tune **θ_in** tối đa **EV sau phí** (per-sector/per-vol); đặt **θ_out < θ_in** (hysteresis).
   - **EMA logit** (α≈0.2–0.4) để khử whipsaw; **turnover cap** & tranche khớp lệnh.
8. **Backtest pseudo-live trên OOF**
   - Equity curve, **Sharpe/Sortino/MaxDD**, **Turnover**, **EV@θ**, **Lift@k**, **IC suite**, **Capacity** (ADV/HHI).

### 2.2 Live / Production (hàng tháng)
1. **Retrain**
   - 2-stage trên **window 24–36m** kết thúc ở m−1 (giữ focal/time/uniq/cost).
2. **Predict tháng m**
   - Apply **calibrator_{m−1}** + **score_bins_{m−1}** → ra **proba, logit, score**.
3. **Sinh tín hiệu**
   - EMA logit → **θ_in/θ_out** (per-sector/vol) → risk/turnover/capacity checks → **orders_m**.
4. **Cuối tháng**
   - Cập nhật **calibrator_m**, **score_bins_m**, **rules_m** bằng data mới; theo dõi drift/PSI.

---

## 3) Chi tiết kỹ thuật then chốt

### 3.1 Label & Horizon
- **Label:** Nhị phân theo **profit 1 tháng** (H≈21 phiên). Có thể nâng cấp triple-barrier & soft labels.
- **Cost:** Phí/slippage per-trade, dùng trong **weights** & **metrics EV**.

### 3.2 WFV + Purge/Embargo
- **Splits:** Train ≤ m−1, Val = m; `step_months=1`, `train_window=24–36m`.
- **Purge/Embargo:** *Event-based* theo `[start,end=t+H]`, loại train samples overlap **[val_start−E, val_end+E]** (E≈5–20 ngày).

### 3.3 Training 2-stage + Weights
- **Stage-1:** XGB nhẹ (n_estimators≈300, depth=4, lr≈0.07) → **p₀**.
- **Weights:**
  - **Time-decay:** half-life ≈ 126 ngày (`exp(-λ·age)`).
  - **Uniqueness:** trung bình `1/concurrency` trên [start,end].
  - **Cost-aware:** `max(future_ret − cost, 0)`.
  - **Focal-like:** với \(p₀\): `α(1−p₀)^γ` (y=1), `(1−α)p₀^γ` (y=0), γ≈1–2.
  → **w_all = time × uniq × cost × focal** (chuẩn hoá mean=1).
- **Stage-2:** XGB final (n_estimators≈900, depth=5, lr≈0.05, reg L1/L2), train với `w_all`.

### 3.4 Metrics ưu tiên (tính trên OOF)
- **AUC-PR**, **ROC-AUC** (tham khảo).
- **IC (Spearman) theo ngày** + **ICIR**.
- **Lift@k%** cross-sectional (k≈10%).
- **EV@θ sau phí** (θ grid 0.55→0.85, tuỳ base-rate & cost).
- **Sharpe quasi-live:** mỗi ngày equal-weight long các mã có `p≥θ` (pnl_daily mean/std).

### 3.5 Calibration & Score
- **Calibration rolling:** Platt/Isotonic train trên OOF ≤ m−1, apply m.
- **Score mapping:** sector-neutral quantiles → **score [-1..3]**; freeze bin edges bằng rolling 12–24m.

### 3.6 Threshold & Execution
- **Tune θ_in/out** tối ưu **EV** theo sector/vol; **EMA logit** + **hysteresis** + **turnover cap**.

---

## 4) I/O chuẩn hoá

### 4.1 Input DataFrame (tối thiểu)
- `date` (datetime), `ticker` (str), **features…**
- `label` (0/1), `future_ret` (float), `cost` (float)
- (khuyến nghị) `sector` (str), `vol_bucket` (cat/str)

### 4.2 Output OOF (cho các bước sau)
- `date, ticker, p_raw, future_ret, cost, sector, vol_bucket, fold_id`
- Sau calibration: thêm `p_calib`, `logit_calib`
- Sau scoring: thêm `score` (−1..3)

---

## 5) Khối code sẵn có (vai trò)
- `add_event_window` → tạo `start/end` theo H ngày giao dịch.
- `generate_wfv_folds` → tạo block **train ≤ m−1 / val = m** theo tháng.
- `apply_wfv_with_purge` → trả index **train/val** sau **purge/embargo** event-based.
- `inner_time_oof_p0` → tạo **p₀ OOF** nội bộ trong train (time-aware) cho focal.
- Weights: `time_decay_weights`, `uniqueness_weights`, `cost_aware_weights`, `focal_like_weights`.
- `train_two_stage` → Stage-1 → weights → Stage-2 (final).
- Metrics: `ic_by_day`, `lift_at_k`, `tune_theta_ev`, `ev_at_threshold`, `sharpe_quasi_live`.
- Orchestrator: `evaluate_walk_forward` → WFV+purge, 2-stage train, predict val, xuất **OOF + metrics**.

---

## 6) Tham số khuyến nghị
- **H_days:** 21; **embargo_days:** 10
- **train_window:** 36m; **val_window:** 1m; **step:** 1m
- **Focal:** γ=1.5, α=None (auto theo prevalence)
- **Time-decay half-life:** 126 ngày
- **Theta grid:** 0.55–0.85; **Lift@k:** 10%

---

## 7) Artefact cần version hoá
- **Models:** stage-1/2 per-month (hoặc seed + cfg)
- **Calibrators rolling:** `calibrator_m.pkl`
- **Score bins:** `score_edges_by_sector_m.json`
- **Rules:** `rules_m.json` (θ_in/out, ema_alpha, turnover cap)
- **OOF store:** parquet theo model_id
- **Ensemble weights & reports**

---

## 8) Roadmap triển khai (ưu tiên tác động cao)
1. **Selector OOF + Ensemble top-K** (mean−λ·std theo EV/Sharpe)
2. **Rolling calibration** + **score sector-neutral**
3. **Tune θ_in/out + EMA + turnover cap**
4. **Bật inner OOF p₀ + 2-stage weights** vào retrain loop
5. **(Optional)** Stacking đa model/horizon & meta-labeling
