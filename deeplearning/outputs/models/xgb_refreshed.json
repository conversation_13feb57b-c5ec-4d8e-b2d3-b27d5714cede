{"learner": {"attributes": {"best_iteration": "599", "best_score": "0.22697500785597707"}, "feature_names": ["(CF_OA_P0+CF_OA_P1+CF_OA_P2+CF_OA_P3+CF_Invest_P0+CF_Invest_P1+CF_Invest_P2+CF_Invest_P3)/(OShares*Price+LtDebt_P0)", "(NP_P0+NP_P1+NP_P2+NP_P3)/OShares", "(NP_P0-NP_P4)/NP_P4", "(NP_P0-NP_P4)/abs(NP_P4)", "CF_OA_5Y/OShares", "C_L1W", "Cash_P0/(LtDebt_P0+1)", "Close/BVPS", "Close/Close_T1", "Close/Close_T1W", "Close/LO_3M_T1", "Close/MA10", "Close/MA200", "Close/Res_1Y", "Close/Sup_1Y", "Close/VAP1M", "Close/VAP1W", "Close/VAP3M", "Close/VNINDEX_RSI_Max3M_Close", "Close/Volume_Max1Y_High", "Close/Volume_Max5Y_High", "Close/Volume_MaxTop5_2Y_Close", "Close_T1/LO_3M_T1", "Close_T1/VAP1M", "Close_T1W/VAP1M", "Close_T1W/VAP3M", "Close_T1W/Volume_Max1Y_High", "D_CMB_Peak_T1/D_CMB", "D_CMB_XFast", "D_MACDdiff", "D_RSI", "D_RSI/D_RSI_T1", "D_RSI/D_RSI_T1W", "D_RSI_Max1W", "D_RSI_Max1W/D_RSI", "D_RSI_Max1W_Close/D_RSI_Max3M_Close", "D_RSI_Max3M", "D_RSI_Max3M/D_RSI_Max1W", "D_RSI_Max3M_MACD/D_RSI_Max1W_MACD", "D_RSI_Min1W", "D_RSI_Min1W/D_RSI_Min3M", "D_RSI_Min1W_Close/D_RSI_Min3M_Close", "D_RSI_Min3M", "D_RSI_T1/D_RSI", "EBITM_P0/EBITM_P4", "EVEB", "EVEB_MA1Y", "EVEB_MA3M", "EVEB_SD1Y", "EVEB_SD3M", "FAsset_Eq_P0/FAsset_Eq_P4", "FSCORE", "FinLev_P0/FinLev_P4", "HI_3M_T1/LO_3M_T1", "ICB_Code", "ID_Current-ID_Release", "ID_Current-Volume_Max1Y_ID", "ID_Current-Volume_MaxTop5_2Y_ID", "ID_LO_3Y-ID_HI_3Y", "LO_3M_T1/Sup_1Y", "MA10/MA200", "MA10_T1/MA200_T1", "MA20/MA50", "MA20_T1/MA50_T1", "MA50/MA200", "NPM_P0/NPM_P4", "NP_P0/NP_P1", "NP_P0/NP_P4", "Open/Close", "OwnEq_Cap_P0/OwnEq_Cap_P4", "PB", "PB_MA1Y", "PB_SD1Y", "PCF", "PE", "PE_MA1Y", "PE_SD1Y", "ROE5Y", "ROE_Min3Y", "ROE_Min5Y", "ROIC3Y", "ROIC5Y", "ROIC_Min3Y", "ROIC_Min5Y", "ROIC_Trailing", "Revenue_YoY_P0", "Risk_Rating", "VNINDEX_CMF", "VNINDEX_MACDdiff", "VNINDEX_PE", "VNINDEX_RSI_Max1W", "VNINDEX_RSI_Max1W/VNINDEX_RSI", "VNINDEX_RSI_Max1W_Close/VNINDEX_RSI_Max3M_Close", "VNINDEX_RSI_Max3M", "VNINDEX_RSI_Max3M_MACD/VNINDEX_RSI_Max1W_MACD", "VNINDEX_RSI_MinT3", "Volume*Price/Trading_Session", "Volume/Volume_1M", "Volume/Volume_3M_P50", "Volume/Volume_3M_P90", "Volume_Max1Y_High/LO_3M_T1", "abs(Dividend_Min3Y)/Price"], "feature_types": ["float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float"], "gradient_booster": {"model": {"gbtree_model_param": {"num_parallel_tree": "1", "num_trees": "0"}, "iteration_indptr": [0], "tree_info": [], "trees": []}, "name": "gbtree"}, "learner_model_param": {"base_score": "5E-1", "boost_from_average": "1", "num_class": "0", "num_feature": "102", "num_target": "1"}, "objective": {"name": "binary:logistic", "reg_loss_param": {"scale_pos_weight": "1"}}}, "version": [3, 0, 2]}