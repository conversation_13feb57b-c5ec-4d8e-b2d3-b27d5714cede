#!/usr/bin/env python3
"""
Example script to run the prediction pipeline.
This demonstrates how to use the PredictionPipeline class.
"""

import os
import sys
from pathlib import Path

# Add current directory to path
current_dir = os.path.dirname(os.path.abspath(__file__))
current_dir = current_dir.replace("/deeplearning", "")
os.chdir(current_dir)
sys.path.insert(0, current_dir)

from deeplearning.prediction_pipeline import PredictionPipeline

def main():
    """Main function to run the prediction pipeline."""
    
    print("=== Stock Prediction Pipeline ===")
    print("This pipeline will:")
    print("1. Load ticker data from ticker_v1a/")
    print("2. Apply feature engineering")
    print("3. Load trained XGBoost model")
    print("4. Generate predictions and convert to scores")
    print("5. Save results with ['time', 'ticker', 'score'] columns")
    print("6. Use multiprocessing for efficiency")
    print()
    
    # Check if model exists
    model_path = "deeplearning/outputs/models/xgb_model.joblib"
    if not Path(model_path).exists():
        print(f"Warning: Model file not found at {model_path}")
        print("Please ensure you have trained a model first using exp_ml_dl.ipynb")
        print("The pipeline will attempt to load the model anyway...")
        print()
    
    # Check if ticker data exists
    ticker_path = "ticker_v1a/"
    if not Path(ticker_path).exists():
        print(f"Error: Ticker data directory not found at {ticker_path}")
        print("Please ensure ticker data is available in the ticker_v1a/ directory")
        return
    
    ticker_files = list(Path(ticker_path).glob("*.csv"))
    print(f"Found {len(ticker_files)} ticker files in {ticker_path}")
    
    if len(ticker_files) == 0:
        print("No ticker CSV files found. Please add ticker data first.")
        return
    
    # Initialize and run pipeline
    try:
        print("\nInitializing prediction pipeline...")
        pipeline = PredictionPipeline(model_path=model_path)
        
        print("Starting prediction process...")
        output_file = "stock_predictions.csv"
        pipeline.run_pipeline(output_file=output_file)
        
        print(f"\n✅ Pipeline completed successfully!")
        print(f"Results saved to: deeplearning/predictions/{output_file}")
        
        # Show sample results
        import pandas as pd
        results_path = f"deeplearning/predictions/{output_file}"
        if Path(results_path).exists():
            df = pd.read_csv(results_path)
            print(f"\nSample results (first 10 rows):")
            print(df.head(10).to_string(index=False))
            print(f"\nTotal predictions: {len(df)}")
            print(f"Unique tickers: {df['ticker'].nunique()}")
            print(f"Score statistics:")
            print(df['score'].describe())
        
    except Exception as e:
        print(f"\n❌ Pipeline failed with error: {e}")
        import traceback
        traceback.print_exc()
        print("\nTroubleshooting tips:")
        print("1. Ensure the model file exists and is valid")
        print("2. Check that ticker data has the required columns")
        print("3. Verify that feature engineering dependencies are available")
        print("4. Check system resources for multiprocessing")

if __name__ == "__main__":
    main()
