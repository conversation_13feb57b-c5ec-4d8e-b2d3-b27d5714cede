import numpy as np


######2
def proba_to_score_power(p, alpha=0.7, min_score=-1, max_score=3, eps=1e-12):
    """
    Nonlinear mapping: score = min_score + (max_score - min_score) * (p**alpha)
    - alpha < 1: nhấn mạnh positive tail
    - alpha > 1: nhấn mạnh negative tail
    """
    p = np.clip(np.asarray(p, dtype=float), eps, 1 - eps)
    return min_score + (max_score - min_score) * (p ** alpha)


def score_to_proba_power(score, alpha=0.7, min_score=-1, max_score=3):
    score = np.asarray(score, dtype=float)
    normed = (score - min_score) / (max_score - min_score)
    return np.clip(normed ** (1 / alpha), 0, 1)


import numpy as np


# =========================
# 1) CALIBRATION
# =========================

def _logit(x):
    return np.log(x) - np.log(1 - x)


def _sigmoid(x):
    return 1 / (1 + np.exp(-x))


class Calibrator:
    """
    Ưu tiên Isotonic Regression; nếu thiếu sklearn/scipy thì fallback về Platt.
    """

    def __init__(self, kind="auto"):
        self.kind = kind
        self.model_ = None
        self.ab_ = None

    def fit(self, p_valid, y_valid):
        p = np.clip(np.asarray(p_valid, float), 1e-9, 1 - 1e-9)
        y = np.asarray(y_valid, float)

        if self.kind in ("iso", "auto"):
            try:
                from sklearn.isotonic import IsotonicRegression
                iso = IsotonicRegression(y_min=1e-9, y_max=1 - 1e-9, out_of_bounds='clip')
                iso.fit(p, y)
                self.kind = "iso"
                self.model_ = iso
                return self
            except Exception:
                # rớt xuống Platt
                pass

        # Platt scaling: q = sigmoid(a*p + b)
        try:
            from scipy.optimize import minimize
            def nll(ab):
                a, b = ab
                q = _sigmoid(a * p + b)
                eps = 1e-9
                return -(y * np.log(q + eps) + (1 - y) * np.log(1 - q + eps)).mean()

            res = minimize(nll, x0=[5.0, -2.5], method="L-BFGS-B")
            self.kind = "platt"
            self.ab_ = tuple(res.x)
        except Exception:
            # Fallback cuối: identity (không khuyến nghị)
            self.kind = "identity"
        return self

    def transform(self, p):
        p = np.clip(np.asarray(p, float), 1e-9, 1 - 1e-9)
        if self.kind == "iso" and self.model_ is not None:
            q = self.model_.predict(p)
        elif self.kind == "platt" and self.ab_ is not None:
            a, b = self.ab_
            q = _sigmoid(a * p + b)
        else:
            q = p  # identity fallback
        return np.clip(q, 1e-9, 1 - 1e-9)


# =========================
# 2) SCORE MAPPER (PIECEWISE)
# =========================

def _default_knots():
    """
    Knots theo thứ tự percentiles [0, 20, 40, 60, 80, 100].
    Gợi ý “ăn theo lift” mạnh dần về phải. Bạn có thể override khi fit.
    """
    return np.array([-1.0, -0.4, 1.0, 1.8, 2.95, 3.0], dtype=float)


def derive_knots_from_lift(target_rates, base_rate):
    """
    Auto-fit score_knots từ %target của 5 bin (0–20,...,80–100).
    Ý tưởng: score ≈ 1 + γ*(lift - 1), ép mốc trái ~ -1 và mốc phải ~ 3.
    - target_rates: list/array length=5 (% theo bin), vd [5.17,12.07,16.93,22.07,29.48]
    - base_rate:    vd 17.0
    Trả về: score_knots 6 điểm cho [0,20,40,60,80,100] (mốc trung gian nội suy).
    """
    tr = np.asarray(target_rates, float)
    lifts = tr / float(base_rate)

    # Tính γ để vừa chạm -1 ở bin 0 và ~3 ở bin 4
    # s = 1 + γ*(lift-1)
    # s0 ≈ -1  => γ_low  = (-2)/(lift0-1)
    # s4 ≈  3  => γ_high = ( 2)/(lift4-1)
    def safe_gamma(num, den):
        if abs(den) < 1e-12:
            return np.inf
        return num / den

    gamma_low = safe_gamma(-2.0, lifts[0] - 1.0)
    gamma_high = safe_gamma(2.0, lifts[-1] - 1.0)

    # lấy gamma bé hơn để tránh overshoot
    gamma = min(gamma_low if gamma_low > 0 else np.inf,
                gamma_high if gamma_high > 0 else np.inf)
    if not np.isfinite(gamma):
        gamma = 2.0  # fallback nhẹ

    # 5 nút ứng với 5 bin. Thêm 2 đầu mút để nội suy mượt.
    raw5 = 1.0 + gamma * (lifts - 1.0)
    raw5[2] = 1.0  # giữ bin 40–60 = neutral pivot

    # Nội suy ra 6 mốc (0,20,40,60,80,100). Đầu/cuối lấy gần kề.
    knots5_pct = np.array([10, 30, 50, 70, 90])  # tâm mỗi bin
    pct6 = np.array([0, 20, 40, 60, 80, 100])
    s6 = np.interp(pct6, knots5_pct, raw5, left=raw5[0], right=raw5[-1])

    s6 = np.clip(s6, -1.0, 3.0)
    s6[2] = 1.0  # đảm bảo pivot
    return s6


class ScoreMapper:
    """
    - Fit percentiles trên p_calib (validation).
    - Giữ monotonic mapping: np.interp theo các mốc percentiles -> score_knots.
    """

    def __init__(self, score_knots=None):
        self.score_knots = np.asarray(score_knots, float) if score_knots is not None else _default_knots()
        self.percentiles_ = None  # mốc p-calib tương ứng [0, 20, 40, 60, 80, 100]
        self.calibrator_ = Calibrator(kind="auto")

    def fit(self, p_valid_raw, y_valid, score_knots=None, lift_target_rates=None, base_rate=None):
        # 1) calibrate
        self.calibrator_.fit(p_valid_raw, y_valid)
        p_cal = self.calibrator_.transform(p_valid_raw)

        # 2) xác định score_knots
        if lift_target_rates is not None and base_rate is not None:
            self.score_knots = derive_knots_from_lift(lift_target_rates, base_rate)
        elif score_knots is not None:
            self.score_knots = np.asarray(score_knots, float)
        else:
            self.score_knots = _default_knots()

        # 3) lấy mốc percentiles trên p_cal
        self.percentiles_ = np.quantile(p_cal, [0.0, 0.2, 0.4, 0.6, 0.8, 1.0])
        return self

    def transform(self, p_raw, clip=True):
        # p_raw -> calibrate -> nội suy theo percentiles_ -> score [-1,3]
        assert self.percentiles_ is not None, "Bạn cần fit() trước khi transform()."
        p_cal = self.calibrator_.transform(p_raw)
        s = np.interp(p_cal, self.percentiles_, self.score_knots)
        if clip:
            s = np.clip(s, -1.0, 3.0)
        return s


# =========================
# 3) VÍ DỤ DÙNG NHANH
# =========================
# --- TRAIN / VALID ---
# p_valid_raw = model.predict_proba(X_valid)[:,1]
# y_valid = y_valid_array
# mapper = ScoreMapper()
# mapper.fit(
#     p_valid_raw, y_valid,
#     # Nếu muốn auto-fit theo lift table (ví dụ TEST2 bạn đưa):
#     # lift_target_rates=[5.17, 12.07, 16.93, 22.07, 29.48], base_rate=17.0
# )
#
# --- INFER / PROD ---
# p_test_raw = model.predict_proba(X_test)[:,1]
# score = mapper.transform(p_test_raw)          # trong [-1, 3]
# label = mapper.predict_label(score)           # Negative / Neutral / Positive


class ScoreMapperSimple:
    """
    Bỏ calibrate mặc định (rank-based). Optional: temperature scaling 1 tham số (nhẹ).
    - Fit percentiles trên proba (hoặc proba sau temperature).
    - Nội suy piecewise-linear sang [-1, 3] theo knots.
    """

    def __init__(self, score_knots=None, use_temperature=False):
        # mốc score cho percentiles [0,20,40,60,80,100]
        self.score_knots = np.asarray(score_knots, float) if score_knots is not None else _default_knots()
        self.use_temperature = use_temperature
        self.t_ = 1.0  # temperature (t>0); t>1 làm proba “gắt” hơn, t<1 làm “mềm” hơn
        self.percentiles_ = None  # các mốc p theo [0,20,40,60,80,100]

    def _apply_temp(self, p):
        if not self.use_temperature:
            return p
        p = np.clip(np.asarray(p, float), 1e-9, 1 - 1e-9)
        return _sigmoid(self.t_ * _logit(p))

    def fit(self, p_valid_raw, y_valid=None, lift_target_rates=None, base_rate=None):
        """
        - p_valid_raw: proba từ XGBoost (validation)
        - y_valid: optional (chỉ dùng khi learn temperature theo Brier)
        - lift_target_rates/base_rate: optional (auto điều chỉnh knots theo lift table)
        """
        p_valid_raw = np.asarray(p_valid_raw, float)

        # Học temperature (optional, 1 tham số)
        if self.use_temperature and y_valid is not None:
            y = np.asarray(y_valid, float)
            p = np.clip(p_valid_raw, 1e-9, 1 - 1e-9)
            z = _logit(p)
            # minimize Brier score w.r.t t (1D search)
            ts = np.linspace(0.5, 3.0, 26)  # nhanh-gọn
            best = (np.inf, 1.0)
            for t in ts:
                q = _sigmoid(t * z)
                brier = np.mean((q - y) ** 2)
                if brier < best[0]:
                    best = (brier, t)
            self.t_ = best[1]

        # Auto chỉnh knots theo lift table (nếu có)
        if lift_target_rates is not None and base_rate is not None:
            self.score_knots = derive_knots_from_lift(lift_target_rates, base_rate)

        # Lấy percentiles trên p (sau temperature nếu có)
        p_use = self._apply_temp(p_valid_raw)
        self.percentiles_ = np.quantile(p_use, [0, .2, .4, .6, .8, 1.0])
        return self

    def transform(self, p_raw, clip=True):
        assert self.percentiles_ is not None, "Cần fit() trước."
        p_use = self._apply_temp(p_raw)
        s = np.interp(p_use, self.percentiles_, self.score_knots)
        if clip: s = np.clip(s, -1.0, 3.0)
        return s

# mapper = ScoreMapperSimple()
# mapper.fit(p_valid_raw)               # chỉ cần proba VALID để lấy percentiles
# score = mapper.transform(p_test_raw)  # ra [-1, 3]
# label = mapper.predict_label(score)


# mapper = ScoreMapperSimple(use_temperature=True)
# mapper.fit(p_valid_raw, y_valid=y_valid)  # học t bằng Brier
# score = mapper.transform(p_test_raw)


# mapper = ScoreMapperSimple()
# mapper.fit(
#     p_valid_raw,
#     lift_target_rates=[5.17, 12.07, 16.93, 22.07, 29.48],
#     base_rate=17.0
# )
# score = mapper.transform(p_test_raw)
