{"label_column": "label_binary_test", "cv_scores": {"roc_auc": [0.966396233974359], "pr_auc": [0.9696879985812865], "log_loss": [0.35428263389660797], "brier_score": [0.09679632720035737]}, "cv_mean_std": {"roc_auc": {"mean": 0.966396233974359, "std": 0.0}, "pr_auc": {"mean": 0.9696879985812865, "std": 0.0}, "log_loss": {"mean": 0.35428263389660797, "std": 0.0}, "brier_score": {"mean": 0.09679632720035737, "std": 0.0}}, "model_params": {"objective": "binary:logistic", "n_estimators": 10, "max_depth": 3, "learning_rate": 0.1, "random_state": 42, "verbosity": 0}, "training_time_avg": 0.0855553150177002, "inference_time_avg": 0.0013496875762939453, "n_features": 20, "n_samples": 1000}