#!/usr/bin/env python3
"""
Test script for cv.py - Comprehensive Cross-Validation Framework
"""

import sys
import os
import pandas as pd
import numpy as np

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """Test if all required modules can be imported"""
    print("🔍 Testing imports...")
    
    try:
        import pandas as pd
        print("✅ pandas imported successfully")
    except ImportError as e:
        print(f"❌ pandas import failed: {e}")
        return False
    
    try:
        import numpy as np
        print("✅ numpy imported successfully")
    except ImportError as e:
        print(f"❌ numpy import failed: {e}")
        return False
    
    try:
        import matplotlib.pyplot as plt
        print("✅ matplotlib imported successfully")
    except ImportError as e:
        print(f"❌ matplotlib import failed: {e}")
        return False
    
    try:
        import seaborn as sns
        print("✅ seaborn imported successfully")
    except ImportError as e:
        print(f"❌ seaborn import failed: {e}")
        return False
    
    try:
        from sklearn.model_selection import TimeSeriesSplit
        from sklearn.metrics import roc_auc_score, average_precision_score
        from sklearn.utils.class_weight import compute_sample_weight
        print("✅ scikit-learn imported successfully")
    except ImportError as e:
        print(f"❌ scikit-learn import failed: {e}")
        return False
    
    try:
        from xgboost import XGBClassifier
        print("✅ xgboost imported successfully")
    except ImportError as e:
        print(f"❌ xgboost import failed: {e}")
        print("💡 Try installing with: pip install xgboost")
        return False
    
    try:
        import joblib
        print("✅ joblib imported successfully")
    except ImportError as e:
        print(f"❌ joblib import failed: {e}")
        return False
    
    return True

def test_data_loading():
    """Test if dl_train.csv can be loaded"""
    print("\n📊 Testing data loading...")
    
    data_path = "dl_train.csv"
    if not os.path.exists(data_path):
        print(f"❌ Data file not found: {data_path}")
        return False
    
    try:
        # Try to read first few rows
        df = pd.read_csv(data_path, nrows=100)
        print(f"✅ Data loaded successfully: {df.shape}")
        print(f"📋 Columns: {list(df.columns[:10])}...")
        
        # Check for required columns
        required_cols = ['time', 'ticker']
        missing_cols = [col for col in required_cols if col not in df.columns]
        if missing_cols:
            print(f"⚠️  Missing required columns: {missing_cols}")
        else:
            print("✅ Required columns found")
        
        # Check for label columns
        label_cols = [col for col in df.columns if col.startswith('label_')]
        print(f"🎯 Found {len(label_cols)} label columns: {label_cols[:3]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ Error loading data: {e}")
        return False

def test_cv_import():
    """Test if CVEvaluator can be imported"""
    print("\n🔧 Testing CVEvaluator import...")
    
    try:
        from cv import CVEvaluator, CVResults
        print("✅ CVEvaluator imported successfully")
        
        # Test initialization
        evaluator = CVEvaluator(
            data_path="dl_train.csv",
            output_dir="test_artifacts"
        )
        print("✅ CVEvaluator initialized successfully")
        
        return True
        
    except Exception as e:
        print(f"❌ CVEvaluator import/init failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def create_sample_data():
    """Create a small sample dataset for testing"""
    print("\n🔬 Creating sample data for testing...")
    
    np.random.seed(42)
    n_samples = 1000
    n_features = 20
    
    # Generate sample features
    feature_data = {}
    for i in range(n_features):
        feature_data[f'feature_{i}'] = np.random.randn(n_samples)
    
    # Generate time series
    start_date = pd.Timestamp('2020-01-01')
    dates = pd.date_range(start_date, periods=n_samples, freq='D')
    
    # Generate sample data
    df = pd.DataFrame(feature_data)
    df['time'] = dates
    df['ticker'] = np.random.choice(['STOCK_A', 'STOCK_B', 'STOCK_C'], n_samples)
    df['tvt'] = 'train'
    df['tvt'].iloc[-200:] = 'val'  # Last 200 samples as validation
    df['week'] = df['time'].dt.strftime('%Y-%W')
    df['month'] = df['time'].dt.strftime('%Y-%m')
    
    # Generate binary labels
    # Create some correlation with features for realistic testing
    signal = (df['feature_0'] + df['feature_1'] * 0.5 + np.random.randn(n_samples) * 0.3)
    df['label_binary_test'] = (signal > signal.median()).astype(int)
    
    # Add profit columns (dummy)
    df['profit_1M'] = np.random.randn(n_samples) * 10
    
    # Save sample data
    sample_path = "sample_dl_train.csv"
    df.to_csv(sample_path, index=False)
    print(f"✅ Sample data created: {sample_path}")
    print(f"📊 Shape: {df.shape}")
    print(f"🎯 Label distribution: {df['label_binary_test'].value_counts().to_dict()}")
    
    return sample_path

def test_full_pipeline():
    """Test the full CV pipeline with sample data"""
    print("\n🚀 Testing full CV pipeline...")
    
    # Create sample data
    sample_path = create_sample_data()
    
    try:
        from cv import CVEvaluator
        
        # Initialize evaluator with sample data
        evaluator = CVEvaluator(
            data_path=sample_path,
            output_dir="test_artifacts"
        )
        
        # Test data loading
        evaluator.load_and_preprocess_data()
        print("✅ Data preprocessing completed")
        
        # Test with simple model params for speed
        simple_params = {
            'objective': 'binary:logistic',
            'n_estimators': 10,  # Very small for testing
            'max_depth': 3,
            'learning_rate': 0.1,
            'random_state': 42,
            'verbosity': 0
        }
        
        # Run evaluation on sample label
        results = evaluator.run_comprehensive_evaluation(
            label_cols=['label_binary_test'],
            model_params=simple_params,
            n_splits=2  # Small number for testing
        )
        
        print("✅ Full pipeline test completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Full pipeline test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all tests"""
    print("🧪 Starting CV Framework Tests")
    print("=" * 50)
    
    tests = [
        ("Import Test", test_imports),
        ("Data Loading Test", test_data_loading),
        ("CV Import Test", test_cv_import),
        ("Full Pipeline Test", test_full_pipeline)
    ]
    
    results = {}
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name} crashed: {e}")
            results[test_name] = False
    
    # Summary
    print(f"\n{'='*50}")
    print("📋 TEST SUMMARY:")
    print(f"{'='*50}")
    
    for test_name, passed in results.items():
        status = "✅ PASSED" if passed else "❌ FAILED"
        print(f"  {test_name}: {status}")
    
    total_passed = sum(results.values())
    total_tests = len(results)
    
    print(f"\n🎯 Overall: {total_passed}/{total_tests} tests passed")
    
    if total_passed == total_tests:
        print("🎉 All tests passed! CV framework is ready to use.")
    else:
        print("⚠️  Some tests failed. Please check the errors above.")
    
    return total_passed == total_tests

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
