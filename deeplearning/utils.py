# Load the necessary libraries
import random

import joblib
import matplotlib.pyplot as plt
import numpy as np
import optuna
import pandas as pd
import xgboost as xgb
from optuna.integration import XGBoostPruningCallback
from optuna.pruners import MedianPruner
from optuna.samplers import TPESampler
from sklearn.calibration import CalibratedClassifierCV
from sklearn.metrics import (
    average_precision_score, roc_auc_score, log_loss, brier_score_loss
)
from sklearn.metrics import confusion_matrix, ConfusionMatrixDisplay
from sklearn.utils.class_weight import compute_sample_weight
from xgboost.callback import EarlyStopping

random.seed(42)


def show_confusion_matrix(y_true, y_pred_prob, class_names=None, normalize=None, figsize=(6, 6)):
    if len(y_pred_prob.shape) > 1:
        y_pred = np.argmax(y_pred_prob, axis=1)
    else:
        y_pred = y_pred_prob
    cm = confusion_matrix(y_true, y_pred, normalize=normalize)
    print("Confusion Matrix:")
    print(cm)
    plt.figure(figsize=figsize)
    disp = ConfusionMatrixDisplay(confusion_matrix=cm, display_labels=class_names)
    disp.plot(cmap='Blues', values_format='.2f' if normalize else 'd', ax=plt.gca(), colorbar=False)
    plt.title('Confusion Matrix' + (f' (normalized: {normalize})' if normalize else ''))
    plt.show()


def ks_score(y_true, y_proba):
    from sklearn.metrics import roc_curve
    """
    Tính và hiển thị KS score
    y_true  : Ground truth labels (0 or 1)
    y_proba : Xác suất mô hình dự đoán cho class 1
    KS Score	Mức phân tách	Diễn giải
    0.00–0.20	Rất yếu 😢	Mô hình gần như không phân biệt được class 0 và 1
    0.20–0.40	Trung bình 😐	Mô hình có chút khả năng phân biệt
    0.40–0.70	Tốt 💪	Mô hình phân tách tốt, áp dụng thực tế được
    > 0.70	Rất tốt 🚀	Hiếm khi gặp, cần kiểm tra overfitting
    """
    fpr, tpr, thresholds = roc_curve(y_true, y_proba)
    ks = max(tpr - fpr)
    ks_threshold = thresholds[np.argmax(tpr - fpr)]

    return ks, ks_threshold


def report_lift_table(df_test, y_test, y_pred_proba, profit_col='profit_3M'):
    df_lift = df_test.reset_index(drop=True).copy()
    # Thêm label và score
    # df_lift['y_true'] = y_test
    # df_lift['y_prob'] = y_pred_proba
    df_lift['y_true'] = np.asarray(y_test)
    df_lift['y_prob'] = np.asarray(y_pred_proba)

    df_lift = df_lift.dropna(subset=['y_true', 'y_prob'])
    df_lift = df_lift.sort_values('y_prob', ascending=False).reset_index(drop=True)

    # baseline
    total_positives = df_lift['y_true'].sum()
    n = len(df_lift)
    baseline_rate = total_positives / n if n > 0 else np.nan
    if baseline_rate == 0:
        baseline_rate = np.nan  # hoặc raise ValueError("No positives")

    # Thêm profit và ticker nếu có, nếu không thì tạo cột dummy
    if profit_col not in df_lift.columns:
        df_lift[profit_col] = 0
    if 'ticker' not in df_lift.columns:
        df_lift['ticker'] = 'UNK'

    # Binning
    # labels = [f'{int(100/q*i)}-{int(100/q*(i+1))}' for i in range(q)]
    labels = ['0-20', '20-40', '40-60', '60-80', '80-100']
    df_lift['bin'] = pd.qcut(df_lift['y_prob'], q=5,
                             labels=labels,
                             duplicates='drop')

    # Group và tính toán
    lift_table = df_lift.groupby('bin', observed=False).agg(
        base=('y_true', 'count'),
        target=('y_true', 'sum'),
        pct_target=('y_true', 'mean'),
        average_profit=(profit_col, 'mean'),
        p10_profit=(profit_col, lambda x: x.quantile(0.1)),
        p50_profit=(profit_col, lambda x: x.quantile(0.5)),
        p90_profit=(profit_col, lambda x: x.quantile(0.9)),
        ticker_nums=('ticker', pd.Series.nunique),
        min_prob=('y_prob', 'min'),
        max_prob=('y_prob', 'max')
    ).reset_index()

    # lift_table = lift_table.drop('pct_target', axis=1)

    lift_table['%target'] = (lift_table['pct_target'] * 100).round(2)
    lift_table['lift'] = lift_table['pct_target'] / baseline_rate
    lift_table['prob_range'] = lift_table.apply(
        lambda x: f"{x['min_prob']:.2f} – {x['max_prob']:.2f}", axis=1
    )

    lift_table = lift_table.rename(columns={'bin': 'Bin score'})

    print(lift_table[
        ['Bin score', 'base', 'target', '%target', 'average_profit', 'p10_profit', 'p50_profit', 'p90_profit',
         'ticker_nums', 'lift', 'prob_range']].to_markdown(
        index=False))


def feature_importance_df(model):
    booster = model.get_booster()
    gain = booster.get_score(importance_type="gain")
    cover = booster.get_score(importance_type="cover")
    weight = booster.get_score(importance_type="weight")
    keys = set(gain.keys()) | set(cover.keys()) | set(weight.keys())
    rows = []
    for k in keys:
        rows.append({
            "feature": k,
            "gain": gain.get(k, 0.0),
            "cover": cover.get(k, 0.0),
            "weight": weight.get(k, 0.0),
        })
    return pd.DataFrame(rows).sort_values("gain", ascending=False)


def get_list_eval(pdxy, cname_tvt='tvt'):
    list_tvt = list(pdxy[cname_tvt].unique())
    i_train = pdxy[cname_tvt] == 'train'
    list_eval = [('train', i_train)]
    for tvt in list_tvt:
        if tvt.startswith('test'):
            i_test = pdxy[cname_tvt] == tvt
            list_eval.append((tvt, i_test))
    i_val = pdxy[cname_tvt] == 'val'
    list_eval.append(('val', i_val))
    return list_eval


def split_tvt(df, cname_tvt='tvt', time_col='time', test_size=10,
              train_cutoff='2022-06-01', val_cutoff='2023-01-01'):
    """
    Split into train/val/test sets.
    - 10% of tickers are assigned to test (any time within test_cutoff range)
    - train: time < train_cutoff
    - val: time in (train_cutoff + 100 days) to val_cutoff
    - test: test tickers in range (train_cutoff, test_cutoff]
    """
    df[time_col] = pd.to_datetime(df[time_col])
    train_cutoff = pd.to_datetime(train_cutoff)
    val_cutoff = pd.to_datetime(val_cutoff)

    list_ticker = list(df['ticker'].unique())
    random.seed(42)
    random.shuffle(list_ticker)

    test_tickers = [list_ticker[i] for i in range(len(list_ticker)) if i % 100 < test_size]

    is_test_ticker = df['ticker'].isin(test_tickers)
    is_train_val_ticker = ~is_test_ticker

    is_train_time = df[time_col] < train_cutoff
    is_cal_time = (df[time_col] >= train_cutoff) & (df[time_col] < val_cutoff)
    is_val_time = (df[time_col] > val_cutoff)

    # Assign tvt
    df[cname_tvt] = 'other'
    df.loc[is_train_val_ticker & is_train_time, cname_tvt] = 'train'
    df.loc[is_train_val_ticker & is_cal_time, cname_tvt] = 'cal'
    df.loc[is_train_val_ticker & is_val_time, cname_tvt] = 'val'
    df.loc[is_test_ticker & is_train_time, cname_tvt] = 'test_1'
    df.loc[is_test_ticker & is_val_time, cname_tvt] = 'test_2'
    print(df[cname_tvt].value_counts())

    return df


#### Common utils


# ==== metric không dùng threshold ====
def prob_metric(y_true, proba, mode="ap"):
    if mode == 'ap':
        return average_precision_score(y_true, proba)
    if mode == 'roc_auc':
        return roc_auc_score(y_true, proba)
    if mode == 'neg_logloss':
        return -log_loss(y_true, proba, eps=1e-15)
    if mode == 'neg_brier':
        return -brier_score_loss(y_true, proba)
    if mode == 'combo':
        ap = average_precision_score(y_true, proba)
        roc = roc_auc_score(y_true, proba)
        return 0.7 * ap + 0.3 * roc
    raise ValueError("mode không hợp lệ")


# ==== HPO trên một split train/val ====
def run_optuna_on_split(
        X_tr, y_tr, X_va, y_va,
        metric_mode="ap",
        n_trials=150,
        n_jobs=8,
        sampler_seed=42,
        space="wide",  # "wide" hoặc ("narrow", base_params, narrow_space_fn)
        sample_weight=None,
        early_stopping_rounds=200,
):
    def suggest_space(trial):
        # space rộng mặc định
        lr_grid  = [0.0001, 0.001, 0.003, 0.005, 0.007, 0.01, 0.015, 0.02, 0.03, 0.05, 0.07, 0.10, 0.15, 0.20, 0.30]
        reg_grid = [1e-8, 1e-6, 1e-4, 1e-3, 0.01, 0.03, 0.1, 0.3, 1, 3, 10]
        mcw_grid = [1, 1.5, 2, 3, 4, 5, 7, 10]

        params = {
            "objective": "binary:logistic",
            "tree_method": trial.suggest_categorical("tree_method", ["hist"]),
            "learning_rate": trial.suggest_categorical("learning_rate", lr_grid),
            "max_depth": trial.suggest_int("max_depth", 3, 10, step=1),
            # "min_child_weight": float(trial.suggest_float("min_child_weight", 1.0, 10.0, log=True, step=1)),
            "min_child_weight": float(trial.suggest_categorical("min_child_weight", mcw_grid)),
            "subsample": trial.suggest_float("subsample", 0.55, 0.95, step=0.05),
            "colsample_bytree": trial.suggest_float("colsample_bytree", 0.55, 0.95, step=0.05),
            # "reg_alpha": trial.suggest_float("reg_alpha", 1e-8, 10.0, log=True),
            # "reg_lambda": trial.suggest_float("reg_lambda", 1e-8, 10.0, log=True),
            # "gamma": trial.suggest_float("gamma", 0.0, 10.0),
            "reg_alpha": float(trial.suggest_categorical("reg_alpha", reg_grid)),
            "reg_lambda": float(trial.suggest_categorical("reg_lambda", reg_grid)),
            "max_bin": trial.suggest_categorical("max_bin", [128, 256, 512]),
            "gamma": float(trial.suggest_categorical("gamma", [0, 0.5, 1, 2, 3, 5, 7, 10])),

            # mẹo: n_estimators lớn + early stopping quyết định điểm dừng
            "n_estimators": 10000,
            "eval_metric": "logloss",
            "random_state": 42,
            "n_jobs": 1,  # để song song theo trials (n_jobs của study), tránh tranh chấp CPU
            "verbosity": 0,
            "use_label_encoder": False
        }

        if isinstance(space, tuple) and space[0] == "narrow":
            base = space[1];
            narrow_fn = space[2]
            params = {**params, **narrow_fn(trial, base)}
        return params

    def objective(trial):

        params = suggest_space(trial)

        dtrain = xgb.DMatrix(X_tr, label=y_tr, weight=sample_weight)
        dval = xgb.DMatrix(X_va, label=y_va)
        evallist = [(dtrain, "train"), (dval, "validation_0")]

        model = xgb.train(
            params=params,
            dtrain=dtrain,
            num_boost_round=10000,  # n_estimators lớn + early stopping cắt
            evals=evallist,
            callbacks=[
                XGBoostPruningCallback(trial, "validation_0-logloss"),
                EarlyStopping(rounds=200, save_best=True)
            ],
            verbose_eval=False
        )

        proba = model.predict(dval)
        score = prob_metric(y_va, proba, metric_mode)
        return float(score)

    study = optuna.create_study(
        direction="maximize",
        sampler=TPESampler(seed=sampler_seed),
        pruner=MedianPruner(n_warmup_steps=5)
    )
    study.optimize(objective, n_trials=n_trials, n_jobs=n_jobs, show_progress_bar=False)
    return study


# ==== space hẹp quanh best params stage 1 ====
def default_narrow_space(trial, base):
    return {
        "tree_method": trial.suggest_categorical("tree_method", [base.get("tree_method", "hist")]),
        "learning_rate": trial.suggest_float(
            "learning_rate",
            max(1e-3, base["learning_rate"] / 3),
            min(0.3, base["learning_rate"] * 3),
            log=True
        ),
        "max_depth": trial.suggest_int(
            "max_depth",
            max(3, base["max_depth"] - 1),
            min(12, base["max_depth"] + 1)
        ),
        "min_child_weight": trial.suggest_float(
            "min_child_weight",
            max(1.0, base["min_child_weight"] / 2),
            min(10.0, base["min_child_weight"] * 2),
            log=True
        ),
        "subsample": trial.suggest_float(
            "subsample",
            max(0.5, base["subsample"] - 0.2),
            min(1.0, base["subsample"] + 0.2)
        ),
        "colsample_bytree": trial.suggest_float(
            "colsample_bytree",
            max(0.5, base["colsample_bytree"] - 0.2),
            min(1.0, base["colsample_bytree"] + 0.2)
        ),
        "reg_alpha": trial.suggest_float(
            "reg_alpha",
            max(1e-8, base["reg_alpha"] / 5),
            min(10.0, base["reg_alpha"] * 5),
            log=True
        ),
        "reg_lambda": trial.suggest_float(
            "reg_lambda",
            max(1e-8, base["reg_lambda"] / 5),
            min(10.0, base["reg_lambda"] * 5),
            log=True
        ),
        "gamma": trial.suggest_float(
            "gamma",
            max(0.0, base["gamma"] - 2.0),
            min(10.0, base["gamma"] + 2.0)
        ),
        "max_bin": trial.suggest_categorical("max_bin", [128, 256, 512])
    }

# def default_narrow_space(trial, base):
#     # lấy hàng xóm ±1 quanh giá trị base trong grid (giữ kind=categorical)
#     def neighbors(grid, val, k=1):
#         L = sorted(set(grid))
#         if val not in L:
#             L = sorted(set(L + [val]))
#         i = L.index(val)
#         lo, hi = max(0, i - k), min(len(L), i + k + 1)
#         return L[lo:hi] if L[lo:hi] else [val]
#
#     # helper cho float-step: giữ step của wide (0.05), thu hẹp ±0.2 quanh base và clip về biên wide
#     def narrow_float_step(name, low0, high0, step, b, span=0.20):
#         lo = max(low0, b - span)
#         hi = min(high0, b + span)
#         if hi < lo: hi = lo
#         return trial.suggest_float(name, lo, hi, step=step)
#
#     return {
#         # giữ nguyên kind = categorical_fixed
#         "tree_method": trial.suggest_categorical("tree_method", [base.get("tree_method", "hist")]),
#
#         # giữ kind = categorical_grid (thu hẹp bằng hàng xóm)
#         "learning_rate": trial.suggest_categorical("learning_rate",
#                             neighbors(lr_grid, float(base["learning_rate"]), k=1)),
#
#         # giữ kind = int
#         "max_depth": trial.suggest_int("max_depth",
#                         max(3, int(base["max_depth"]) - 1),
#                         min(10, int(base["max_depth"]) + 1),
#                         step=1),
#
#         # giữ kind = categorical_grid
#         "min_child_weight": float(trial.suggest_categorical("min_child_weight",
#                                   neighbors(mcw_grid, float(base["min_child_weight"]), k=1))),
#
#         # giữ kind = float-step (step=0.05 như wide), thu hẹp ±0.2
#         "subsample": narrow_float_step("subsample", 0.55, 0.95, 0.05, float(base["subsample"]), 0.20),
#         "colsample_bytree": narrow_float_step("colsample_bytree", 0.55, 0.95, 0.05, float(base["colsample_bytree"]), 0.20),
#
#         # giữ kind = categorical_grid
#         "reg_alpha": float(trial.suggest_categorical("reg_alpha",
#                          neighbors(reg_grid, float(base["reg_alpha"]), k=1))),
#         "reg_lambda": float(trial.suggest_categorical("reg_lambda",
#                           neighbors(reg_grid, float(base["reg_lambda"]), k=1))),
#         "gamma": float(trial.suggest_categorical("gamma",
#                        neighbors(gamma_grid, float(base["gamma"]), k=1))),
#
#         # giữ kind = categorical_grid (narrow nhất: cố định base)
#         "max_bin": trial.suggest_categorical("max_bin", [int(base["max_bin"])]),
#     }


# ==== train + evaluate + save (dùng chung) ====
def train_eval_save(
        params, X_tr, y_tr, X_va, y_va,
        X_cal, y_cal,
        X_tests: list, y_tests: list, data_tests: list, test_tags: list,
        label_name: str,
        outdir,
        metric_mode="ap",
        early_stopping_rounds=200,
        use_calibrated=False,
        weight_balanced=True
):
    # weight cho imbalance
    sample_weight = compute_sample_weight('balanced', y_tr) if weight_balanced else None

    clf_params = params.copy()
    clf_params.update({
        'objective': 'binary:logistic',
        'n_estimators': clf_params.get('n_estimators', 10000),
        'eval_metric': 'logloss',
        'random_state': 42,
        'verbosity': 0,
        'n_jobs': -1,
        'early_stopping_rounds': early_stopping_rounds
    })
    model = xgb.XGBClassifier(**clf_params)
    model.fit(
        X_tr, y_tr,
        sample_weight=sample_weight,
        eval_set=[(X_va, y_va)],
        verbose=False
    )

    # calibration
    if use_calibrated:
        calibrator = CalibratedClassifierCV(model, method="sigmoid", cv="prefit")
        calibrator.fit(X_cal, y_cal)
        pred_fn = lambda X: calibrator.predict_proba(X)[:, 1]
        joblib.dump(calibrator, outdir / "models" / f"calibrator_{label_name}.joblib")
    else:
        pred_fn = lambda X: model.predict_proba(X)[:, 1]

    # eval train/val
    for tag, X_, y_ in [("TRAIN", X_tr, y_tr), ("VAL", X_va, y_va)]:
        p_ = pred_fn(X_)
        print(f"[{label_name}] {tag}: AP={average_precision_score(y_, p_):.4f} | "
              f"AUC={roc_auc_score(y_, p_):.4f} | -LogLoss={-log_loss(y_, p_, eps=1e-15):.4f} | "
              f"-Brier={-brier_score_loss(y_, p_):.4f}")

    # save artifacts
    joblib.dump(model, outdir / "models" / f"xgb_model_{label_name}.joblib")
    model.get_booster().save_model(str(outdir / "models" / f"xgb_model_{label_name}.json"))

    # tests + lift/profit
    period_tag = '2M' if '2M' in label_name else '3M' if '3M' in label_name else '1M'
    for X_te, y_te, data_te, tg in zip(X_tests, y_tests, data_tests, test_tags):
        p_te = pred_fn(X_te)
        ap = average_precision_score(y_te, p_te)
        auc = roc_auc_score(y_te, p_te)
        nll = -log_loss(y_te, p_te, eps=1e-15)
        bri = -brier_score_loss(y_te, p_te)
        print(f"[{label_name}] {tg}: AP={ap:.4f} | AUC={auc:.4f} | -LogLoss={nll:.4f} | -Brier={bri:.4f}")
        report_lift_table(data_te, y_te, p_te, profit_col=f'profit_{period_tag}')

    return model
