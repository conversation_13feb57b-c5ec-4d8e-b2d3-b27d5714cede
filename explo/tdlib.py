import datetime
import gc
import json
import os
import re
import time

import numpy as np
import requests
import trendln
from pandas import json_normalize
from tqdm import tqdm
import vnstock
from vnstock3 import Vnstock
import pandas as pd
from core_utils.gcsheet import GoogleSheetServices
from explo.falib import *
###
from explo.talib import *
from core_utils.dictionary import DICTIONARY
from collections import defaultdict
import datetime

###

DICT_SHIFT = {'1W': 5, '2W': 10, '1M': 20, '3M': 60, '6M': 120, '1Y': 240, '2Y': 480, '3Y': 720, '5Y': 1200}
pd.set_option('future.no_silent_downcasting', True)


# def multi_process(func, list_input, num_proc=7):
#     with Pool(num_proc) as p:
#         output = p.map(func, list_input)
#     return output


def AMIRSI(df_input, period):
    df = df_input[['Close']].reset_index(drop=True).copy()
    """Calculate RSI using the same logic as AmiBroker."""
    df['diff'] = df['Close'].diff()
    df['W'] = df['diff'].apply(lambda x: x if x > 0 else 0)
    df['S'] = df['diff'].apply(lambda x: -x if x < 0 else 0)

    df['P'] = 0.0
    df['N'] = 0.0

    for i in range(1, len(df)):
        df.loc[i, 'P'] = ((period - 1) * df.loc[i - 1, 'P'] + df.loc[i, 'W']) / period
        df.loc[i, 'N'] = ((period - 1) * df.loc[i - 1, 'N'] + df.loc[i, 'S']) / period

    df['RSI'] = df['P'] / (df['P'] + df['N'])

    return df['RSI'].values


def CMB(pd00_input, rsi_length=14, rsi_mom_length=9, rsi_ma_length=3, ma_length=3, fastLength=13, slowLength=33):
    """Calculate CMB using RSI the same logic as AmiBroker."""

    pd00 = pd00_input.copy()
    pd00['RSI'] = AMIRSI(pd00, rsi_length)
    pd00['RSI_DELTA'] = MOM(pd00, rsi_mom_length, price='RSI')
    pd00['RSI2'] = AMIRSI(pd00, rsi_ma_length)
    pd00['RSI_SMA'] = SMA(pd00, ma_length, key='RSI2')
    pd00['CMB'] = pd00['RSI_DELTA'] + pd00['RSI_SMA']
    pd00['CMB-Fast'] = SMA(pd00, fastLength, key='CMB')
    pd00['CMB-Slow'] = SMA(pd00, slowLength, key='CMB')
    return pd00['CMB'], pd00['CMB-Fast'], pd00['CMB-Slow']


def STOKN(df, n=14):
    """
    Stochastic oscillator %K last n periods
    %K = (Current Close - Lowest Low)/(Highest High - Lowest Low) * 100
    """
    df['Lowest Low'] = df['Low'].rolling(n).min()
    df['Highest High'] = df['High'].rolling(n).max()
    result = pd.Series((df['Close'] - df['Lowest Low']) / (df['Highest High'] - df['Lowest Low']), name='SO%k')
    del df['Lowest Low'], df['Highest High']
    return out(SETTINGS, df, result)


def compute_indicator_v1(pdraw, cname_time='time', dictionary=[], options={}):
    """ Compute indicator given daily price data
    Basic:
        D-RSI (14)
        D-MFI (14)
        D-ADX
        D-ATR
        %K14 : # 
        %D14 : 
        C_H1Y  : # Current / Highest 1W, 1M, 3M, 6M, 1Y , 2Y
        D-CMB (Fast/Slow)
        W-CMB (Fast/Slow)
        M-CMB
    Advanced:
        D-CMB-XFast   : 0 .. M # indicate how many periods since the CMB crossed CMB Fast
        D-CMB-XSlow   : 0 .. M # indicate how many periods since the CMB crossed CMB Slow
        W-CMB-Peak    : -1, 0, 1 # indicate if weekly CMB has just got top (1) or bottom (-1) in the previous period
        W-CMB-Step    : -inf .. inf # CMB divergence step
        W-CMB-LEN     : 0 .. M # CMB positive divergence orginal length between two peaks
        W-CMB-LAG     : 0 .. M # CMB positive divergence delay
        M-CMB-peak-T1 : -1 .. 2 # Value at the latest top/bottom
        M-CMB-peak-T2 : -1 .. 2 # Value at the previous top/bottom
    """
    pd00 = pdraw.copy()
    pd00['year'] = pd.to_datetime(pd00['time'], format='%Y-%m-%d').dt.year
    current_year = pd.Timestamp.now().year
    dictionary = list(DICTIONARY.keys())
    all_cols = [col for col in dictionary if col not in pd00.columns]
    all_cols.sort()
    new_cols_df = pd.DataFrame(np.nan, index=pd00.index, columns=all_cols)

    # Concatenate the new columns DataFrame with the original DataFrame
    pd00 = pd.concat([pd00, new_cols_df], axis=1)

    pd00['Volume_MaxTop5_2Y_ID'], pd00['Volume_MaxTop5_2Y_High'], pd00['Volume_MaxTop5_2Y_Low'], pd00[
        'Volume_MaxTop5_2Y_Close'], pd00['Volume_MaxTop5_2Y'] = TOP_N_NEAREST(pd00, cname_x='Volume', period=240 * 2,
                                                                              top_n=5,
                                                                              cname_retrieve=['High', 'Low', 'Close'])
    pd00['Volume_Max5Y_ID'], pd00['Volume_Max5Y_High'], pd00['Volume_Max5Y_Low'], pd00['Volume_Max5Y_Close'], pd00[
        'Volume_Max5Y'] = EXTREME(pd00, cname_x='Volume', type="max", period=240 * 5,
                                  cname_retrieve=['High', 'Low', 'Close'])

    pd00['Volume_Max2Y_ID'], pd00['Volume_Max2Y_High'], pd00['Volume_Max2Y_Low'], pd00['Volume_Max2Y_Close'], pd00[
        'Volume_Max2Y'] = EXTREME(pd00, cname_x='Volume', type="max", period=240 * 2,
                                  cname_retrieve=['High', 'Low', 'Close'])

    pd00['Volume_Max1Y_ID'], pd00['Volume_Max1Y_High'], pd00['Volume_Max1Y_Low'], pd00['Volume_Max1Y_Close'], pd00[
        'Volume_Max1Y'] = EXTREME(pd00, cname_x='Volume', type="max", period=240,
                                  cname_retrieve=['High', 'Low', 'Close'])

    for rate in range(5, 11):
        pd00[f'Inflation_{rate}'] = ((1 - rate / 100) ** (current_year - pd00['year'])).round(4)

    pd00['Volume_1W'] = pd00['Volume'].rolling(5).mean()
    pd00['Volume_1M'] = pd00['Volume'].rolling(20).mean()
    pd00['Volume_1M_P50'] = pd00['Volume'].rolling(window=20).agg(lambda x: np.percentile(x, 50))

    pd00['Volume_3M'] = pd00['Volume'].rolling(3 * 20).mean()
    pd00['Volume_3M_P90'] = pd00['Volume'].rolling(window=20 * 3).agg(lambda x: np.percentile(x, 90))
    pd00['Volume_3M_P80'] = pd00['Volume'].rolling(window=20 * 3).agg(lambda x: np.percentile(x, 80))
    pd00['Volume_3M_P50'] = pd00['Volume'].rolling(window=20 * 3).agg(lambda x: np.percentile(x, 50))

    pd00['Volume_6M_P50'] = pd00['Volume'].rolling(window=20 * 6).agg(lambda x: np.percentile(x, 50))
    pd00['Volume_1Y_P50'] = pd00['Volume'].rolling(window=240 * 1).agg(lambda x: np.percentile(x, 50))

    pd00['ID_LO_2Y'] = pd00['Low'].rolling(window=240 * 2, min_periods=240).agg(lambda x: x.idxmin())
    pd00['ID_HI_2Y'] = pd00['High'].rolling(window=240 * 2, min_periods=240).agg(lambda x: x.idxmax())
    pd00['ID_LO_3Y'] = pd00['Low'].rolling(window=240 * 3, min_periods=240 * 2).agg(lambda x: x.idxmin())
    pd00['ID_HI_3Y'] = pd00['High'].rolling(window=240 * 3, min_periods=240 * 2).agg(lambda x: x.idxmax())
    pd00['ID_LO_5Y'] = pd00['Low'].rolling(window=240 * 5, min_periods=3 * 240).agg(lambda x: x.idxmin())
    pd00['ID_HI_5Y'] = pd00['High'].rolling(window=240 * 5, min_periods=3 * 240).agg(lambda x: x.idxmax())
    pd00['ID_Current'] = pd00.index

    pd00['MA10'] = MA(pd00, n=10, price='Close')
    pd00['MA20'] = MA(pd00, n=20, price='Close')
    pd00['MA50'] = MA(pd00, n=50, price='Close')
    pd00['MA100'] = MA(pd00, n=100, price='Close')
    pd00['MA200'] = MA(pd00, n=200, price='Close')

    pd00['MA10_T1'] = pd00['MA10'].shift(1)
    pd00['MA20_T1'] = pd00['MA20'].shift(1)
    pd00['MA50_T1'] = pd00['MA50'].shift(1)
    pd00['MA100_T1'] = pd00['MA100'].shift(1)
    pd00['MA200_T1'] = pd00['MA200'].shift(1)
    pd00['MA200_T100'] = pd00['MA200'].shift(100)
    pd00['MA200_T200'] = pd00['MA200'].shift(200)

    pd00['ID_C_XMA200'], pd00['ID_C_XMA200_P1'], _ = CROSS_SIGNAL(pd00, 'Close', 'MA200')

    pd00['D_RSI'] = AMIRSI(pd00, 14)
    pd00['D_RSI_T1'] = pd00['D_RSI'].shift(1)
    pd00['D_RSI_T1W'] = pd00['D_RSI'].shift(5)

    pd00['D_CMF'] = CMF(pd00, 20)
    pd00['D_CMF_T1'] = pd00['D_CMF'].shift(1)
    pd00['D_CMF_T1W'] = pd00['D_CMF'].shift(5)
    pd00['D_MFI'], pd00['D_RawMFI'] = MFI(pd00, 14)
    pd00['D_MFI_T1'] = pd00['D_MFI'].shift(1)
    pd00['D_MFI_T1W'] = pd00['D_MFI'].shift(5)
    pd00['D_RawMFI_T1W'] = pd00['D_RawMFI'].shift(5)
    pd00['D_ADX'] = ADX(pd00, 14, 14)  #
    pd00['D_ATR'] = ATR(pd00, 14)
    pd00['D_ATR'] = pd00['D_ATR'].shift(1) / pd00['Close']  #
    pd00['D_MACD'], pd00['D_MACDsign'], pd00['D_MACDdiff'] = MACD(pd00, 12, 26)
    pd00['D_MACD_T1'] = pd00['D_MACD'].shift(1)
    pd00['D_MACD_T1W'] = pd00['D_MACD'].shift(5)
    pd00['D_MACDsign_T1W'] = pd00['D_MACDsign'].shift(5)
    pd00['D_MACDdiff_T1W'] = pd00['D_MACDdiff'].shift(5)

    for period, p in zip(['T3', '1W', '1M', '3M'], [3, 5, 20, 60]):
        pd00[f'D_RSI_Max{period}'], pd00[f'D_RSI_Max{period}_Close'], pd00[f'D_RSI_Max{period}_Volume'], pd00[
            f'D_RSI_Max{period}_MACD'], pd00[f'D_RSI_Max{period}_MACDdiff'], pd00[f'D_RSI_Max{period}_MFI'], pd00[
            f'D_RSI_Max{period}_CMF'] = PEAK_SIGNAL(pd00, cname_x='D_RSI',
                                                    cname_retrieve=['Close', 'Volume', 'D_MACD', 'D_MACDdiff', 'D_MFI',
                                                                    'D_CMF'],
                                                    period=p, type='max')
        pd00[f'D_RSI_Min{period}'], pd00[f'D_RSI_Min{period}_Close'], pd00[f'D_RSI_Min{period}_Volume'], pd00[
            f'D_RSI_Min{period}_MACD'], pd00[f'D_RSI_Min{period}_MACDdiff'], pd00[f'D_RSI_Min{period}_MFI'], pd00[
            f'D_RSI_Min{period}_CMF'] = PEAK_SIGNAL(pd00, cname_x='D_RSI',
                                                    cname_retrieve=['Close', 'Volume', 'D_MACD', 'D_MACDdiff', 'D_MFI',
                                                                    'D_CMF'],
                                                    period=p, type='min')

        # pd00[f'MFI_{period}_MAX'], pd00[f'C_{period}_MFIMAX'], pd00[f'MACD_{period}_MFIMAX'], pd00[
        #     f'RSI_{period}_MFIMAX'] = PEAK_SIGNAL(pd00, cname_x='D_MFI', cname_retrieve=['D_MACD', 'D_RSI'], period=p,
        #                                           type='max')
        # pd00[f'MFI_{period}_MIN'], pd00[f'C_{period}_MFIMIN'], pd00[f'MACD_{period}_MFIMIN'], pd00[
        #     f'RSI_{period}_MFIMIN'] = PEAK_SIGNAL(pd00, cname_x='D_MFI', cname_retrieve=['D_MACD', 'D_RSI'], period=p,
        #                                           type='min')

    pd00['STOK14'] = STOKN(pd00, 14)
    pd00['STOD14'] = SMA(pd00, 3, key='STOK14')
    pd00['C_H1W'] = pd00['Close'] / pd00['High'].rolling(5).max()
    pd00['C_H1M'] = pd00['Close'] / pd00['High'].rolling(20).max()
    pd00['C_H3M'] = pd00['Close'] / pd00['High'].rolling(3 * 20).max()
    pd00['C_H6M'] = pd00['Close'] / pd00['High'].rolling(6 * 20).max()
    pd00['C_H1Y'] = pd00['Close'] / pd00['High'].rolling(12 * 20).max()
    pd00['C_H2Y'] = pd00['Close'] / pd00['High'].rolling(24 * 20).max()
    pd00['C_L1W'] = pd00['Close'] / pd00['Low'].rolling(5).min()
    pd00['C_L1M'] = pd00['Close'] / pd00['Low'].rolling(20).min()
    pd00['C_L3M'] = pd00['Close'] / pd00['Low'].rolling(3 * 20).min()
    pd00['C_L1Y'] = pd00['Close'] / pd00['Low'].rolling(12 * 20).min()
    pd00['C_L2Y'] = pd00['Close'] / pd00['Low'].rolling(24 * 20).min()

    pd00['HI_3M_T1'] = (pd00['Close'] / pd00['C_H3M']).shift(1)
    pd00['HI_1M_T1'] = (pd00['Close'] / pd00['C_H1M']).shift(1)
    pd00['LO_3M_T1'] = (pd00['Close'] / pd00['C_L3M']).shift(1)
    pd00['LO_1M_T1'] = (pd00['Close'] / pd00['C_L1M']).shift(1)

    pd00['Close_T1'] = pd00['Close'].shift(1)
    pd00['Close_T1W'] = pd00['Close'].shift(5)
    pd00['Close_T1M'] = pd00['Close'].shift(20)
    pd00['Close_1M'] = pd00['Close'].shift(-20)
    pd00['Open_1D'] = pd00['Open'].shift(-1).fillna(pd00['Close'].iloc[-1])

    pd00['Change_T1'] = ((pd00['Close'] / pd00['Close_T1']) - 1) * 100
    pd00['Change_T5'] = ((pd00['Close'] / pd00['Close_T1W']) - 1) * 100
    pd00['Change_1W'] = pd00['Change_T1'].rolling(5).mean()
    pd00['Change_1M'] = pd00['Change_T1'].rolling(20).mean()
    pd00['Change_3M'] = pd00['Change_T1'].rolling(20 * 3).mean()

    pd00['D_CMB'], pd00['D_CMB_Fast'], pd00['D_CMB_Slow'] = CMB(pd00)
    pd00['D_CMB_Peak'], pd00['D_CMB_Peak_T1'], _ = PEAK(pd00, 'D_CMB')

    pd00['D_CMB_XFast'] = CROSS(pd00, 'D_CMB', 'D_CMB_Fast')
    pd00['D_CMB_XSlow'] = CROSS(pd00, 'D_CMB', 'D_CMB_Slow')
    pd00['D_CMB_Step'], pd00['D_CMB_LEN'], pd00['D_CMB_LAG'] = DIVERGENCE(pd00,
                                                                          'D_CMB', 'Close', max_length=24, min_length=2)

    pd00['STrend_L'], _ = SUPPORT_TRENDLINE_v1(pd00, 'Low', MIN_LENGTH=20, MAX_LENGTH=240, TOLERANCE=0.01,
                                               ratio_threshold=(0.955, 0.1, 0.08))
    pd00['STrend_S'], _ = SUPPORT_TRENDLINE_v1(pd00, 'Low', MIN_LENGTH=5, MAX_LENGTH=120, TOLERANCE=1e-4,
                                               ratio_threshold=(0.97, 0.1, 0.03))

    pd00['VAP1W'] = VAP_v1(pd00, period=5)
    pd00['VAP2W'] = VAP_v1(pd00, period=10)
    pd00['VAP1M'] = VAP_v1(pd00, period=20)
    pd00['VAP3M'] = VAP_v1(pd00, period=20 * 3)
    pd00['VAP6M'] = VAP_v1(pd00, period=20 * 6)
    pd00['Res_1Y'], pd00['Sup_1Y'] = RES_SUP_v1(pd00, period=240)
    pd00['Res_1Y_T1'] = pd00['Res_1Y'].shift(1)
    pd00['Sup_1Y_T1'] = pd00['Sup_1Y'].shift(1)

    pd00['ID_C_XVAP1M_Up_P0'], pd00['ID_C_XVAP1M_Up_P1'], pd00['ID_C_XVAP1M_Up_P2'] = CROSS_SIGNAL(pd00, 'Close',
                                                                                                   'VAP1M',
                                                                                                   strategy='up',
                                                                                                   offset=0.05)
    pd00['ID_C_XVAP1M_Down_P0'], pd00['ID_C_XVAP1M_Down_P1'], pd00['ID_C_XVAP1M_Down_P2'] = CROSS_SIGNAL(pd00, 'Close',
                                                                                                         'VAP1M',
                                                                                                         strategy='down',
                                                                                                         offset=0.05)
    pd00['ID_C_XVAP3M_Up_P0'], pd00['ID_C_XVAP3M_Up_P1'], pd00['ID_C_XVAP3M_Up_P2'] = CROSS_SIGNAL(pd00, 'Close',
                                                                                                   'VAP3M',
                                                                                                   strategy='up',
                                                                                                   offset=0.05)
    pd00['ID_C_XVAP3M_Down_P0'], pd00['ID_C_XVAP3M_Down_P1'], pd00['ID_C_XVAP3M_Down_P2'] = CROSS_SIGNAL(pd00, 'Close',
                                                                                                         'VAP3M',
                                                                                                         strategy='down',
                                                                                                         offset=0.05)
    pd00['ID_XVAP1M_Up_P0'], pd00['ID_XVAP1M_Up_P1'], pd00['ID_XVAP1M_Up_P2'] = CROSS_VALUE(pd00, 'Close',
                                                                                            'VAP1M', strategy='up',
                                                                                            duration=60, offset=0.05)
    pd00['ID_XVAP1M_Down_P0'], pd00['ID_XVAP1M_Down_P1'], pd00['ID_XVAP1M_Down_P2'] = CROSS_VALUE(pd00, 'Close',
                                                                                                  'VAP1M',
                                                                                                  strategy='down',
                                                                                                  duration=60,
                                                                                                  offset=0.05)

    pd00['ID_XVAP3M_Up_P0'], pd00['ID_XVAP3M_Up_P1'], pd00['ID_XVAP3M_Up_P2'] = CROSS_VALUE(pd00, 'Close',
                                                                                            'VAP3M', strategy='up',
                                                                                            duration=60, offset=0.05)
    pd00['ID_XVAP3M_Down_P0'], pd00['ID_XVAP3M_Down_P1'], pd00['ID_XVAP3M_Down_P2'] = CROSS_VALUE(pd00, 'Close',
                                                                                                  'VAP3M',
                                                                                                  strategy='down',
                                                                                                  duration=60,
                                                                                                  offset=0.05)

    pd00['vol1M'], pd00['vol1M_Low'], pd00['vol3M'], pd00['vol3M_Low'] = VOL1M3M(pd00)
    pd00.dropna(axis=1, how='all', inplace=True)

    pd00 = pd00.merge(get_candle_stick(pd00, len=options.get("len", 5), code='D'), on='time', how='left')

    T = pdraw.shape[0]
    # weekly CMB
    ll = []
    for t in range(0, 5, 1):
        pd_resample = resample_data(pdraw[:(T - t)], n=5, cname_time=cname_time)
        pdx = get_candle_stick(pd_resample, len=options.get("len", 5), code='W')
        list_col = [f for f in pdx.columns if f not in ['time']]
        pd_resample = pd_resample.merge(pdx, on='time', how='left')
        pd_resample['W_CMB'], pd_resample['W_CMB_Fast'], pd_resample['W_CMB_Slow'] = CMB(pd_resample)
        pd_resample['W_CMB_Peak'], pd_resample['W_CMB_Peak_T1'], _ = PEAK(pd_resample, 'W_CMB')
        pd_resample['W_CMB_Step'], pd_resample['W_CMB_LEN'], pd_resample['W_CMB_LAG'] = DIVERGENCE(pd_resample,
                                                                                                   'W_CMB', 'Close',
                                                                                                   max_length=24,
                                                                                                   min_length=2)
        ll.append(pd_resample[['time', 'W_CMB', 'W_CMB_Fast', 'W_CMB_Slow', 'W_CMB_Peak', 'W_CMB_Peak_T1', 'W_CMB_Step',
                               'W_CMB_LEN', 'W_CMB_LAG'] + list_col])
        # pd_resample[['time','Close','W_CMB','W_CMB_Fast','W_CMB_Slow','W_CMB_Peak','W_CMB_Step','W_CMB_LEN','W_CMB_LAG']].to_csv('../ticker/debug.csv',index=False)
    pd01 = pd.concat(ll, axis=0).sort_values(by='time').reset_index(drop=True)
    # monthly
    ll = []
    for t in range(0, 20, 1):
        pd_resample = resample_data(pdraw[:(T - t)], n=20, cname_time=cname_time)
        pdx = get_candle_stick(pd_resample, len=options.get("len", 5), code='M')
        list_col = [f for f in pdx.columns if f not in ['time']]
        pd_resample = pd_resample.merge(pdx, on='time', how='left')
        pd_resample['M_CMB'], pd_resample['M_CMB_Fast'], pd_resample['M_CMB_Slow'] = CMB(pd_resample)
        _, pd_resample['M_CMB_Peak_T1'], pd_resample['M_CMB_Peak_T2'] = PEAK(pd_resample, 'M_CMB')
        pd_resample['M_CMB_Step'], pd_resample['M_CMB_LEN'], pd_resample['M_CMB_LAG'] = DIVERGENCE(pd_resample,
                                                                                                   'M_CMB', 'Close',
                                                                                                   max_length=24,
                                                                                                   min_length=2)
        ll.append(pd_resample[['time', 'M_CMB', 'M_CMB_Peak_T1', 'M_CMB_Peak_T2', 'M_CMB_Step', 'M_CMB_LEN',
                               'M_CMB_LAG'] + list_col])
    pd02 = pd.concat(ll, axis=0).sort_values(by='time').reset_index(drop=True)

    pd03 = (pd00
            .merge(pd01, on='time', how='left')
            .merge(pd02, on='time', how='left')
            )

    for k in ['W_CMB', 'W_CMB_Fast', 'W_CMB_Slow', 'W_CMB_Peak', 'W_CMB_Peak_T1', 'W_CMB_Step', 'W_CMB_LEN',
              'W_CMB_LAG',
              'M_CMB', 'M_CMB_Peak_T1', 'M_CMB_Peak_T2', 'M_CMB_Step', 'M_CMB_LEN', 'M_CMB_LAG']:
        pd03[k] = pd03[k].ffill()

    return pd03


def compute_indicator_daily(pdraw, cname_time='time', dictionary=[], options={}):
    """ Compute indicator given daily price data
    Basic:
        D-RSI (14)
        D-MFI (14)
        D-ADX
        D-ATR
        %K14 : #
        %D14 :
        C_H1Y  : # Current / Highest 1W, 1M, 3M, 6M, 1Y , 2Y
        D-CMB (Fast/Slow)
        W-CMB (Fast/Slow)
        M-CMB
    Advanced:
        D-CMB-XFast   : 0 .. M # indicate how many periods since the CMB crossed CMB Fast
        D-CMB-XSlow   : 0 .. M # indicate how many periods since the CMB crossed CMB Slow
        W-CMB-Peak    : -1, 0, 1 # indicate if weekly CMB has just got top (1) or bottom (-1) in the previous period
        W-CMB-Step    : -inf .. inf # CMB divergence step
        W-CMB-LEN     : 0 .. M # CMB positive divergence orginal length between two peaks
        W-CMB-LAG     : 0 .. M # CMB positive divergence delay
        M-CMB-peak-T1 : -1 .. 2 # Value at the latest top/bottom
        M-CMB-peak-T2 : -1 .. 2 # Value at the previous top/bottom
    """

    pd00 = pdraw.copy()
    pd00['ID_Current'] = pd00.index
    pd00['year'] = pd.to_datetime(pd00['time'], format='%Y-%m-%d').dt.year
    current_year = pd.Timestamp.now().year

    dictionary = list(DICTIONARY.keys())
    all_cols = [col for col in dictionary if col not in pd00.columns]
    all_cols.sort()
    new_cols_df = pd.DataFrame(np.nan, index=pd00.index, columns=all_cols)

    # Concatenate the new columns DataFrame with the original DataFrame
    pd_result = pd00.copy()

    _pd00 = pd.concat([pd00, new_cols_df], axis=1)
    offset = 40

    # 1 month
    pd00 = _pd00.tail(DICT_SHIFT['1M'] + offset).reset_index(drop=True)
    for rate in range(5, 11):
        pd00[f'Inflation_{rate}'] = ((1 - rate / 100) ** (current_year - pd00['year'])).round(4)

    pd00['Volume_1W'] = pd00['Volume'].rolling(5).mean()
    pd00['Volume_1M'] = pd00['Volume'].rolling(20).mean()
    pd00['Volume_1M_P50'] = pd00['Volume'].rolling(window=20).agg(lambda x: np.percentile(x, 50))

    pd00['C_H1W'] = pd00['Close'] / pd00['High'].rolling(5).max()
    pd00['C_L1W'] = pd00['Close'] / pd00['Low'].rolling(5).min()
    pd00['C_H1M'] = pd00['Close'] / pd00['High'].rolling(20).max()
    pd00['C_L1M'] = pd00['Close'] / pd00['Low'].rolling(20).min()
    pd00['HI_1M_T1'] = (pd00['Close'] / pd00['C_H1M']).shift(1)
    pd00['LO_1M_T1'] = (pd00['Close'] / pd00['C_L1M']).shift(1)

    pd00.dropna(axis=1, how='all', inplace=True)
    cols = [col for col in pd00.columns if col not in pd_result.columns] + ['ID_Current']
    pd_result = pd_result.merge(pd00[cols], on=['ID_Current'], how='left')

    # 3 months
    pd00 = _pd00.tail(DICT_SHIFT['3M'] + offset).reset_index(drop=True)

    pd00['Volume_3M'] = pd00['Volume'].rolling(3 * 20).mean()
    pd00['Volume_3M_P90'] = pd00['Volume'].rolling(window=20 * 3).agg(lambda x: np.percentile(x, 90))
    pd00['Volume_3M_P80'] = pd00['Volume'].rolling(window=20 * 3).agg(lambda x: np.percentile(x, 80))
    pd00['Volume_3M_P50'] = pd00['Volume'].rolling(window=20 * 3).agg(lambda x: np.percentile(x, 50))

    pd00['STOK14'] = STOKN(pd00, 14)
    pd00['STOD14'] = SMA(pd00, 3, key='STOK14')

    pd00['C_H3M'] = pd00['Close'] / pd00['High'].rolling(3 * 20).max()
    pd00['C_L3M'] = pd00['Close'] / pd00['Low'].rolling(3 * 20).min()
    pd00['HI_3M_T1'] = (pd00['Close'] / pd00['C_H3M']).shift(1)
    pd00['LO_3M_T1'] = (pd00['Close'] / pd00['C_L3M']).shift(1)

    pd00['Close_T1'] = pd00['Close'].shift(1)
    pd00['Close_T1W'] = pd00['Close'].shift(5)
    pd00['Close_T1M'] = pd00['Close'].shift(20)
    pd00['Close_1M'] = pd00['Close'].shift(-20)
    pd00['Open_1D'] = pd00['Open'].shift(-1).fillna(pd00['Close'].iloc[-1])
    pd00['Change_T1'] = ((pd00['Close'] / pd00['Close_T1']) - 1) * 100
    pd00['Change_T5'] = ((pd00['Close'] / pd00['Close_T1W']) - 1) * 100
    pd00['Change_1W'] = pd00['Change_T1'].rolling(5).mean()
    pd00['Change_1M'] = pd00['Change_T1'].rolling(20).mean()
    pd00['Change_3M'] = pd00['Change_T1'].rolling(20 * 3).mean()

    pd00.dropna(axis=1, how='all', inplace=True)
    cols = [col for col in pd00.columns if col not in pd_result.columns] + ['ID_Current']
    pd_result = pd_result.merge(pd00[cols], on=['ID_Current'], how='left')

    # 6 months
    pd00 = _pd00.tail(DICT_SHIFT['6M'] + offset).reset_index(drop=True)

    pd00['Volume_6M_P50'] = pd00['Volume'].rolling(window=20 * 6).agg(lambda x: np.percentile(x, 50))
    pd00['C_H6M'] = pd00['Close'] / pd00['High'].rolling(6 * 20).max()

    pd00['D_CMB'], pd00['D_CMB_Fast'], pd00['D_CMB_Slow'] = CMB(pd00)
    pd00['D_CMB_Peak'], pd00['D_CMB_Peak_T1'], _ = PEAK(pd00, 'D_CMB')

    pd00['D_CMB_XFast'] = CROSS(pd00, 'D_CMB', 'D_CMB_Fast')
    pd00['D_CMB_XSlow'] = CROSS(pd00, 'D_CMB', 'D_CMB_Slow')
    pd00['D_CMB_Step'], pd00['D_CMB_LEN'], pd00['D_CMB_LAG'] = DIVERGENCE(pd00,
                                                                          'D_CMB', 'Close', max_length=24, min_length=2)
    pd00['VAP1W'] = VAP_v1(pd00, period=5)
    pd00['VAP2W'] = VAP_v1(pd00, period=10)
    pd00['VAP6M'] = VAP_v1(pd00, period=20 * 6)
    pd00['vol1M'], pd00['vol1M_Low'], pd00['vol3M'], pd00['vol3M_Low'] = VOL1M3M(pd00)

    pd00['MA10'] = MA(pd00, n=10, price='Close')
    pd00['MA20'] = MA(pd00, n=20, price='Close')
    pd00['MA50'] = MA(pd00, n=50, price='Close')
    pd00['MA100'] = MA(pd00, n=100, price='Close')

    pd00['MA10_T1'] = pd00['MA10'].shift(1)
    pd00['MA20_T1'] = pd00['MA20'].shift(1)
    pd00['MA50_T1'] = pd00['MA50'].shift(1)
    pd00['MA100_T1'] = pd00['MA100'].shift(1)

    pd00.dropna(axis=1, how='all', inplace=True)
    cols = [col for col in pd00.columns if col not in pd_result.columns] + ['ID_Current']
    pd_result = pd_result.merge(pd00[cols], on=['ID_Current'], how='left')

    # 1 year
    pd00 = _pd00.tail(DICT_SHIFT['1Y'] + offset).reset_index(drop=True)
    pd00['Volume_1Y_P50'] = pd00['Volume'].rolling(window=240 * 1).agg(lambda x: np.percentile(x, 50))

    pd00['C_H1Y'] = pd00['Close'] / pd00['High'].rolling(12 * 20).max()
    pd00['C_L1Y'] = pd00['Close'] / pd00['Low'].rolling(12 * 20).min()

    pd00['Res_1Y'], pd00['Sup_1Y'] = RES_SUP_v1(pd00, period=240)
    pd00['Res_1Y_T1'] = pd00['Res_1Y'].shift(1)
    pd00['Sup_1Y_T1'] = pd00['Sup_1Y'].shift(1)

    pd00.dropna(axis=1, how='all', inplace=True)
    cols = [col for col in pd00.columns if col not in pd_result.columns] + ['ID_Current']
    pd_result = pd_result.merge(pd00[cols], on=['ID_Current'], how='left')

    # 2 years
    pd00 = _pd00.tail(DICT_SHIFT['2Y'] + offset).reset_index(drop=True)
    ###
    pd00['D_RSI'] = AMIRSI(pd00, 14)
    pd00['D_RSI_T1'] = pd00['D_RSI'].shift(1)
    pd00['D_RSI_T1W'] = pd00['D_RSI'].shift(5)
    pd00['D_CMF'] = CMF(pd00, 20)
    pd00['D_CMF_T1'] = pd00['D_CMF'].shift(1)
    pd00['D_CMF_T1W'] = pd00['D_CMF'].shift(5)
    pd00['D_MFI'], pd00['D_RawMFI'] = MFI(pd00, 14)
    pd00['D_MFI_T1'] = pd00['D_MFI'].shift(1)
    pd00['D_MFI_T1W'] = pd00['D_MFI'].shift(5)
    pd00['D_RawMFI_T1W'] = pd00['D_RawMFI'].shift(5)
    pd00['D_ADX'] = ADX(pd00, 14, 14)  #
    pd00['D_ATR'] = ATR(pd00, 14)
    pd00['D_ATR'] = pd00['D_ATR'].shift(1) / pd00['Close']  #
    pd00['D_MACD'], pd00['D_MACDsign'], pd00['D_MACDdiff'] = MACD(pd00, 12, 26)
    pd00['D_MACD_T1'] = pd00['D_MACD'].shift(1)
    pd00['D_MACD_T1W'] = pd00['D_MACD'].shift(5)
    pd00['D_MACDsign_T1W'] = pd00['D_MACDsign'].shift(5)
    pd00['D_MACDdiff_T1W'] = pd00['D_MACDdiff'].shift(5)
    for period, p in zip(['T3', '1W', '1M', '3M'], [3, 5, 20, 60]):
        pd00[f'D_RSI_Max{period}'], pd00[f'D_RSI_Max{period}_Close'], pd00[f'D_RSI_Max{period}_Volume'], pd00[
            f'D_RSI_Max{period}_MACD'], pd00[f'D_RSI_Max{period}_MACDdiff'], pd00[f'D_RSI_Max{period}_MFI'], pd00[
            f'D_RSI_Max{period}_CMF'] = PEAK_SIGNAL(pd00, cname_x='D_RSI',
                                                    cname_retrieve=['Close', 'Volume', 'D_MACD', 'D_MACDdiff', 'D_MFI',
                                                                    'D_CMF'],
                                                    period=p, type='max')
        pd00[f'D_RSI_Min{period}'], pd00[f'D_RSI_Min{period}_Close'], pd00[f'D_RSI_Min{period}_Volume'], pd00[
            f'D_RSI_Min{period}_MACD'], pd00[f'D_RSI_Min{period}_MACDdiff'], pd00[f'D_RSI_Min{period}_MFI'], pd00[
            f'D_RSI_Min{period}_CMF'] = PEAK_SIGNAL(pd00, cname_x='D_RSI',
                                                    cname_retrieve=['Close', 'Volume', 'D_MACD', 'D_MACDdiff', 'D_MFI',
                                                                    'D_CMF'],
                                                    period=p, type='min')

    pd00['C_H2Y'] = pd00['Close'] / pd00['High'].rolling(24 * 20).max()
    pd00['C_L2Y'] = pd00['Close'] / pd00['Low'].rolling(24 * 20).min()

    pd00.dropna(axis=1, how='all', inplace=True)
    cols = [col for col in pd00.columns if col not in pd_result.columns] + ['ID_Current']
    pd_result = pd_result.merge(pd00[cols], on=['ID_Current'], how='left')

    # # 3 years
    # pd00 = _pd00.tail(DICT_SHIFT['3Y'] + offset).reset_index(drop=True)
    #
    #
    #
    # pd00.dropna(axis=1, how='all', inplace=True)
    # cols = [col for col in pd00.columns if col not in pd_result.columns] + ['ID_Current']
    # pd_result = pd_result.merge(pd00[cols], on=['ID_Current'], how='left')

    # All-times
    pd00 = _pd00.copy()

    pd00['VAP1M'] = VAP_v1(pd00, period=20)
    pd00['VAP3M'] = VAP_v1(pd00, period=20 * 3)
    pd00['ID_C_XVAP1M_Up_P0'], pd00['ID_C_XVAP1M_Up_P1'], pd00['ID_C_XVAP1M_Up_P2'] = CROSS_SIGNAL(pd00, 'Close',
                                                                                                   'VAP1M',
                                                                                                   strategy='up',
                                                                                                   offset=0.05)
    pd00['ID_C_XVAP1M_Down_P0'], pd00['ID_C_XVAP1M_Down_P1'], pd00['ID_C_XVAP1M_Down_P2'] = CROSS_SIGNAL(pd00, 'Close',
                                                                                                         'VAP1M',
                                                                                                         strategy='down',
                                                                                                         offset=0.05)
    pd00['ID_C_XVAP3M_Up_P0'], pd00['ID_C_XVAP3M_Up_P1'], pd00['ID_C_XVAP3M_Up_P2'] = CROSS_SIGNAL(pd00, 'Close',
                                                                                                   'VAP3M',
                                                                                                   strategy='up',
                                                                                                   offset=0.05)
    pd00['ID_C_XVAP3M_Down_P0'], pd00['ID_C_XVAP3M_Down_P1'], pd00['ID_C_XVAP3M_Down_P2'] = CROSS_SIGNAL(pd00, 'Close',
                                                                                                         'VAP3M',
                                                                                                         strategy='down',
                                                                                                         offset=0.05)
    pd00['ID_XVAP1M_Up_P0'], pd00['ID_XVAP1M_Up_P1'], pd00['ID_XVAP1M_Up_P2'] = CROSS_VALUE(pd00, 'Close',
                                                                                            'VAP1M', strategy='up',
                                                                                            duration=60, offset=0.05)
    pd00['ID_XVAP1M_Down_P0'], pd00['ID_XVAP1M_Down_P1'], pd00['ID_XVAP1M_Down_P2'] = CROSS_VALUE(pd00, 'Close',
                                                                                                  'VAP1M',
                                                                                                  strategy='down',
                                                                                                  duration=60,
                                                                                                  offset=0.05)

    pd00['ID_XVAP3M_Up_P0'], pd00['ID_XVAP3M_Up_P1'], pd00['ID_XVAP3M_Up_P2'] = CROSS_VALUE(pd00, 'Close',
                                                                                            'VAP3M', strategy='up',
                                                                                            duration=60, offset=0.05)
    pd00['ID_XVAP3M_Down_P0'], pd00['ID_XVAP3M_Down_P1'], pd00['ID_XVAP3M_Down_P2'] = CROSS_VALUE(pd00, 'Close',
                                                                                                  'VAP3M',
                                                                                                  strategy='down',
                                                                                                  duration=60,
                                                                                                  offset=0.05)

    pd00['Volume_MaxTop5_2Y_ID'], pd00['Volume_MaxTop5_2Y_High'], pd00['Volume_MaxTop5_2Y_Low'], pd00[
        'Volume_MaxTop5_2Y_Close'], pd00['Volume_MaxTop5_2Y'] = TOP_N_NEAREST(pd00, cname_x='Volume', period=240 * 2,
                                                                              top_n=5,
                                                                              cname_retrieve=['High', 'Low', 'Close'])

    pd00['Volume_Max5Y_ID'], pd00['Volume_Max5Y_High'], pd00['Volume_Max5Y_Low'], pd00['Volume_Max5Y_Close'], pd00[
        'Volume_Max5Y'] = EXTREME(pd00, cname_x='Volume', type="max", period=240 * 5,
                                  cname_retrieve=['High', 'Low', 'Close'])

    pd00['Volume_Max2Y_ID'], pd00['Volume_Max2Y_High'], pd00['Volume_Max2Y_Low'], pd00['Volume_Max2Y_Close'], pd00[
        'Volume_Max2Y'] = EXTREME(pd00, cname_x='Volume', type="max", period=240 * 2,
                                  cname_retrieve=['High', 'Low', 'Close'])

    pd00['Volume_Max1Y_ID'], pd00['Volume_Max1Y_High'], pd00['Volume_Max1Y_Low'], pd00['Volume_Max1Y_Close'], pd00[
        'Volume_Max1Y'] = EXTREME(pd00, cname_x='Volume', type="max", period=240,
                                  cname_retrieve=['High', 'Low', 'Close'])

    pd00['ID_LO_2Y'] = pd00['Low'].rolling(window=240 * 2, min_periods=240).agg(lambda x: x.idxmin())
    pd00['ID_HI_2Y'] = pd00['High'].rolling(window=240 * 2, min_periods=240).agg(lambda x: x.idxmax())
    pd00['ID_LO_3Y'] = pd00['Low'].rolling(window=240 * 3, min_periods=240 * 2).agg(lambda x: x.idxmin())
    pd00['ID_HI_3Y'] = pd00['High'].rolling(window=240 * 3, min_periods=240 * 2).agg(lambda x: x.idxmax())
    pd00['ID_LO_5Y'] = pd00['Low'].rolling(window=240 * 5, min_periods=3 * 240).agg(lambda x: x.idxmin())
    pd00['ID_HI_5Y'] = pd00['High'].rolling(window=240 * 5, min_periods=3 * 240).agg(lambda x: x.idxmax())

    pd00['MA200'] = MA(pd00, n=200, price='Close')
    pd00['MA200_T1'] = pd00['MA200'].shift(1)
    pd00['MA200_T100'] = pd00['MA200'].shift(100)
    pd00['MA200_T200'] = pd00['MA200'].shift(200)
    pd00['ID_C_XMA200'], pd00['ID_C_XMA200_P1'], _ = CROSS_SIGNAL(pd00, 'Close', 'MA200')

    pd00['STrend_L'], _ = SUPPORT_TRENDLINE_v1(pd00, 'Low', MIN_LENGTH=20, MAX_LENGTH=240, TOLERANCE=0.01,
                                               ratio_threshold=(0.955, 0.1, 0.08))
    pd00['STrend_S'], _ = SUPPORT_TRENDLINE_v1(pd00, 'Low', MIN_LENGTH=5, MAX_LENGTH=120, TOLERANCE=1e-4,
                                               ratio_threshold=(0.97, 0.1, 0.03))

    pd00.dropna(axis=1, how='all', inplace=True)
    cols = [col for col in pd00.columns if col not in pd_result.columns] + ['ID_Current']
    pd_result = pd_result.merge(pd00[cols], on=['ID_Current'], how='left')

    pd00 = pd_result.merge(get_candle_stick(_pd00.tail(DICT_SHIFT['1M'] + offset), len=options.get("len", 5), code='D'),
                           on='time', how='left')

    T = pdraw.shape[0]
    # weekly CMB
    ll = []
    for t in range(0, 5, 1):
        pd_resample = resample_data(pdraw[:(T - t)], n=5, cname_time=cname_time)
        pdx = get_candle_stick(pd_resample, len=options.get("len", 5), code='W')
        list_col = [f for f in pdx.columns if f not in ['time']]
        pd_resample = pd_resample.merge(pdx, on='time', how='left')
        pd_resample['W_CMB'], pd_resample['W_CMB_Fast'], pd_resample['W_CMB_Slow'] = CMB(pd_resample)
        pd_resample['W_CMB_Peak'], pd_resample['W_CMB_Peak_T1'], _ = PEAK(pd_resample, 'W_CMB')
        pd_resample['W_CMB_Step'], pd_resample['W_CMB_LEN'], pd_resample['W_CMB_LAG'] = DIVERGENCE(pd_resample,
                                                                                                   'W_CMB', 'Close',
                                                                                                   max_length=24,
                                                                                                   min_length=2)
        ll.append(pd_resample[['time', 'W_CMB', 'W_CMB_Fast', 'W_CMB_Slow', 'W_CMB_Peak', 'W_CMB_Peak_T1', 'W_CMB_Step',
                               'W_CMB_LEN', 'W_CMB_LAG'] + list_col])
        # pd_resample[['time','Close','W_CMB','W_CMB_Fast','W_CMB_Slow','W_CMB_Peak','W_CMB_Step','W_CMB_LEN','W_CMB_LAG']].to_csv('../ticker/debug.csv',index=False)
    pd01 = pd.concat(ll, axis=0).sort_values(by='time').reset_index(drop=True)
    # monthly
    ll = []
    for t in range(0, 20, 1):
        pd_resample = resample_data(pdraw[:(T - t)], n=20, cname_time=cname_time)
        pdx = get_candle_stick(pd_resample, len=options.get("len", 5), code='M')
        list_col = [f for f in pdx.columns if f not in ['time']]
        pd_resample = pd_resample.merge(pdx, on='time', how='left')
        pd_resample['M_CMB'], pd_resample['M_CMB_Fast'], pd_resample['M_CMB_Slow'] = CMB(pd_resample)
        _, pd_resample['M_CMB_Peak_T1'], pd_resample['M_CMB_Peak_T2'] = PEAK(pd_resample, 'M_CMB')
        pd_resample['M_CMB_Step'], pd_resample['M_CMB_LEN'], pd_resample['M_CMB_LAG'] = DIVERGENCE(pd_resample,
                                                                                                   'M_CMB', 'Close',
                                                                                                   max_length=24,
                                                                                                   min_length=2)
        ll.append(pd_resample[['time', 'M_CMB', 'M_CMB_Peak_T1', 'M_CMB_Peak_T2', 'M_CMB_Step', 'M_CMB_LEN',
                               'M_CMB_LAG'] + list_col])
    pd02 = pd.concat(ll, axis=0).sort_values(by='time').reset_index(drop=True)

    pd03 = (pd00
            .merge(pd01, on='time', how='left')
            .merge(pd02, on='time', how='left')
            )

    for k in ['W_CMB', 'W_CMB_Fast', 'W_CMB_Slow', 'W_CMB_Peak', 'W_CMB_Peak_T1', 'W_CMB_Step', 'W_CMB_LEN',
              'W_CMB_LAG',
              'M_CMB', 'M_CMB_Peak_T1', 'M_CMB_Peak_T2', 'M_CMB_Step', 'M_CMB_LEN', 'M_CMB_LAG']:
        pd03[k] = pd03[k].ffill()

    return pd03


def compute_fa_indicator_v2(pd00, pd_fa):
    if (pd_fa is None) or (pd_fa.empty):
        return pd00[['time']].copy()

    pd_fa['quarter'] = pd_fa['yearReport'].astype(int).astype(str) + 'Q' + pd_fa['lengthReport'].astype(int).astype(
        str)

    pd00 = pd00[['Close', 'Price', 'time']].copy()
    pd00['quarter'] = pd00["time"].apply((lambda x: quarter_report(x, pd_fa)))

    all_quarter = pd00['quarter'].unique()
    pd_00_quater = pd.DataFrame(data=all_quarter, columns=['quarter'])

    release_report_date(pd00, pd_fa, pd_00_quater)
    quarter_8_v1(pd_fa, pd_00_quater)
    selected_quarter_v1(pd_fa, pd_00_quater)
    finance_indicator_v1(pd_fa, pd_00_quater)
    finance_indicator_cal_v1(pd_fa, pd_00_quater)
    price_indicator_v1(pd00, pd_fa, pd_00_quater)
    ROE_v1(pd_fa, pd_00_quater)
    devidends(pd_fa, pd_00_quater)
    quarter_4_v1(pd_fa, pd_00_quater)
    f_score_v1(pd_fa, pd_00_quater)
    trailing_v1(pd_fa, pd_00_quater)

    pd00 = pd00.merge(pd_00_quater, on='quarter', how='left')
    pd00['PB'] = pd00['Close'] * pd00['PB']
    pd00['PS'] = pd00['Close'] * pd00['PS']
    pd00['PCF'] = pd00['Close'] * pd00['PCF']
    pd00['PE'] = pd00['Close'] * pd00['PE']
    pd00['EVEB'] = pd00['Close'] * pd00['EVEB']
    # pd00['PE'] = pd00['Price'] * pd00['PE']
    pd00['EPS'] = 1 / pd00['PE']

    # pd00["GROW"] = (pd00["EPS"] / pd00["EPS"].shift(240) - 1) * 100
    pd00["GROW"] = (pd00["NP_P0"] / pd00["NP_P4"] - 1) * 100
    pd00["PEG"] = (pd00["PE"] / pd00["GROW"])

    # cols = ['PB', 'PS', 'PCF', 'EPS', 'PE', 'FSCORE']
    # pd00[cols] = pd00[cols].ffill()

    by = 5
    convert = {"1M": 4, "3M": 12, "1Y": 48, "3Y": 144, "5Y": 240}
    index = pd00.index[np.arange(len(pd00)) % by == 1]
    pd00_rolling = pd00.loc[index][['PE', 'PB', 'EVEB']].copy()

    pd00.loc[index, 'PE_MA5Y'] = pd00_rolling['PE'].rolling(convert['5Y'], min_periods=120).mean()
    pd00.loc[index, 'PE_MA1Y'] = pd00_rolling['PE'].rolling(convert['1Y']).mean()
    pd00.loc[index, 'PE_MA3M'] = pd00_rolling['PE'].rolling(convert['3M']).mean()
    pd00.loc[index, 'PE_SD5Y'] = pd00_rolling['PE'].rolling(convert['5Y'], min_periods=120).std()
    pd00.loc[index, 'PE_SD1Y'] = pd00_rolling['PE'].rolling(convert['1Y']).std()
    pd00.loc[index, 'PE_SD3M'] = pd00_rolling['PE'].rolling(convert['3M']).std()

    pd00.loc[index, 'PB_MA5Y'] = pd00_rolling['PB'].rolling(convert['5Y'], min_periods=120).mean()
    pd00.loc[index, 'PB_MA1Y'] = pd00_rolling['PB'].rolling(convert['1Y']).mean()
    pd00.loc[index, 'PB_MA3M'] = pd00_rolling['PB'].rolling(convert['3M']).mean()
    pd00.loc[index, 'PB_SD5Y'] = pd00_rolling['PB'].rolling(convert['5Y'], min_periods=120).std()
    pd00.loc[index, 'PB_SD1Y'] = pd00_rolling['PB'].rolling(convert['1Y']).std()
    pd00.loc[index, 'PB_SD3M'] = pd00_rolling['PB'].rolling(convert['3M']).std()

    pd00.loc[index, 'EVEB_MA5Y'] = pd00_rolling['EVEB'].rolling(convert['5Y'], min_periods=120).mean()
    pd00.loc[index, 'EVEB_MA1Y'] = pd00_rolling['EVEB'].rolling(convert['1Y']).mean()
    pd00.loc[index, 'EVEB_MA3M'] = pd00_rolling['EVEB'].rolling(convert['3M']).mean()
    pd00.loc[index, 'EVEB_SD5Y'] = pd00_rolling['EVEB'].rolling(convert['5Y'], min_periods=120).std()
    pd00.loc[index, 'EVEB_SD1Y'] = pd00_rolling['EVEB'].rolling(convert['1Y']).std()
    pd00.loc[index, 'EVEB_SD3M'] = pd00_rolling['EVEB'].rolling(convert['3M']).std()

    cols = ['PE_MA5Y', 'PE_MA1Y', 'PE_MA3M', 'PE_SD5Y', 'PE_SD1Y', 'PE_SD3M',
            'PB_MA5Y', 'PB_MA1Y', 'PB_MA3M', 'PB_SD5Y', 'PB_SD1Y', 'PB_SD3M',
            'EVEB_MA5Y', 'EVEB_MA1Y', 'EVEB_MA3M', 'EVEB_SD5Y', 'EVEB_SD1Y', 'EVEB_SD3M']
    pd00[cols] = pd00[cols].ffill()
    pd00 = pd00.drop(['Close', 'Price', 'GROW'], axis=1)
    return pd00


def compute_profit_v1(pd00_input, cname_time='time', cname_price='Open', delay=1):
    """
    Compute profit for a given ticker
    L1M : lowest price in the next 1 month (20 sessions) / 
    L3M : %Profit after 60 days or -7% cutloss
    """

    pd00 = pd00_input.sort_values(cname_time).reset_index(drop=True).copy()
    buyPrice = pd00[cname_price].shift(-delay)
    dictP = {'1W': 5,
             '2W': 10,
             '3W': 15,
             '1M': 20,
             '2M': 40,
             '3M': 60,
             '6M': 120,
             '1Y': 240,
             '2Y': 480
             }
    # L1W H1W C1W ...
    ll = [cname_time]
    for k in dictP.keys():
        n = dictP[k]
        pd00[f'L{k}'] = pd00['Low'].rolling(n).min().shift(-n - delay) / buyPrice
        pd00[f'H{k}'] = pd00['High'].rolling(n).max().shift(-n - delay) / buyPrice
        pd00[f'C{k}'] = pd00['Close'].shift(-n - delay) / buyPrice
        pd00[f'O{k}'] = pd00['Open'].shift(-n - delay) / buyPrice
        ll.extend([f'L{k}', f'H{k}', f'C{k}', f'O{k}'])
    return pd00[ll]


def compute_additional_indicators(ticker, pd00_input, df_risk, df_market):
    cols = ['time']
    pd00 = pd00_input.copy()
    pd00['quarter'] = pd.PeriodIndex(pd.to_datetime(pd00['time']), freq='Q').astype(str)

    # Risk
    if f'{ticker}_downside-beta_bin' not in df_risk.columns or f'{ticker}_downside-deviation_bin' not in df_risk.columns:
        return pd00[cols]

    beta = df_risk[f'{ticker}_downside-beta_bin']
    dev = df_risk[f'{ticker}_downside-deviation_bin']
    diff = (beta - dev).abs()
    df_risk['D_Risk_Rating'] = np.where(diff <= 3, np.ceil((beta + dev) / 2), np.fmax(beta, dev))
    df_risk['D_Risk_Rating'].fillna(3, inplace=True)

    beta = df_risk[f'{ticker}_beta_bin']
    dev = df_risk[f'{ticker}_deviation_bin']
    diff = (beta - dev).abs()
    df_risk['Risk_Rating'] = np.where(diff <= 3, np.ceil((beta + dev) / 2), np.fmax(beta, dev))
    df_risk['Risk_Rating'].fillna(3, inplace=True)

    df_risk['Risk_Rating'] = df_risk[['Risk_Rating','D_Risk_Rating']].max(axis=1)

    df_risk.rename(columns={f'{ticker}_downside-beta_bin': 'D_Beta',
                            f'{ticker}_downside-deviation_bin': 'D_Dev',
                            f'{ticker}_beta_bin': 'Beta',
                            f'{ticker}_deviation_bin': 'Dev'
                            }, inplace=True)

    pd00 = pd00.merge(df_risk, on='quarter', how='left')
    cols.extend(['D_Beta', 'D_Dev','Beta', 'Dev', 'Risk_Rating'])

    # PE
    industry_code = pd00['ICB_Code'].iat[0]
    df_ins = df_market[['time', f'PE_{industry_code}_mean', f'PE_{industry_code}_weight']].copy()
    df_ins.rename(columns={f'PE_{industry_code}_mean': 'PE_ICB' ,
                           f'PE_{industry_code}_weight': 'PE_ICB_Weight'
                           }, inplace=True)

    pd00 = pd00.merge(df_ins, on='time', how='left')

    target_cols = ['PE_ICB', 'PE_ICB_Weight']
    is_today = pd00['time'] == pd00['time'].max()

    # get vaild previous value
    prev_known = pd00[target_cols].ffill().shift(1)
    # fill today's missing value with previous value
    pd00.loc[is_today, target_cols] = (
        pd00.loc[is_today, target_cols]
        .combine_first(prev_known.loc[is_today])
    )

    cols.extend(['PE_ICB', 'PE_ICB_Weight'])

    return pd00[cols]


def get_candle_stick(pd00_input, cname_time='time', len=5, code='D'):
    pd00 = pd00_input[[cname_time, 'Open', 'High', 'Low', 'Close', 'Volume']].copy()
    pd00['Volume_Average'] = pd00['Volume'].rolling(len).mean()
    pd00['Close_Average'] = pd00['Close'].rolling(len).mean()
    list_col = []
    for d in range(0, len + 1):
        for v in ['Open', 'High', 'Low', 'Close']:
            if (d == 0) & (v == 'Close'):
                continue
            col = f'{v}_{code}{d:02d}'
            pd00[col] = pd00[v].shift(d) / pd00['Close_Average']
            list_col.append(col)
        for v in ['Volume']:
            col = f'{v}_{code}{d:02d}'
            pd00[col] = pd00[v].shift(d) / pd00['Volume_Average']
            list_col.append(col)
    return pd00[[cname_time] + list_col]


def resample_data(pd00_input, n=5, cname_time='time'):
    # data sample
    # ticker	open	high	low	close	volume	open interest	ymd
    # AAT	9.51	9.51	9.36	9.51	1568200	34800000	20210324
    pd00 = pd00_input.sort_values(cname_time).reset_index(drop=True).copy()
    L = pd00.shape[0]
    pd00['no'] = range(L)
    pd00['no'] = pd00['no'] - (L - 1) % n + (n - 1)
    pd00['no'] = pd00['no'] // n
    pd01 = pd00.groupby('no').agg({'Open': 'first',
                                   'High': 'max',
                                   'Low': 'min',
                                   'Close': 'last',
                                   'Volume': 'sum',
                                   #                                    'Open Interest':'sum',
                                   cname_time: 'last'}).reset_index(drop=True)
    return pd01


def CROSS(pd00_input, cname_x, cname_y):
    """
    Input - df : ordered dataframe with two columns cname_x and cname_y
    Output - X : series indicating how long cname_x has crossed cname_y. 0 : just crossed, 1: crossed in the last period, ... M
    """
    pd00 = pd00_input[[cname_x, cname_y]].reset_index(drop=True)
    pd00['_X_'] = np.nan
    for t in range(2, pd00_input.shape[0]):
        if ((pd00.loc[t, cname_x] > pd00.loc[t, cname_y]) & (pd00.loc[t - 2, cname_x] < pd00.loc[t - 2, cname_y])):
            pd00.loc[t, '_X_'] = 0
        elif ((pd00.loc[t, cname_x] < pd00.loc[t, cname_y]) & (pd00.loc[t - 2, cname_x] > pd00.loc[t - 2, cname_y])):
            pd00.loc[t, '_X_'] = 0
        else:
            pd00.loc[t, '_X_'] = pd00.loc[t - 1, '_X_'] + 1
    return pd00['_X_'].values


def CROSS_SIGNAL(pd00_input, cname_x, cname_y, strategy=None, offset: float = 0):
    """
       Calculate the cross signals between two columns in a DataFrame.
       Track history of the cross signals.
       Returns:
           Tuple[np.ndarray, np.ndarray]: The cross signals and their previous values.
       """
    pd00 = pd00_input[[cname_x, cname_y]].reset_index(drop=True).copy()
    pd00['X_CROSS_Y_P0'] = np.nan
    pd00['X_CROSS_Y_P1'] = np.nan
    pd00['X_CROSS_Y_P2'] = np.nan

    ratio_up = 1 + offset
    ratio_down = 1 - offset
    up = True
    down = True
    if strategy == 'up':
        down = False
    if strategy == 'down':
        up = False

    cross_p0 = np.nan
    cross_p1 = np.nan
    cross_p2 = np.nan

    for t in range(1, pd00_input.shape[0]):
        if (pd00.loc[t - 1, cname_x] > ratio_down * pd00.loc[t - 1, cname_y]) and (
                pd00.loc[t, cname_x] < ratio_down * pd00.loc[t, cname_y]) and down:
            cross_p2 = cross_p1
            cross_p1 = cross_p0
            cross_p0 = t
        elif (pd00.loc[t - 1, cname_x] < ratio_up * pd00.loc[t - 1, cname_y]) and (
                pd00.loc[t, cname_x] > ratio_up * pd00.loc[t, cname_y]) and up:
            cross_p2 = cross_p1
            cross_p1 = cross_p0
            cross_p0 = t

        pd00.loc[t, 'X_CROSS_Y_P0'] = cross_p0
        pd00.loc[t, 'X_CROSS_Y_P1'] = cross_p1
        pd00.loc[t, 'X_CROSS_Y_P2'] = cross_p2

    return pd00['X_CROSS_Y_P0'].values, pd00['X_CROSS_Y_P1'].values, pd00['X_CROSS_Y_P2'].values


def CROSS_VALUE(pd00_input, cname_x, cname_value_y, strategy=None, duration: int = 60, offset: float = 0):
    """
       Calculate the cross signals between two columns in a DataFrame.
       Track current values with previous time
       Returns:
           Tuple[np.ndarray, np.ndarray]: The cross signals and their previous values.
       """
    pd00 = pd00_input[[cname_x, cname_value_y]].reset_index(drop=True).copy()
    pd00['CROSS_Y_P0'] = np.nan
    pd00['CROSS_Y_P1'] = np.nan
    pd00['CROSS_Y_P2'] = np.nan

    ratio_up = 1 + offset
    ratio_down = 1 - offset
    up = True
    down = True
    if strategy == 'up':
        down = False
    if strategy == 'down':
        up = False

    cross_p0 = np.nan
    cross_p1 = np.nan
    cross_p2 = np.nan
    y_current_value = np.nan
    for t in range(2, pd00_input.shape[0]):
        if y_current_value != pd00.loc[t, cname_value_y]:
            y_current_value = pd00.loc[t, cname_value_y]
            cross_p0 = np.nan
            cross_p1 = np.nan
            cross_p2 = np.nan

            for i in range(min(duration, t - 1), -1, -1):
                if (pd00.loc[t - i - 1, cname_x] > ratio_down * y_current_value) and (
                        pd00.loc[t - i, cname_x] < ratio_down * y_current_value) and down:
                    cross_p2 = cross_p1
                    cross_p1 = cross_p0
                    cross_p0 = t - i
                elif (pd00.loc[t - i - 1, cname_x] < ratio_up * y_current_value) and (
                        pd00.loc[t - i, cname_x] > ratio_up * y_current_value) and up:
                    cross_p2 = cross_p1
                    cross_p1 = cross_p0
                    cross_p0 = t - i
        else:
            if (pd00.loc[t - 1, cname_x] > ratio_down * y_current_value) and (
                    pd00.loc[t, cname_x] < ratio_down * y_current_value) and down:
                cross_p2 = cross_p1
                cross_p1 = cross_p0
                cross_p0 = t
            elif (pd00.loc[t - 1, cname_x] < ratio_up * y_current_value) and (
                    pd00.loc[t, cname_x] > ratio_up * y_current_value) and up:
                cross_p2 = cross_p1
                cross_p1 = cross_p0
                cross_p0 = t

            if t - cross_p2 >= min(duration, t):
                cross_p2 = np.nan
            if t - cross_p1 >= min(duration, t):
                cross_p1 = np.nan
            if t - cross_p0 >= min(duration, t):
                cross_p0 = np.nan

        pd00.loc[t, 'CROSS_Y_P0'] = cross_p0
        pd00.loc[t, 'CROSS_Y_P1'] = cross_p1
        pd00.loc[t, 'CROSS_Y_P2'] = cross_p2

    return pd00['CROSS_Y_P0'].values, pd00['CROSS_Y_P1'].values, pd00['CROSS_Y_P2'].values


def TOP_N_NEAREST(pdx, cname_x, period, cname_retrieve, top_n=5):
    # Helper function to find the last higher index within a window
    def fast_find_last_higher_index(values, top_5, window):
        n = len(values)
        result = np.full(n, np.nan)

        for i in range(window - 1, n):
            window_data = values[i - window + 1:i + 1]
            top_value = top_5[i]

            idx = np.where(window_data > top_value)[0]
            if len(idx) > 0:
                result[i] = i - window + 1 + idx[-1]

        return result

    # Initialize the DataFrame with the required columns
    pdx = pdx[[cname_x] + cname_retrieve].copy()
    cname_retrieve.extend([cname_x])
    cname_cols = [f"{element}_top" for element in cname_retrieve]
    for col in cname_cols:
        pdx[col] = np.nan

    # Calculate the rank of the top N values within the rolling window
    pdx['rank'] = pdx[cname_x].rolling(window=period).agg(lambda x: np.sort(x)[-top_n])
    pdx['id'] = fast_find_last_higher_index(pdx[cname_x].values, pdx['rank'].values, period)

    # Retrieve the corresponding values for the top N indices
    for i in range(pdx.shape[0]):
        id = pdx.loc[i, 'id']
        if not pd.isna(id):
            for col, retrieve in zip(cname_cols, cname_retrieve):
                pdx.loc[i, col] = pdx.loc[id, retrieve]

    return [pdx[col] for col in ["id"] + cname_cols]


def PEAK_SIGNAL(pdx, cname_x, type, period, cname_retrieve):
    pdx = pdx[[cname_x] + cname_retrieve].copy()
    cname_cols = [element.lower() for element in cname_retrieve]
    cols = ["x"] + cname_cols
    pdx[cols] = np.nan

    if type == "max":
        pdx['id'] = pdx[cname_x].rolling(window=period).agg(lambda x: x.idxmax())
    elif type == "min":
        pdx['id'] = pdx[cname_x].rolling(window=period).agg(lambda x: x.idxmin())

    for i in range(pdx.shape[0]):
        id = pdx.loc[i, 'id']
        if not pd.isna(id):
            pdx.loc[i, "x"] = pdx.loc[id, cname_x]
            for col, cname in zip(cname_cols, cname_retrieve):
                pdx.loc[i, col] = pdx.loc[id, cname]

    return [pdx[col] for col in cols]


def PEAK(pd00_input, cname_key):
    """
    Previous top/bottom of a time series
    Usage : Peak,Peak_T1,Peak_T2 = PEAK(pd00,'Close')
    Return:
    Peak :  indicate if t-1 is top (1) none (0) bottom (-1)
    Peak_T1 : list of values of most recent peaks
    Peak_T2 : list of values of the peaks before
    """
    pd00 = pd00_input[[cname_key]].reset_index(drop=True)
    pd00['peak'] = (((pd00[cname_key] - pd00.shift(-1)[cname_key]) * (
            pd00[cname_key] - pd00.shift(1)[cname_key])) > 0).astype(int)
    pd00['peak'] = pd00['peak'] * (
            (pd00[cname_key] > pd00[cname_key].shift(1)) * 2 - 1)  # peak = -1:bottom 0:none 1:top
    pd00['peak_T1'] = np.nan
    pd00['peak_T2'] = np.nan
    for i in range(1, pd00.shape[0]):
        if pd00.loc[i - 1, 'peak'] != 0:
            pd00.loc[i, 'peak_T1'] = pd00.loc[i - 1, cname_key]
            pd00.loc[i, 'peak_T2'] = pd00.loc[i - 1, 'peak_T1']
        else:
            pd00.loc[i, 'peak_T1'] = pd00.loc[i - 1, 'peak_T1']
            pd00.loc[i, 'peak_T2'] = pd00.loc[i - 1, 'peak_T2']
    return pd00['peak'].shift(1).values, pd00['peak_T1'].values, pd00['peak_T2'].values


def EXTREME(pdx, cname_x, period, cname_retrieve, type="max"):
    cname_retrieve.extend([cname_x])
    pdx = pdx[cname_retrieve].copy()

    cname_cols = [f"{element}_retrieve" for element in cname_retrieve]
    pdx[cname_cols] = np.nan

    if type == "max":
        pdx['id'] = pdx[cname_x].rolling(window=period, min_periods=int(period / 2)).agg(lambda x: x.idxmax())
    elif type == "min":
        pdx['id'] = pdx[cname_x].rolling(window=period, min_periods=int(period / 2)).agg(lambda x: x.idxmin())

    for i in range(pdx.shape[0]):
        id = pdx.loc[i, 'id']
        if not pd.isna(id):
            for col, retrieve in zip(cname_cols, cname_retrieve):
                pdx.loc[i, col] = pdx.loc[id, retrieve]

    return [pdx[col] for col in ["id"] + cname_cols]


def VOL_EXTREME(pdx, type="max", period=240 * 5):
    pdx = pdx[['High', 'Low', 'Close', 'Volume']].copy()
    cols = ["hight_value", "low_value", "close_value", "volume_value"]
    pdx[cols] = np.nan

    if type == "max":
        pdx['id'] = pdx['Volume'].rolling(window=period, min_periods=2 * 240).agg(lambda x: x.idxmax())
    elif type == "min":
        pdx['id'] = pdx['Volume'].rolling(window=period, min_periods=2 * 240).agg(lambda x: x.idxmin())

    for i in range(pdx.shape[0]):
        id = pdx.loc[i, 'id']
        if not pd.isna(id):
            pdx.loc[i, "hight_value"] = pdx.loc[id, "High"]
            pdx.loc[i, "low_value"] = pdx.loc[id, "Low"]
            pdx.loc[i, "close_value"] = pdx.loc[id, "Close"]
            pdx.loc[i, "volume_value"] = pdx.loc[id, "Volume"]

    return pdx["id"], pdx["hight_value"], pdx["low_value"], pdx["close_value"], pdx["volume_value"]


def VOL1M3M(pdx):
    """
    pdx['vol1M'],pdx['vol1M_Low'],pdx['vol3M'],pdx['vol3M_Low'] = VOL1M3M(pdx)
    """
    period = 20
    data = {'index': [],
            'Volume': pdx['Volume'].to_list() * period,
            'Low': pdx['Low'].to_list() * period,
            }
    for i in range(1, 21):
        data['index'].extend(pdx.index + i)

    pdx1 = pd.DataFrame(data).sort_values(['index', 'Volume'], ascending=[True, False]) \
        .drop_duplicates(subset=['index']).query(f'index<={pdx.index.max()}').set_index('index')

    # loop through pdx1
    data3 = {'index': pdx1.index.to_list() + (pdx1.index + 20).to_list() + (pdx1.index + 40).to_list(),
             'Volume': pdx1['Volume'].to_list() * 3,
             'Low': pdx1['Low'].to_list() * 3,
             }
    pdx3 = pd.DataFrame(data3).sort_values(['index', 'Volume'], ascending=[True, False]) \
        .drop_duplicates(subset=['index']).query(f'index<={pdx1.index.max()}').set_index('index')

    del data, data3
    gc.collect()

    return pdx1['Volume'], pdx1['Low'], pdx3['Volume'], pdx3['Low']


# ===========================
def VAP(pdxx, cname_date='time', period=20):
    """ Return support based on volume during period
    Support is defined as the price lower than current price where the volume is the highest during the period
    Usage: S = VAP(pdxx,cname_price='Close',cname_volume='Volume',period=20)
    Assume that price are integer!
    """
    ll = np.array(sorted(pdxx['Close'].astype('int').unique()))
    price_step = int(min((ll[1:] - ll[:-1])))
    T = pdxx.shape[0]
    data = {'Price': [], 'Volume': [], cname_date: []}
    for t in range(T):
        lp = list(range(int(pdxx.iloc[t]['Low']), int(pdxx.iloc[t]['High']) + price_step, price_step))
        ld = pdxx.iloc[t:(t + period)][cname_date].to_list()
        data['Price'].extend(lp * len(ld))
        data['Volume'].extend([pdxx.iloc[t]['Volume'].astype(float) / len(lp)] * len(lp) * len(ld))
        for d in ld:
            data[cname_date].extend([d] * len(lp))
    pdy = pd.DataFrame(data).groupby(['Price', cname_date], as_index=False).agg({'Volume': 'sum'})
    del data
    gc.collect()

    pdy = pdy.merge(pdxx[[cname_date, 'Close']], on=cname_date, how='left')
    pdz = pdy[pdy['Price'] <= pdy['Close']].sort_values([cname_date, 'Volume'],
                                                        ascending=[True, False]).drop_duplicates(subset=[cname_date],
                                                                                                 keep='first')
    pd_tt = pdxx[[cname_date]].copy()
    pdz = pd_tt.merge(pdz, on=cname_date, how='left')
    S = pdz['Price'].shift(-period + 1).shift(period).to_list()
    return S, pdz


def VAP_v1(pdxx, cname_date='time', period=20):
    pdxx = pdxx[['time', 'Low', 'High', 'Close', 'Volume']].copy()
    pdxx['d_time'] = pd.to_datetime(pdxx['time'])
    close_vals = pdxx['Close'].dropna().astype('int')
    ll = np.array(sorted(np.ceil(close_vals).astype(int).unique()))

    # Fix price_step
    if len(ll) < 2:
        price_step = 1
    else:
        price_step = int(min(ll[1:] - ll[:-1]))
        if price_step == 0:
            price_step = 1

    T = pdxx.shape[0]
    data = {'Price': [], 'Volume': [], cname_date: []}
    pdxx["end"] = 0
    for t in range(T):
        low_value = pdxx.iloc[t]['Low']
        high_value = pdxx.iloc[t]['High']
        if np.isnan(low_value) or np.isnan(high_value):
            lp = []
        else:
            lp = list(range(int(low_value), int(high_value) + price_step, price_step))
            # lp = list(range(int(pdxx.iloc[t]['Low']), int(pdxx.iloc[t]['High']) + price_step, price_step))
        if len(lp) == 0:
            data['Price'].extend([np.nan])
            data['Volume'].extend([np.nan])
            date = pdxx.iloc[t][cname_date]
            data[cname_date].extend([date])
            pdxx.at[t, 'end'] = len(data[cname_date])
            continue
        data['Price'].extend(lp)
        data['Volume'].extend([pdxx.iloc[t]['Volume'].astype(float) / len(lp)] * len(lp))
        date = pdxx.iloc[t][cname_date]
        data[cname_date].extend([date] * len(lp))
        pdxx.at[t, 'end'] = len(data[cname_date])
    pdy = pd.DataFrame(data)

    pdxx['VAP'] = np.nan
    for id in range(T):
        end_idx = pdxx.iloc[id]['end']
        start_idx = pdxx.iloc[id - period + 1]['end'] if (id - period + 1) > 0 else 0
        df = pdy.iloc[start_idx:end_idx].copy()
        # df = df[df['Price'] <= pdxx.iloc[id]['Close']]
        df = df.groupby('Price', as_index=False).agg({'Volume': 'sum'})
        if df.empty:
            pdxx.at[id, 'VAP'] = np.nan
            continue
        max_vol_id = df['Volume'].idxmax()
        pdxx.at[id, 'VAP'] = df.loc[max_vol_id, 'Price']

    return pdxx['VAP'].shift(-period + 1).shift(period).to_list()


def RES_SUP_v1(pdxx, cname_date='time', period=240):
    pdxx = pdxx[['time', 'Low', 'High', 'Close', 'Volume', ]].copy()
    pdxx['d_time'] = pd.to_datetime(pdxx['time'])
    T = pdxx.shape[0]
    data = {'Price': [], 'Volume': [], cname_date: []}
    pdxx["end"] = 0
    for t in range(T):
        low_value = pdxx.iloc[t]['Low']
        high_value = pdxx.iloc[t]['High']
        if np.isnan(low_value) or np.isnan(high_value):
            lp = []
        else:
            # find_step
            if high_value <= 10e3:
                price_step = 10
            elif high_value <= 50e3:
                price_step = 50
            else:
                price_step = 100

            lp = list(range(int(low_value), int(high_value) + price_step, price_step))
            # lp = list(range(int(pdxx.iloc[t]['Low']), int(pdxx.iloc[t]['High']) + price_step, price_step))
        if len(lp) == 0:
            data['Price'].extend([np.nan])
            data['Volume'].extend([np.nan])
            date = pdxx.iloc[t][cname_date]
            data[cname_date].extend([date])
            pdxx.at[t, 'end'] = len(data[cname_date])
            continue
        data['Price'].extend(lp)
        data['Volume'].extend([pdxx.iloc[t]['Volume'].astype(float) / len(lp)] * len(lp))
        date = pdxx.iloc[t][cname_date]
        data[cname_date].extend([date] * len(lp))
        pdxx.at[t, 'end'] = len(data[cname_date])
    pdy = pd.DataFrame(data)
    pdy['Volume'] = pdy['Volume'].fillna(0)
    pdy['Volume'] = pdy['Volume'].astype(int)

    pdxx[['res', 'sup', 'vol_res', 'vol_sup']] = None
    before_end_idx = 0
    before_start_idx = 0

    df_range = defaultdict(int)

    for id in range(T):
        if (id - period + 1) < 0:
            continue

        end_idx = pdxx.iloc[id]['end']
        start_idx = pdxx.iloc[id - period + 1]['end']

        if not df_range:
            # First initialization
            for price, volume in zip(pdy.iloc[start_idx:end_idx]['Price'], pdy.iloc[start_idx:end_idx]['Volume']):
                df_range[price] += volume

        else:
            # Update range
            out_of_range = pdy.iloc[before_start_idx:start_idx]
            new_of_range = pdy.iloc[before_end_idx:end_idx]

            # Add new volumes
            for price, volume in zip(new_of_range['Price'], new_of_range['Volume']):
                df_range[price] += volume

            # Remove out-of-range volumes
            for price, volume in zip(out_of_range['Price'], out_of_range['Volume']):
                df_range[price] -= volume
                if df_range[price] <= 300:
                    del df_range[price]

        before_end_idx = end_idx
        before_start_idx = start_idx

        if not df_range:
            continue

        # print(f'shape {id} RES_SUP_v1: ', len(df_range))
        df_range_filtered = pd.DataFrame(list(df_range.items()), columns=['Price', 'Volume'])
        df_range_filtered = df_range_filtered.sort_values('Volume', ascending=False)

        df = df_range_filtered.head(int(0.2 * df_range_filtered.shape[0])).copy()
        if df.empty:
            df = df_range_filtered.copy()

        df = df.sort_values('Price', ascending=True).reset_index(drop=True)
        # print(f'shape {id} RES_SUP_v1: ', len(df))

        # smoothing
        diff = df['Price'].max() * 0.01
        price_standard = df.iloc[0]['Price']

        df['group'] = 0
        i_group = 0
        for id_dfgroup in range(df.shape[0]):
            if df.iloc[id_dfgroup]['Price'] - price_standard > diff:
                price_standard = df.iloc[id_dfgroup]['Price']
                i_group += 1
            df.at[id_dfgroup, 'group'] = i_group

        df_smoothing = df.loc[df.groupby('group')['Volume'].idxmax(), ['group', 'Price', 'Volume']].reset_index(
            drop=True)

        idx_max = df_smoothing['Price'].idxmax()
        idx_min = df_smoothing['Price'].idxmin()

        if not pd.isna(idx_max) and idx_max in df_smoothing.index:
            pdxx.at[id, 'res'] = df_smoothing.at[idx_max, 'Price']
            pdxx.at[id, 'vol_res'] = df_smoothing.at[idx_max, 'Volume']
        if not pd.isna(idx_min) and idx_min in df_smoothing.index:
            pdxx.at[id, 'sup'] = df_smoothing.at[idx_min, 'Price']
            pdxx.at[id, 'vol_sup'] = df_smoothing.at[idx_min, 'Volume']

    pdxx['r_v'] = pdxx['res'] * pdxx['vol_res']
    pdxx['s_v'] = pdxx['sup'] * pdxx['vol_sup']

    pdxx['sum_rv_5'] = pdxx['r_v'].rolling(5).sum()
    pdxx['sum_sv_5'] = pdxx['s_v'].rolling(5).sum()
    pdxx['sum_vol_rv_5'] = pdxx['vol_res'].rolling(5).sum()
    pdxx['sum_vol_sv_5'] = pdxx['vol_sup'].rolling(5).sum()

    pdxx['res_mean_weight_5'] = pdxx['sum_rv_5'] / pdxx['sum_vol_rv_5'].replace(0, np.nan)
    pdxx['sup_mean_weight_5'] = pdxx['sum_sv_5'] / pdxx['sum_vol_sv_5'].replace(0, np.nan)

    return pdxx['res_mean_weight_5'], pdxx['sup_mean_weight_5']


def RES_SUP(pdxx, cname_date='time', period=240):
    pdxx = pdxx[['time', 'Low', 'High', 'Close', 'Volume', ]].copy()
    pdxx['d_time'] = pd.to_datetime(pdxx['time'])
    T = pdxx.shape[0]
    data = {'Price': [], 'Volume': [], cname_date: []}
    pdxx["end"] = 0
    for t in range(T):
        low_value = pdxx.iloc[t]['Low']
        high_value = pdxx.iloc[t]['High']
        if np.isnan(low_value) or np.isnan(high_value):
            lp = []
        else:
            # find_step
            if high_value <= 10e3:
                price_step = 10
            elif high_value <= 50e3:
                price_step = 50
            else:
                price_step = 100

            lp = list(range(int(low_value), int(high_value) + price_step, price_step))
            # lp = list(range(int(pdxx.iloc[t]['Low']), int(pdxx.iloc[t]['High']) + price_step, price_step))
        if len(lp) == 0:
            data['Price'].extend([np.nan])
            data['Volume'].extend([np.nan])
            date = pdxx.iloc[t][cname_date]
            data[cname_date].extend([date])
            pdxx.at[t, 'end'] = len(data[cname_date])
            continue
        data['Price'].extend(lp)
        data['Volume'].extend([pdxx.iloc[t]['Volume'].astype(float) / len(lp)] * len(lp))
        date = pdxx.iloc[t][cname_date]
        data[cname_date].extend([date] * len(lp))
        pdxx.at[t, 'end'] = len(data[cname_date])
    pdy = pd.DataFrame(data)

    pdxx[['res', 'sup', 'vol_res', 'vol_sup']] = np.nan
    before_end_idx = 0
    before_start_idx = 0
    df_range = None

    for id in range(T):
        if (id - period + 1) < 0:
            continue
        end_idx = pdxx.iloc[id]['end']
        start_idx = pdxx.iloc[id - period + 1]['end']

        if df_range is None:
            # The first time
            df_range = pdy.iloc[start_idx:end_idx].copy()
            df_range = df_range.groupby('Price', as_index=False).agg({'Volume': 'sum'}).sort_values('Volume',
                                                                                                    ascending=False)
        else:
            out_of_range = pdy.iloc[before_start_idx:start_idx]
            new_of_range = pdy.iloc[before_end_idx:end_idx]

            out_volume = out_of_range.iloc[0]['Volume']
            new_volume = new_of_range.iloc[0]['Volume']

            df_range.loc[df_range['Price'].isin(out_of_range['Price']), 'Volume'] -= out_volume
            df_range.loc[df_range['Price'].isin(new_of_range['Price']), 'Volume'] += new_volume

            new_prices = new_of_range[~new_of_range['Price'].isin(df_range['Price'])]
            if not new_prices.empty:
                df_range = pd.concat([df_range, new_prices[['Price', 'Volume']]], ignore_index=True)

            df_range = df_range[df_range['Volume'] > 300]
            df_range = df_range.sort_values('Volume', ascending=False)

        before_end_idx = end_idx
        before_start_idx = start_idx

        if df_range.empty:
            continue

        print(f'shape {id} RES_SUPv2: ', df_range.shape[0])

        df = df_range.copy()
        df = df.head(int(0.2 * df.shape[0]))
        if df.empty:
            df = df_range.copy()

        df = df.sort_values('Price', ascending=True).reset_index(drop=True)
        # smoothing
        smoothing = []
        diff = df['Price'].max() * 0.01
        df_smoothing = []
        price_standard = df.iloc[0]['Price']
        for id_dfgroup in range(df.shape[0]):
            if df.iloc[id_dfgroup]['Price'] - price_standard < diff:
                smoothing.append(df.iloc[id_dfgroup])
            else:
                smoothing_df = pd.concat(smoothing, axis=1).T
                smoothing_df['cap'] = smoothing_df['Price'] * smoothing_df['Volume']

                median_cap = smoothing_df['cap'].median()
                closest_idx = (smoothing_df['cap'] - median_cap).abs().idxmin()
                price_at_median_cap = smoothing_df.loc[closest_idx, 'Price']

                df_group = pd.DataFrame(smoothing_df.agg({'Price': 'max', 'Volume': 'sum'})).T
                df_group['Price'] = price_at_median_cap
                df_smoothing.append(df_group)

                price_standard = df.iloc[id_dfgroup]['Price']
                smoothing = [df.iloc[id_dfgroup]]
        if len(df_smoothing) > 0:
            df_smoothing = pd.concat(df_smoothing, ignore_index=True).sort_values('Volume', ascending=False)
        else:
            df_smoothing = df

        pdxx.at[id, 'res'] = max(df_smoothing['Price'])
        pdxx.at[id, 'vol_res'] = df_smoothing[df_smoothing['Price'] == max(df_smoothing['Price'])]['Volume'].iloc[0]

        pdxx.at[id, 'sup'] = min(df_smoothing['Price'])
        pdxx.at[id, 'vol_sup'] = df_smoothing[df_smoothing['Price'] == max(df_smoothing['Price'])]['Volume'].iloc[0]

    pdxx['r_v'] = pdxx['res'] * pdxx['vol_res']
    pdxx['s_v'] = pdxx['sup'] * pdxx['vol_sup']

    pdxx['sum_rv_5'] = pdxx['r_v'].rolling(5).sum()
    pdxx['sum_sv_5'] = pdxx['s_v'].rolling(5).sum()
    pdxx['sum_vol_rv_5'] = pdxx['vol_res'].rolling(5).sum()
    pdxx['sum_vol_sv_5'] = pdxx['vol_sup'].rolling(5).sum()

    pdxx['res_mean_weight_5'] = pdxx['sum_rv_5'] / pdxx['sum_vol_rv_5'].replace(0, np.nan)
    pdxx['sup_mean_weight_5'] = pdxx['sum_sv_5'] / pdxx['sum_vol_sv_5'].replace(0, np.nan)

    return pdxx['res_mean_weight_5'], pdxx['sup_mean_weight_5']


# ===========================
def SUPPORT_TRENDLINE(pd00, cname_key, MIN_LENGTH=20, MAX_LENGTH=200, ACCURACY=2, TOLERANCE=1e-4):
    """ SUPPORT_TRENDLINE(pd00,cname_key,MIN_LENGTH=10,MAX_LENGTH=200)
    Compute support a given pd00
    Usage: S,support_lines = SUPPORT_TRENDLINE(pd00,cname_key,MIN_LENGTH=10,MAX_LENGTH=200)
    S(t) : support at time t given pd00[0]..pd00[(t-1)]
    1. Find all local minima from 1..T-2.
    2. support_lines = [] # (slope,intercept,start,end)
    3. For each time point t = 0..T-1
        3.0 S(t) = max(support_lines at t) , set to 0 if no support line
        3.1 If t is a local minima then find new support lines ending at t and add to support_lines
        3.2 Remove violated support_lines
    """
    # 1. Find all local minima from 1..T-2.
    T = pd00.shape[0]
    minIdx = trendln.get_extrema((pd00[cname_key], None), extmethod=trendln.METHOD_NUMDIFF, accuracy=ACCURACY)
    for i in range(len(minIdx)):
        #        print(f'1 i={i} minIdx[i]={minIdx[i]}')
        if minIdx[i] < T - 1:
            if pd00.iloc[minIdx[i] + 1][cname_key] < pd00.iloc[minIdx[i]][cname_key] * (1.0 + TOLERANCE):
                minIdx[i] = minIdx[i] + 1
    if len(minIdx) < 2:
        return [0] * pd00.shape[0], []
    # 2. Initialize support_lines
    support_lines = []
    im, jm = 0, -1  # start and end of local minima upto t
    S = []
    # 3. Main loop
    for t in range(T):
        try:
            # 3.0 S(t) = max(support_lines at t) , set to 0 if no support line
            if len(support_lines) == 0:
                S.append(0)
            else:
                S.append(max([support_lines[i][0] * t + support_lines[i][1] for i in range(len(support_lines))]))
            # Update im and jm (the local minima cover t)
            if im < len(minIdx):
                if t - minIdx[im] + 1 > MAX_LENGTH:
                    im += 1
            if jm < len(minIdx) - 1:
                if minIdx[jm + 1] == t:
                    jm += 1
            # 3.1 If t is a local minima then find new support lines ending at t and add to support_lines
            if minIdx[jm] == t:
                # find new support lines from im.jm
                for i in range(im, jm):
                    if ((minIdx[jm] - minIdx[i] + 1) < MIN_LENGTH) | ((minIdx[jm] - minIdx[i] + 1) > MAX_LENGTH):
                        continue
                    x1, y1 = minIdx[i], pd00[cname_key][minIdx[i]]
                    x2, y2 = minIdx[jm], pd00[cname_key][minIdx[jm]]
                    slope = (y2 - y1) / (x2 - x1)
                    intercept = y1 - slope * x1
                    if slope < 0:
                        continue
                    # check violation
                    v = False
                    for k in range(minIdx[i] + 1, minIdx[jm] - 1):
                        if pd00[cname_key][k] < (k * slope + intercept) * (1.0 - TOLERANCE):
                            v = True
                            break
                    if not v:
                        support_lines.append((slope, intercept, x1, x2))
            # 3.2 Remove violated support_lines
            support_lines = [s for s in support_lines if pd00[cname_key][t] >= (t * s[0] + s[1]) * (1.0 - TOLERANCE)]
        except:
            print(f'Error at t={t}')
    return S, support_lines


def SUPPORT_TRENDLINE_v1(pd00, cname_key, MIN_LENGTH=5, MAX_LENGTH=120, TOLERANCE=1e-4,
                         ratio_threshold=(0.97, 0.1, 0.03)):
    # def find_longest_support(pd00, cname_key, MIN_LENGTH = 20 ,MAX_LENGTH=240, TOLERANCE=0.01, ratio_threshold=(0.955, 0.1, 0.08)):
    """
    Find the longest support line of array data at each local minimum.
    Use Thales's theorem to determine that all data in A[i:j] is smaller than the straight line connecting 2 points
    if a line is drawn parallel to one side of a triangle intersecting the other two sides in distinct points, then
    the other two sides are divided in the same ratio
    #
    ratio_threshold=(0.955, 0.1, 0.08), MIN_LENGTH=20, MAX_LENGTH=240,TOLERANCE=0.01 -> long term
    ratio_threshold=(0.97, 0.1, 0.03), MIN_LENGTH=5, MAX_LENGTH=120,TOLERANCE=1e-4 -> short term
    """
    pd00 = pd00[[cname_key, 'Close', 'High']]
    # 1. Find all local minima from 1..T-2.
    T = pd00.shape[0]
    minima_idxs = trendln.get_extrema((pd00[cname_key], None), extmethod=trendln.METHOD_NUMDIFF, accuracy=10)

    for i in range(len(minima_idxs)):
        if minima_idxs[i] < T - 1:
            if pd00.iloc[minima_idxs[i] + 1][cname_key] < pd00.iloc[minima_idxs[i]][cname_key] * (1 + TOLERANCE):
                minima_idxs[i] = minima_idxs[i] + 1

    pd00['C_H1W'] = pd00['Close'] / (pd00['High'].rolling(5).max())

    # Condition 1: Decreasing price x.y%
    minima_idxs = [idx for idx in minima_idxs if pd00.iloc[idx]['C_H1W'] < ratio_threshold[0]]

    # plt.plot(minima_idxs, pdx.iloc[minima_idxs]['Low'], marker='o', color='darkseagreen', linestyle='None',
    #          label='Minima original')
    #  Condition 2: Distance from 2 bottom is more than 1 month
    for i in range(1, 3):
        i = 1
        minima = minima_idxs.copy()
        for idx in range(len(minima_idxs)):
            if pd00.iloc[minima_idxs[idx - i]][cname_key] == 0:
                continue
            # Up hill
            if (0 < minima_idxs[idx] - minima_idxs[idx - i] < MIN_LENGTH):
                try:
                    ratio = pd00.iloc[minima_idxs[idx]][cname_key] / pd00.iloc[minima_idxs[idx - i]][cname_key]
                    # if ratio < 1 and abs(ratio - 1) < ratio_threshold[1]:
                    #     minima.remove(minima_idxs[idx - i]) if minima_idxs[idx - i] in minima else None
                    if ratio > 1 and abs(ratio - 1) < ratio_threshold[2]:
                        minima.remove(minima_idxs[idx]) if minima_idxs[idx] in minima else None
                except:
                    continue

            # Down hill
            if (0 < minima_idxs[idx] - minima_idxs[idx - i] < 2 * MIN_LENGTH):
                try:
                    ratio = pd00.iloc[minima_idxs[idx]][cname_key] / pd00.iloc[minima_idxs[idx - i]][cname_key]
                    if ratio < 1 and abs(ratio - 1) < ratio_threshold[1]:
                        minima.remove(minima_idxs[idx - i]) if minima_idxs[idx - i] in minima else None
                    # elif ratio > 1 and abs(ratio - 1) < ratio_threshold[2]:
                    #     minima.remove(minima_idxs[idx]) if minima_idxs[idx] in minima else None
                except:
                    continue

        minima_idxs = minima.copy()

    if len(minima_idxs) < 2:
        return [0] * T, []

    # plt.plot(minima_idxs, pdx.iloc[minima_idxs]['Low'], marker='.', color='red', linestyle='None',
    #          label='Minima filtered')

    minima_values = list(pd00.iloc[minima_idxs][cname_key].values)

    # 2. Find support line
    all_support_lines = []
    min_minima_idx = np.argmin(minima_values)
    min_minima_loc = minima_idxs[min_minima_idx]

    # Concatenate local minimum indexes and value for loop
    minima = list(zip(minima_idxs, minima_values))
    for i, (cur_loc, cur_value) in enumerate(reversed(minima)):
        sl_temp = []
        minima_idx = len(minima) - i - 1

        if cur_loc < min_minima_loc:
            min_minima_idx = np.argmin(minima_values[:minima_idx + 1])
            min_minima_loc = minima_idxs[min_minima_idx]

        # Heuristics: Use the smallest value of the local minimum set as a starting point
        for check_minima_idx in range(min_minima_idx, minima_idx):
            check_loc = minima_idxs[check_minima_idx]
            check_value = minima_values[check_minima_idx]
            if (check_value >= cur_value) or (cur_loc - check_loc) < 0 or (cur_loc - check_loc) > MAX_LENGTH:
                continue

            # Use transform and (check_date, cur_value) point as the length coordinate reference
            loc_distance = cur_loc - check_loc
            value_distance = cur_value - check_value

            mid_locs = np.arange(check_loc, cur_loc)
            mid_values = pd00.iloc[check_loc:cur_loc][cname_key].values

            mask = mid_values < cur_value
            calib_mid_locs = abs((mid_locs[mask] - check_loc) - loc_distance)
            calib_mid_values = abs(mid_values[mask] - cur_value) * (1 - TOLERANCE)

            # Build ratios based on Thales's theorem
            x_ratio = calib_mid_locs / loc_distance
            y_ratio = calib_mid_values / value_distance

            ratio = y_ratio > x_ratio
            count = np.count_nonzero(ratio)

            if (count / loc_distance) < TOLERANCE:
                sl_temp.append([check_loc, cur_loc])

        # 3. Find the support line with the largest slope
        max_slope = 0
        for loc in sl_temp:
            x1, y1 = loc[0], pd00.iloc[loc[0]][cname_key]
            x2, y2 = loc[1], pd00.iloc[loc[1]][cname_key]
            slope = (y2 - y1) / (x2 - x1)
            if slope < 0:
                continue
            if slope > max_slope:
                max_slope = slope
                sp_loc = loc

        if max_slope != 0:
            intercept = pd00.iloc[sp_loc[0]][cname_key] - max_slope * sp_loc[0]
            all_support_lines.append((max_slope, intercept, sp_loc[0], sp_loc[1]))

    # 4. Loop find support line
    all_support_lines.sort(key=lambda x: x[3], reverse=False)

    S = []
    sp_lines = []
    id_sp = 0

    for t in range(T):
        if len(sp_lines) == 0:
            S.append(0)
        else:
            # S.append(np.percentile([sp_lines[i][0] * t + sp_lines[i][1] for i in range(len(sp_lines))], 90, method="nearest"))
            S.append(max([sp_lines[i][0] * t + sp_lines[i][1] for i in range(len(sp_lines))]))

        if id_sp < len(all_support_lines) and t >= all_support_lines[id_sp][3]:
            sp_lines.append(all_support_lines[id_sp])
            id_sp += 1
        sp_lines = [s for s in sp_lines if pd00[cname_key][t] >= (t * s[0] + s[1]) * (1.0 - TOLERANCE * 2)]

    # print(len(all_support_lines))
    return S, all_support_lines


def DIVERGENCE(pd00_input, key, ref, max_length=24, min_length=4):
    """
    Divergence between two time series
    1. Identify bottom/top
    2. Check increasing bottom or decreasing top
    3. double check divergence
    4. propagate trend
    return
    # SKIP Peak: -1 (bottom) 0 (none) 1 (increasing)
    Step: value increment (=0 if no divergence)
    LEN: length of the movement
    LAG: age of the movemement (0 if divergence occurs at t and is discovered at t+1)
    """

    def check_increase(pd00, key, ref, k, h):
        # check increasing divergence : key increases from k to h , ref decreases from k to h
        # check if key increases from k to h
        if pd00.at[k, key] >= pd00.at[h, key]:
            return False
        if pd00.at[k, 'peak'] != -1:
            return False
        if pd00.at[k, ref] <= pd00.at[h, ref]:
            return False
        step = (pd00.at[h, key] - pd00.at[k, key]) / (h - k)
        for i in list(range(k + 1, h)) + [h + 1]:
            if pd00.at[i, key] < pd00.at[k, key] + step * (i - k):
                return False
        return True

    def check_decrease(pd00, key, ref, k, h):
        # check decreasing divergence : key decreases from k to h , ref increases from k to h
        # usage: check_decrease(pd00,'CMB','Price',k,h)
        if pd00.at[k, key] <= pd00.at[h, key]:
            return False
        if pd00.at[k, 'peak'] != 1:
            return False
        if pd00.at[k, ref] >= pd00.at[h, ref]:
            return False
        step = (pd00.at[h, key] - pd00.at[k, key]) / (h - k)
        for i in list(range(k + 1, h)) + [h + 1]:
            if pd00.at[i, key] > pd00.at[k, key] + step * (i - k):
                return False
        return True

    pd00 = pd00_input[['time', key, ref]].reset_index(drop=True)
    # compute peak, length, step of the trends
    pd00['peak'] = ((pd00[key] - pd00[key].shift(1)) * (pd00[key] - pd00[key].shift(-1)) > 0).astype(int)
    pd00['peak'] = pd00['peak'] * ((pd00[key] > pd00[key].shift(1)) * 2 - 1)  # peak = -1:bottom 0:none 1:top
    pd00['length'] = 0
    pd00['step'] = .0
    for i in range(min_length, pd00.shape[0]):
        # top
        if (pd00.at[i, 'peak'] == 1):
            for j in range(min_length, min(max_length, i)):
                if check_decrease(pd00, key, ref, i - j, i):
                    pd00.loc[i, 'length'] = j
                    pd00.loc[i, 'step'] = (pd00.at[i, key] - pd00.at[i - j, key]) / j
                    break
        # bottom
        if (pd00.at[i, 'peak'] == -1):
            for j in range(min_length, min(max_length, i)):
                if check_increase(pd00, key, ref, i - j, i):
                    pd00.loc[i, 'length'] = j
                    pd00.loc[i, 'step'] = (pd00.at[i, key] - pd00.at[i - j, key]) / j
                    break

    # enable lag
    # peak!=0, lag=0, step=x, length=y -> right at the trend
    # peak!=0, lag>0, step=x, length=y -> continue trend for lag periods
    #
    pd00['lag'] = 0
    #    pd00['prjval']=np.nan
    for i in range(min_length, pd00.shape[0]):
        if ((pd00.at[i, 'step'] == 0) | (pd00.at[i, 'length'] < min_length)) & (pd00.at[i - 1, 'step'] != 0):
            lag = pd00.loc[i - 1, 'lag'] + 1
            prjval = pd00.loc[i - lag, key] + pd00.at[i - 1, 'step'] * lag
            #            pd00.loc[i,'lag']   =lag
            #            pd00.loc[i,'prjval']=prjval
            if (pd00.at[i - 1, 'peak'] * (pd00.at[i, key] - prjval)) < 0:
                pd00.loc[i, 'peak'] = pd00.loc[i - 1, 'peak']
                pd00.loc[i, 'length'] = pd00.loc[i - 1, 'length']
                pd00.loc[i, 'step'] = pd00.loc[i - 1, 'step']
                pd00.loc[i, 'lag'] = pd00.loc[i - 1, 'lag'] + 1
    #    pd00.to_csv(f'../ticker/debug_{pd00.shape[0]}.csv',index=False)
    for i in range(min_length, pd00.shape[0]):
        if pd00.at[i, 'lag'] == 0 and pd00.at[i, 'length'] != 0:
            pd00.loc[i, 'step'] = pd00.loc[i - 1, 'step']
            pd00.loc[i, 'length'] = pd00.loc[i - 1, 'length']
            pd00.loc[i, 'lag'] = pd00.loc[i - 1, 'lag'] + 1

    return pd00['step'].values, pd00['length'].values, pd00['lag'].values


## Export list skip
def export_list_skip(list_skipped):
    list_downloaded_ticker = [f.replace('.csv', '') for f in os.listdir('../data/daily_latest/') if f.endswith('.csv')]
    list_skipped = []
    list_vol = []
    for ticker in list_downloaded_ticker:
        pdraw = pd.read_csv('../data/daily_latest/{}.csv'.format(ticker)).rename(columns={'high': 'High',
                                                                                          'low': 'Low', 'open': 'Open',
                                                                                          'close': 'Close',
                                                                                          'volume': 'Volume'})
        pdraw = pdraw.drop_duplicates(subset=['time'], keep='last').reset_index(drop=True).sort_values('time')
        vol20 = ((pdraw.iloc[-20:]['Volume']) * (pdraw.iloc[-20:]['Close'])).sum()
        if vol20 < 2e+9:
            list_skipped.append(ticker)
            list_vol.append(vol20)
    # output to csv
    pd.DataFrame({'ticker': list_skipped, 'vol20': list_vol}).to_csv('../data/skip.csv', index=False)


def update_data(upto_date, folder='../data', overwrite=False, max_retry=3, START_DATE='2012-01-01',
                fname_skip='../data/skip.csv'):
    gsheet_service = GoogleSheetServices(json_key_file='../core_utils/env/gckey.json')
    gsheet_skip = gsheet_service.get_sheet_data(sheet_name="TickerSkip")['Ticker'].to_list()
    pd_skip = pd.read_csv(fname_skip)
    list_skip = pd_skip['ticker'].to_list() + gsheet_skip

    data_folder = folder + '/' + upto_date
    fa_data_folder = folder + '/' + 'fa_latest'
    # mkdir folder if not exists
    if not os.path.exists(data_folder):
        os.mkdir(data_folder)
    if not os.path.exists(fa_data_folder):
        os.mkdir(fa_data_folder)
    # get list of tickers and indexes
    upto_date = (datetime.datetime.strptime(upto_date, '%Y-%m-%d') + datetime.timedelta(days=1)).strftime('%Y-%m-%d')
    stock_tcbs = Vnstock(show_log=False).stock(source='TCBS')
    stock_vci = Vnstock(show_log=False).stock(source='VCI')
    pd_ticker = stock_vci.listing.symbols_by_industries()

    list_ticker = [f for f in pd_ticker['symbol'].unique() if (len(f) == 3) & (f not in list_skip)]
    # list_ticker = [f for f in stock_vci.listing.symbols_by_group('VN30')]
    # list_ticker = ["API", "VSF", "HPG", "SKG"]
    list_index = ["VNINDEX", "VN30", "HNX", "HNX30", "UPCOM"]

    # get index data
    for ticker in list_index:
        if os.path.exists(f'{data_folder}/{ticker}.csv') and not overwrite:
            continue
        try:
            df = stock_tcbs.quote.history(symbol=ticker, start=START_DATE, end=upto_date, interval='1D',
                                          count_back=None)
            df["ticker"] = ticker
        except:
            df = vnstock.stock_historical_data(symbol=ticker, start_date=START_DATE, end_date=upto_date,
                                               resolution='1D',
                                               type='index')
        df['Price'] = df['close']

        # additional data from investing.con for VNINDEX
        if ticker == 'VNINDEX':
            additonal_df = pd.read_csv(f'../data/fa_latest/ADD_VNINDEX.csv')
            additonal_df['time'] = pd.to_datetime(additonal_df['time'], format='%m/%d/%Y')
            df = pd.concat([df, additonal_df], ignore_index=True, axis=0).drop_duplicates(subset=['time'],
                                                                                          keep='first').sort_values(
                'time', ascending=True).reset_index(drop=True)

        df.to_csv(f'{data_folder}/{ticker}.csv', index=False)

    df = request_pe_vnindex()
    df.to_csv(f'{fa_data_folder}/PE_VNINDEX.csv', index=False)

    # get ticker data
    for ticker in tqdm(list_ticker):
        if os.path.exists(f'{data_folder}/{ticker}.csv') and os.path.exists(
                f'{fa_data_folder}/{ticker}.csv') and not overwrite:
            continue
        retries = 0
        while retries < max_retry:
            try:
                stock_vci.update_symbol(ticker)
                ticker_df = pd_ticker[pd_ticker['symbol'] == ticker]
                df = stock_vci.quote.history(start=START_DATE, end=upto_date, interval='1D', count_back=None)
                df[['open', 'high', 'low', 'close']] = (df[['open', 'high', 'low', 'close']] * 1000)
                df['time'] = df['time'].dt.date
                df['ticker'] = ticker
                df['ICB_Code'] = ticker_df['icb_code4'].values[0]
                df['CT_Code'] = ticker_df['com_type_code'].values[0]

                price = request_unadjust_price(ticker, start_date=START_DATE)
                df = df.merge(price, on='time', how='left')
                df['Price'] = df[['close', 'Price']].max(axis=1)

                # get fundamental data
                if not os.path.exists(f'{fa_data_folder}/{ticker}.csv') or overwrite:
                    df_ratio = stock_vci.finance.ratio(period='quarter', lang='en', dropna=False)
                    df_ratio.columns = df_ratio.columns.droplevel(0)
                    df_ratio.sort_values(['yearReport', 'lengthReport'], ascending=[False, False], inplace=True)

                    df_income = stock_vci.finance.income_statement(period='quarter', lang='en', dropna=False)
                    df_income.sort_values(['yearReport', 'lengthReport'], ascending=[False, False], inplace=True)

                    df_balance = stock_vci.finance.balance_sheet(period='quarter', lang='en', dropna=False)
                    df_balance.sort_values(['yearReport', 'lengthReport'], ascending=[False, False], inplace=True)

                    df_cashflow = stock_vci.finance.cash_flow(period='quarter', lang='en', dropna=False)
                    df_cashflow.sort_values(['yearReport', 'lengthReport'], ascending=[False, False], inplace=True)

                    # finance_yearly_index = stock.finance.ratio(period='year', lang='en')
                    # finance_yearly_index.columns = finance_yearly_index.columns.droplevel(0)
                    # df_finance = pd.concat([finance_quarter_index, finance_yearly_index], axis=0).reset_index(drop=True)

                    pd_fa = pd.merge(df_ratio, df_income, on=['ticker', 'yearReport', 'lengthReport'], how='left'). \
                        merge(df_balance, on=['ticker', 'yearReport', 'lengthReport'], how='left'). \
                        merge(df_cashflow, on=['ticker', 'yearReport', 'lengthReport'], how='left')
                    pd_fa['periods'] = pd_fa['yearReport'].astype(str) + '-' + pd_fa['lengthReport'].astype(str)

                    try:
                        stock_tcbs.update_symbol(ticker)
                        ratio_tcbs = stock_tcbs.finance.ratio(period='quarter', dropna=False)
                        ratio_tcbs[['year', 'quarter']] = ratio_tcbs[['year', 'quarter']].astype(int)

                        cash_flow_tcbs = stock_tcbs.finance.cash_flow(period='quarter')
                        cash_flow_tcbs[['year', 'quarter']] = cash_flow_tcbs[['year', 'quarter']].astype(int)

                        pd_tcbs = pd.merge(ratio_tcbs, cash_flow_tcbs, on=['year', 'quarter'], how='outer')
                        pd_tcbs['periods'] = pd_tcbs['year'].astype(str) + '-' + pd_tcbs['quarter'].astype(str)
                        pd_tcbs.drop_duplicates(subset=['periods'], keep='first', inplace=True)

                        pd_fa = pd.merge(pd_fa, pd_tcbs, on='periods', how='outer').sort_values('periods',
                                                                                                ascending=False).reset_index(
                            drop=True)
                        pd_fa['lengthReport'] = pd_fa[['lengthReport', 'quarter']].max(axis=1).astype(int).astype(str)
                        pd_fa['yearReport'] = pd_fa[['yearReport', 'year']].max(axis=1).astype(int).astype(str)
                        pd_fa['ticker'] = ticker
                        pd_fa.drop(['quarter', 'year'], axis=1, inplace=True)
                    except Exception as e:
                        print(f'Wrong ticker with TCBS: {ticker} - {e}')

                    pd_fa.to_csv(f'{fa_data_folder}/{ticker}.csv', index=False)

                df.to_csv(f'{data_folder}/{ticker}.csv', index=False)
                break
            except Exception as e:
                retries += 1
                print(f'Error: {ticker} - trial = {retries}  | {e}')

                time.sleep(2)
    # create symbolic link
    if os.path.exists(f'{folder}/daily_latest'):
        os.remove(f'{folder}/daily_latest')
    os.symlink(f'{data_folder}', f'{folder}/daily_latest')


def load_ticker(ticker, in_folder='../data/daily_latest'):
    pdraw = pd.read_csv(f'{in_folder}/{ticker}.csv').rename(
        columns={'high': 'High', 'low': 'Low', 'open': 'Open', 'close': 'Close', 'volume': 'Volume'})

    pdraw['time'] = pd.to_datetime(pdraw['time']).dt.strftime('%Y-%m-%d')
    pdraw = pdraw.drop_duplicates(subset=['time'], keep='last').reset_index(drop=True).sort_values('time')
    return pdraw


def load_fa_ticker(ticker, in_folder='../data/fa_latest'):
    if not os.path.exists(f'{in_folder}/{ticker}.csv'):
        return None
    pdraw = pd.read_csv(f'{in_folder}/{ticker}.csv')
    return pdraw


def request_unadjust_price(ticker, start_date="01/01/2012"):
    """Get price date isn't adjusted"""

    def extract_change_value(cell):
        match = re.search(r'\((-?\d+\.\d+) %\)', cell)
        if match:
            return float(match.group(1))
        return np.nan

    url = 'https://s.cafef.vn/Ajax/PageNew/DataHistory/PriceHistory.ashx'
    response = requests.get(
        url,
        params={
            'Symbol': ticker,
            'StartDate': start_date,
            'PageSize': 10000
        }
    )
    if response.status_code != 200:
        raise Exception(response['message'])
    response = response.json()['Data']['Data']
    df = json_normalize(response)

    df['time'] = pd.to_datetime(df['Ngay'], format='%d/%m/%Y')
    df['Price'] = df['GiaDongCua'] * 1000
    # df['Change'] = df['ThayDoi'].apply(extract_change_value)
    # df['time'] = df['time'].dt.strftime('%Y-%m-%d')
    df['time'] = df['time'].dt.date

    px = df[['time', 'Price']].sort_values('time', ascending=True).reset_index(drop=True)

    return px


def request_pe_vnindex():
    """Get price date isn't adjusted"""

    url = 'https://s.cafef.vn/Ajax/PageNew/FinanceData/GetDataChartPE.ashx'
    response = requests.get(url)
    if response.status_code != 200:
        raise Exception(response['message'])
    response = response.json()['Data']['DataChart']
    df = json_normalize(response)

    df['time'] = df['TimeStamp'].agg(lambda x: datetime.datetime.fromtimestamp(x))
    df['time'] = df['time'].dt.date
    df.rename(columns={'Pe': 'VNINDEX_PE'}, inplace=True)
    df.rename(columns={'LNST': 'VNINDEX_LNST'}, inplace=True)

    px = df[['time', 'Index', 'VNINDEX_PE', 'VNINDEX_LNST']].sort_values('time', ascending=True).reset_index(drop=True)

    return px


def get_pdidx(pd_pe=None, pd0=None, pd1=None):
    # index
    pd_pe = load_fa_ticker('PE_VNINDEX') if pd_pe is None else pd_pe
    pd0 = load_ticker('VNINDEX') if pd0 is None else pd0
    pd1 = load_ticker('VN30') if pd1 is None else pd1

    cols_pe = ['time', 'VNINDEX_PE', 'VNINDEX_LNST', 'Trading_Session', 'Volume_Session']
    pd_pe['VNINDEX_PE_MA2Y'] = pd_pe['VNINDEX_PE'].rolling(240 * 2, min_periods=int(240 * 2 * 0.7)).mean()
    pd_pe['VNINDEX_PE_MA4Y'] = pd_pe['VNINDEX_PE'].rolling(240 * 4, min_periods=int(240 * 4 * 0.7)).mean()
    pd_pe['VNINDEX_PE_MA5Y'] = pd_pe['VNINDEX_PE'].rolling(240 * 5, min_periods=int(240 * 5 * 0.7)).mean()

    cols_pe.extend(['VNINDEX_PE_MA2Y', 'VNINDEX_PE_MA4Y', 'VNINDEX_PE_MA5Y'])

    cols0 = ['time', 'VNINDEX', 'VNINDEX_MA200']
    cols1 = ['time', 'VN30', 'VN30_MA200']
    pd0['VNINDEX'] = pd0['Close']
    pd0['VNINDEX_MA200'] = pd0['Close'].rolling(200).mean()

    pd1['VN30'] = pd1['Close']
    pd1['VN30_MA200'] = pd1['Close'].rolling(200).mean()

    for k in ['1W', '2W', '1M', '3M']:
        pd0[f'VNINDEX_T{k}'] = pd0['Close'] / pd0['Close'].shift(DICT_SHIFT[k])
        cols0.append(f'VNINDEX_T{k}')
        pd1[f'VN30_T{k}'] = pd1['Close'] / pd1['Close'].shift(DICT_SHIFT[k])
        cols1.append(f'VN30_T{k}')
    pd0['VNINDEX_RSI'] = AMIRSI(pd0, 14)
    pd0['VNINDEX_MFI'], pd0['VNINDEX_RawMFI'] = MFI(pd0, 14)
    pd0['VNINDEX_CMF'] = CMF(pd0, 20)
    pd0['VNINDEX_MACD'], pd0['VNINDEX_MACDsign'], pd0['VNINDEX_MACDdiff'] = MACD(pd0, 12, 26)
    cols0.extend(
        ['VNINDEX_RSI', 'VNINDEX_MFI', 'VNINDEX_RawMFI', 'VNINDEX_CMF', 'VNINDEX_MACD', 'VNINDEX_MACDsign',
         'VNINDEX_MACDdiff'])

    # pd0['VNINDEX_RSI_T3'] = pd0['VNINDEX_RSI'].shift(3)
    # pd0['VNINDEX_MFI_T3'] = pd0['VNINDEX_MFI'].shift(3)
    # pd0['VNINDEX_RawMFI_T3'] = pd0['VNINDEX_RawMFI'].shift(3)
    # pd0['VNINDEX_CMF_T3'] = pd0['VNINDEX_CMF'].shift(3)
    # pd0['VNINDEX_MACD_T3'] = pd0['VNINDEX_MACD'].shift(3)
    # pd0['VNINDEX_MACDsign_T3'] = pd0['VNINDEX_MACDsign'].shift(3)
    # pd0['VNINDEX_MACDdiff_T3'] = pd0['VNINDEX_MACDdiff'].shift(3)
    # cols0.extend(
    #     ['VNINDEX_RSI_T3', 'VNINDEX_MFI_T3', 'VNINDEX_RawMFI_T3', 'VNINDEX_CMF_T3', 'VNINDEX_MACD_T3',
    #      'VNINDEX_MACDsign_T3', 'VNINDEX_MACDdiff_T3'])

    pd0['VNINDEX_RSI_T1W'] = pd0['VNINDEX_RSI'].shift(DICT_SHIFT['1W'])
    pd0['VNINDEX_MFI_T1W'] = pd0['VNINDEX_MFI'].shift(DICT_SHIFT['1W'])
    pd0['VNINDEX_RawMFI_T1W'] = pd0['VNINDEX_RawMFI'].shift(DICT_SHIFT['1W'])
    pd0['VNINDEX_CMF_T1W'] = pd0['VNINDEX_CMF'].shift(DICT_SHIFT['1W'])
    pd0['VNINDEX_MACD_T1W'] = pd0['VNINDEX_MACD'].shift(DICT_SHIFT['1W'])
    pd0['VNINDEX_MACDsign_T1W'] = pd0['VNINDEX_MACDsign'].shift(DICT_SHIFT['1W'])
    pd0['VNINDEX_MACDdiff_T1W'] = pd0['VNINDEX_MACDdiff'].shift(DICT_SHIFT['1W'])
    cols0.extend(
        ['VNINDEX_RSI_T1W', 'VNINDEX_MFI_T1W', 'VNINDEX_RawMFI_T1W', 'VNINDEX_CMF_T1W', 'VNINDEX_MACD_T1W',
         'VNINDEX_MACDsign_T1W', 'VNINDEX_MACDdiff_T1W'])

    pd1['VN30_RSI'] = AMIRSI(pd1, 14)
    pd1['VN30_MFI'], pd1['VN30_RawMFI'] = MFI(pd1, 14)
    pd1['VN30_CMF'] = CMF(pd0, 20)
    pd1['VN30_MACD'], pd1['VN30_MACDsign'], pd1['VN30_MACDdiff'] = MACD(pd1, 12, 26)
    cols1.extend(['VN30_RSI', 'VN30_MFI', 'VN30_RawMFI', 'VN30_CMF', 'VN30_MACD', 'VN30_MACDsign', 'VN30_MACDdiff'])

    pd1['VN30_RSI_T1W'] = pd1['VN30_RSI'].shift(DICT_SHIFT['1W'])
    pd1['VN30_MFI_T1W'] = pd1['VN30_MFI'].shift(DICT_SHIFT['1W'])
    pd1['VN30_RawMFI_T1W'] = pd1['VN30_RawMFI'].shift(DICT_SHIFT['1W'])
    pd1['VN30_CMF_T1W'] = pd1['VN30_CMF'].shift(DICT_SHIFT['1W'])

    pd1['VN30_MACD_T1W'] = pd1['VN30_MACD'].shift(DICT_SHIFT['1W'])
    pd1['VN30_MACDsign_T1W'] = pd1['VN30_MACDsign'].shift(DICT_SHIFT['1W'])
    pd1['VN30_MACDdiff_T1W'] = pd1['VN30_MACDdiff'].shift(DICT_SHIFT['1W'])
    cols1.extend(
        ['VN30_RSI_T1W', 'VN30_MFI_T1W', 'VN30_RawMFI_T1W', 'VN30_CMF_T1W', 'VN30_MACD_T1W', 'VN30_MACDsign_T1W',
         'VN30_MACDdiff_T1W'])
    #
    # # Vol max
    # pd0['VNINDEX_Volume_Max5Y_ID'], pd0['VNINDEX_Volume_Max5Y_Close'], _ = EXTREME(pd0, cname_x='Volume', type="max",
    #                                                                                period=240 * 5,
    #                                                                                cname_retrieve=['Close'])
    #
    # pd0['VNINDEX_Volume_Max3Y_ID'], pd0['VNINDEX_Volume_Max3Y_Close'], _ = EXTREME(pd0, cname_x='Volume', type="max",
    #                                                                                period=240 * 3,
    #                                                                                cname_retrieve=['Close'])
    # cols0.extend(
    #     ['VNINDEX_Volume_Max5Y_ID', 'VNINDEX_Volume_Max5Y_Close', 'VNINDEX_Volume_Max3Y_ID',
    #      'VNINDEX_Volume_Max3Y_Close'])
    #
    # pd1['VN30_Volume_Max5Y_ID'], pd1['VN30_Volume_Max5Y_Close'], _ = EXTREME(pd1, cname_x='Volume', type="max",
    #                                                                          period=240 * 5,
    #                                                                          cname_retrieve=['Close'])
    #
    # pd1['VN30_Volume_Max3Y_ID'], pd1['VN30_Volume_Max3Y_Close'], _ = EXTREME(pd1, cname_x='Volume', type="max",
    #                                                                          period=240 * 3,
    #                                                                          cname_retrieve=['Close'])
    #
    # cols1.extend(
    #     ['VN30_Volume_Max5Y_ID', 'VN30_Volume_Max5Y_Close', 'VN30_Volume_Max3Y_ID', 'VN30_Volume_Max3Y_Close'])

    # VNINDEX_RSI_Max, VNINDEX_RSI_Min
    for period, p in zip(['1W', '3M'], [5, 60]):
        pd0[f'VNINDEX_RSI_Max{period}'], pd0[f'VNINDEX_RSI_Max{period}_Close'], pd0[f'VNINDEX_RSI_Max{period}_Volume'], \
            pd0[f'VNINDEX_RSI_Max{period}_MACD'], pd0[f'VNINDEX_RSI_Max{period}_MFI'], pd0[
            f'VNINDEX_RSI_Max{period}_CMF'] = PEAK_SIGNAL(pd0,
                                                          cname_x='VNINDEX_RSI',
                                                          cname_retrieve=[
                                                              'Close',
                                                              'Volume',
                                                              'VNINDEX_MACD',
                                                              'VNINDEX_MFI',
                                                              'VNINDEX_CMF'],
                                                          period=p, type='max')
        cols0.extend([f'VNINDEX_RSI_Max{period}', f'VNINDEX_RSI_Max{period}_Close', f'VNINDEX_RSI_Max{period}_MACD',
                      f'VNINDEX_RSI_Max{period}_MFI', f'VNINDEX_RSI_Max{period}_CMF'])

    for period, p in zip(['T3'], [3]):
        pd0[f'VNINDEX_RSI_Min{period}'], pd0[f'VNINDEX_RSI_Min{period}_Close'], pd0[f'VNINDEX_RSI_Min{period}_Volume'], \
            pd0[f'VNINDEX_RSI_Min{period}_MACD'], pd0[f'VNINDEX_RSI_Min{period}_MFI'], pd0[
            f'VNINDEX_RSI_Min{period}_CMF'] = PEAK_SIGNAL(pd0,
                                                          cname_x='VNINDEX_RSI',
                                                          cname_retrieve=[
                                                              'Close',
                                                              'Volume',
                                                              'VNINDEX_MACD',
                                                              'VNINDEX_MFI',
                                                              'VNINDEX_CMF'],
                                                          period=p, type='min')

        cols0.extend([f'VNINDEX_RSI_Min{period}', f'VNINDEX_RSI_Min{period}_Close', f'VNINDEX_RSI_Min{period}_MACD',
                      f'VNINDEX_RSI_Min{period}_MFI', f'VNINDEX_RSI_Min{period}_CMF'])

    # for period, p in zip(['T3', '1W', '1M', '3M'], [3, 5, 20, 60]):
    #     pd1[f'VN30_RSI_Max{period}'], pd1[f'VN30_RSI_Max{period}_Close'], pd1[f'VN30_RSI_Max{period}_MACD'], pd1[
    #         f'VN30_RSI_Max{period}_MFI'], pd1[f'VN30_RSI_Max{period}_CMF'] = PEAK_SIGNAL(pd1, cname_x='VN30_RSI',
    #                                                                                      cname_retrieve=['Close',
    #                                                                                                      'VN30_MACD',
    #                                                                                                      'VN30_MFI',
    #                                                                                                      'VN30_CMF'],
    #                                                                                      period=p, type='max')
    #     pd1[f'VN30_RSI_Min{period}'], pd1[f'VN30_RSI_Min{period}_Close'], pd1[f'VN30_RSI_Min{period}_MACD'], pd1[
    #         f'VN30_RSI_Min{period}_MFI'], pd1[f'VN30_RSI_Min{period}_CMF'] = PEAK_SIGNAL(pd1, cname_x='VN30_RSI',
    #                                                                                      cname_retrieve=['Close',
    #                                                                                                      'VN30_MACD',
    #                                                                                                      'VN30_MFI',
    #                                                                                                      'VN30_CMF'],
    #                                                                                      period=p, type='min')
    #     cols1.extend([f'VN30_RSI_Max{period}', f'VN30_RSI_Max{period}_Close', f'VN30_RSI_Max{period}_MACD',
    #                   f'VN30_RSI_Max{period}_MFI', f'VN30_RSI_Max{period}_CMF',
    #                   f'VN30_RSI_Min{period}', f'VN30_RSI_Min{period}_Close', f'VN30_RSI_Min{period}_MACD',
    #                   f'VN30_RSI_Min{period}_MFI', f'VN30_RSI_Min{period}_CMF'])

    pdidx = pd0[cols0].merge(pd1[cols1], on='time', how='outer').sort_values('time').reset_index(drop=True)
    pdidx.ffill(inplace=True)
    pdidx = pdidx.merge(pd_pe[cols_pe], on='time', how='left')
    pdidx = pdidx.drop_duplicates(subset=['time'], keep='last').reset_index(drop=True)
    return pdidx


def process_ticker_v1(ticker, in_folder='../data/daily_latest', out_folder='../ticker', options={}, pdidx=None):
    # if os.path.exists(f'{out_folder}/{ticker}.csv'):
    #     return "done"
    pdraw = load_ticker(ticker, in_folder=in_folder)
    fa_pdraw = load_fa_ticker(ticker, in_folder='../data/fa_latest')
    pdz1 = compute_fa_indicator_v2(pdraw, fa_pdraw)

    retries = 0
    while retries < 2:
        try:
            pdx1 = compute_indicator_v1(pdraw, options=options)
            pdz1 = compute_fa_indicator_v2(pdraw, fa_pdraw)
            pdy1 = compute_profit_v1(pdraw)

            if pdidx is None:
                pdidx = get_pdidx()

            pdxy = pdx1.merge(pdz1, on='time', how='left').merge(pdy1, on='time', how='left').merge(pdidx, on='time',
                                                                                                    how='left')
            for k in ['1W', '2W', '1M', '3M']:
                pdxy[f'T{k}'] = pdxy['Close'] / pdxy['Close'].shift(DICT_SHIFT[k])

            pdxy.to_csv(f'{out_folder}/{ticker}.csv', index=False)
            break
        except Exception as e:
            print(f'{ticker}: {e}')
            retries += 1
            continue

    return "done"


__T0__ = time.time()


def tic(s=""):
    global __T0__
    __T0__ = time.time()
    if s:
        print(f"{__T0__} {s}")


def toc(s=""):
    print(f"{time.time() - __T0__:0.3f}s {s}")


# import xgboost


# ===================================================
def get_importance_pd(model):
    dict_imp = model.get_booster().get_score(importance_type='gain')
    ll = dict_imp.keys()
    return pd.DataFrame(data={'feature': ll, 'importance': [dict_imp[i] for i in ll]}).sort_values('importance',
                                                                                                   ascending=False)


# ===================================================
def refresh_gpu_model(model, pickle_filename='tmp.pkl'):
    pd.to_pickle(model, 'tmp.pkl')
    model.get_booster().__del__()
    model = pd.read_pickle('tmp.pkl')
    return model


# ===================================================
def train_xgb_classifier(pdXY, cname_feature, cname_label, i_train, list_eval,
                         params={'tree_method': 'hist',
                                 'max_bin': 128,
                                 'max_depth': 3,
                                 'min_child_weight': 10,
                                 'subsample': .7,
                                 'colsample_bytree': .7,
                                 'learning_rate': .1,
                                 'n_estimators': 1000,
                                 'predictor': 'cpu_predictor',
                                 'verbosity': 0,
                                 'eval_metric': 'auc',
                                 'early_stopping_rounds': 100,
                                 }):
    import xgboost
    ii = pdXY[cname_label].notnull()
    X_train = pdXY[ii & i_train][cname_feature]
    y_train = pdXY[ii & i_train][cname_label].astype(int).values
    eval_set = []
    for (dname, i_test) in list_eval:
        eval_set.append((pdXY[ii & i_test][cname_feature], (pdXY[ii & i_test][cname_label].astype(int).values)))
    model = xgboost.XGBClassifier(**params)
    model.fit(X_train, y_train, eval_set=eval_set,
              verbose=params['verbosity'])
    model = refresh_gpu_model(model)
    best_ntree = model.get_booster().best_ntree_limit
    if (xgboost.__version__ >= '1.4'):
        y_pred = model.predict_proba(pdXY[cname_feature], iteration_range=(0, best_ntree))[:, 1]
    else:
        y_pred = model.predict_proba(pdXY[cname_feature], ntree_limit=best_ntree)[:, 1]
    return y_pred, model


def get_list_eval(pdxy, cname_tvt='tvt'):
    list_tvt = list(pdxy[cname_tvt].unique())
    i_train = pdxy[cname_tvt] == 'train'
    list_eval = [('train', i_train)]
    for tvt in list_tvt:
        if tvt.startswith('test'):
            i_test = pdxy[cname_tvt] == tvt
            list_eval.append((tvt, i_test))
    i_val = pdxy[cname_tvt] == 'val'
    list_eval.append(('val', i_val))
    return list_eval


def split_tvt_v1(pdxy, cname_tvt='tvt', test_size=20, YMD_test='2021-01-01'):
    """
    Split data into train, val, test
    """
    list_ticker = list(pdxy['ticker'].unique())
    list_ticker_test = [list_ticker[i] for i in range(len(list_ticker)) if i % 100 < test_size]
    iV = (pdxy['Volume'] * pdxy['Close'] > 200e6)
    pdxy[cname_tvt] = 'other'
    iA = pdxy['time'] < YMD_test
    iB = ~iA
    iTest = pdxy['ticker'].isin(list_ticker_test)
    iTrain = ~iTest
    pdxy.loc[iV & iA & iTrain, cname_tvt] = 'train'
    pdxy.loc[iV & iB & iTrain, cname_tvt] = 'val'
    pdxy.loc[iV & iA & iTest, cname_tvt] = 'test1'
    pdxy.loc[iV & iB & iTest, cname_tvt] = 'test2'
    return pdxy


def prepare_pdxy_v1(CUTLOSS=0.1, in_folder='../ticker_v1a/'):
    ## combine all ticker data into one file
    list_processed_ticker = [f.replace('.csv', '') for f in os.listdir(in_folder) if f.endswith('.csv')]
    ll = [pd.read_csv(f'{in_folder}/{ticker}.csv') for ticker in list_processed_ticker]
    print(0, ll[0]['time'].max())
    pdxy = pd.concat(ll, axis=0, ignore_index=True)  # .reset_index(drop=True)
    print(1, pdxy['time'].max())
    cname_feature = [f for f in pdxy.columns if f not in ['time', 'ticker', 'ymd', 'L1W', 'H1M', 'H3M', 'H1Y', 'H2Y',
                                                          'L1W', 'L1M', 'L3M', 'L1Y', 'L2Y',
                                                          'Open', 'Close', 'High', 'Low', 'Volume']]
    pdxy['Y1'] = (pdxy['L1M'] > 1. - CUTLOSS) & (pdxy['H1M'] > 1 + CUTLOSS * 3)  # win 30% after 1 month
    pdxy.loc[pdxy['L1M'].isnull(), 'Y1'] = np.nan
    pdxy['Y2'] = (pdxy['L1Y'] > 1. - CUTLOSS) & (pdxy['H1Y'] > 1 + CUTLOSS * 5)  # win 50% after 1 year
    pdxy.loc[pdxy['L1Y'].isnull(), 'Y2'] = np.nan
    pdxy['Y3'] = (pdxy['L1M'] > 1. - CUTLOSS) & (pdxy['H1M'] > 1 + CUTLOSS * 3)  # cutloss after 1 month
    pdxy.loc[pdxy['L1M'].isnull(), 'Y3'] = np.nan
    pdxy['Y4'] = (pdxy['L2Y'] > 1. - CUTLOSS) & (pdxy['H2Y'] > 1 + CUTLOSS * 10)  # win 100% after 2 years
    pdxy.loc[pdxy['L2Y'].isnull(), 'Y4'] = np.nan
    cname_tvt = 'tvt'
    split_tvt_v1(pdxy, cname_tvt)
    list_eval = get_list_eval(pdxy, cname_tvt=cname_tvt)
    list_label = ['Y1', 'Y2', 'Y3', 'Y4']
    print(2, pdxy['time'].max())
    return {'pdxy': pdxy,
            'cname_feature': cname_feature,
            'list_label': list_label,
            'list_eval': list_eval,
            }


def apply_filter(df, filter):
    idx = df.query(filter).index
    return df.index.isin(idx).astype(int)


def preprocess_dictFilter(strFilter):
    dictFilter = json.loads(strFilter)
    print("input", dictFilter)
    list_key = list(dictFilter.keys())
    for f in list_key:
        if f.startswith('_'):
            dictFilter[f[1:]] = dictFilter[f]
    list_key = dictFilter.keys()
    for f in list_key:
        for k in list_key:
            dictFilter[f] = dictFilter[f].replace("{" + k + "}", "(" + dictFilter[k] + ")")
    print("output", dictFilter)
    return dictFilter


def eval_filter(pd_all, dictFilter, CUTLOSS=0.1):
    dictP = {"filter": [],
             "hit": [],
             'Profit 1W': [],
             'Profit 1M': [],
             'Profit 3M': [],
             'Profit 1Y': [],
             'Profit 2Y': [],
             f'Win {CUTLOSS * 200:.0f}% 1W': [],
             f'Win {CUTLOSS * 300:.0f}% 1M': [],
             f'Win {CUTLOSS * 300:.0f}% 3M': [],
             f'Win {CUTLOSS * 500:.0f}% 1Y': [],
             f'Win {CUTLOSS * 1000:.0f}% 2Y': [],
             f'CutLoss {CUTLOSS * 100:.0f}% 1W': [],
             f'CutLoss {CUTLOSS * 100:.0f}% 1M': [],
             f'CutLoss {CUTLOSS * 100:.0f}% 3M': [],
             f'CutLoss {CUTLOSS * 100:.0f}% 1Y': [],
             f'CutLoss {CUTLOSS * 100:.0f}% 2Y': [],
             }
    for s in ['1W', '1M', '3M', '1Y', '2Y']:
        pd_all[f"P{s}"] = (pd_all[f"H{s}"] - 1) * (pd_all[f"L{s}"] > 1 - CUTLOSS) + (-CUTLOSS) * (
                pd_all[f"L{s}"] <= 1 - CUTLOSS)

    for f in dictFilter:
        if not (f.startswith('_')):
            continue
        pdx = pd_all.query(f"({dictFilter[f]})&(L2Y==L2Y)").copy()
        pdx['month'] = pdx['time'].map(lambda x: x[:7])
        pdx['weight'] = 1 / pdx.groupby(['month', 'ticker'])['ticker'].transform('count')
        pdx['weight'] = pdx['weight'] / pdx['weight'].sum()
        dictP['filter'].append(f[1:])
        dictP['hit'].append(pdx.shape[0])
        for s in ['1W', '1M', '3M', '1Y', '2Y']:
            dictP[f'Profit {s}'].append((pdx[f'P{s}'] * pdx['weight']).sum())
        dictP[f'Win {CUTLOSS * 200:.0f}% 1W'].append(((pdx['P1W'] > CUTLOSS * 2) * pdx['weight']).sum())
        dictP[f'Win {CUTLOSS * 300:.0f}% 1M'].append(((pdx['P1M'] > CUTLOSS * 3) * pdx['weight']).sum())
        dictP[f'Win {CUTLOSS * 300:.0f}% 3M'].append(((pdx['P3M'] > CUTLOSS * 3) * pdx['weight']).sum())
        dictP[f'Win {CUTLOSS * 500:.0f}% 1Y'].append(((pdx['P1M'] > CUTLOSS * 5) * pdx['weight']).sum())
        dictP[f'Win {CUTLOSS * 1000:.0f}% 2Y'].append(((pdx['P1M'] > CUTLOSS * 10) * pdx['weight']).sum())
        for s in ['1W', '1M', '3M', '1Y', '2Y']:
            dictP[f'CutLoss {CUTLOSS * 100:.0f}% {s}'].append(((pdx[f'L{s}'] <= 1 - CUTLOSS) * pdx['weight']).sum())
    return pd.DataFrame(dictP)


def process_raw_vnindex():
    respone = open('response.json', 'r')
    respone = json.load(respone)
    df = json_normalize(respone['data'])
    df.drop(columns=['volume'], inplace=True)
    df = df.rename(
        columns={'last_closeRaw': 'close', 'last_openRaw': 'open', 'last_minRaw': 'low', 'last_maxRaw': 'high',
                 'volumeRaw': 'volume'})

    # df['last_close'].apply(lambda x: float(x.replace(',', '')))
    df['volume'] = df['volume'] * 1e3
    df['ticker'] = 'VNINDEX'
    df['Price'] = df['close']
    df['rowDateRaw'].agg(lambda x: datetime.datetime.fromtimestamp(x))

    df['time'] = df['rowDateRaw'].agg(lambda x: datetime.datetime.fromtimestamp(x))
    df['time'] = df['time'].dt.date
    df_sa = df[['time', 'open', 'high', 'low', 'close', 'volume', 'ticker', 'Price']].copy()
    df_sa.to_csv('../data/fa_latest/VNINDEX.csv', index=False)


if __name__ == "__main__":
    now = time.time()

    # ind_dictionary = get_keys()
    # dictionary = list(DICTIONARY.keys())
    # empty = [i for i in dictionary if i not in ind_dictionary]

    # process_raw_vnindex()
    # get_pdidx()
    process_ticker_v1('HPG', out_folder='../ticker_v1a', options={"len": 5})
    #
    # pdraw = load_ticker('KSV', in_folder='../data/daily_latest')
    # pdraw = pdraw.tail(240 * 5 + 100).reset_index(drop=True)
    # a = compute_indicator_daily(pdraw)
    # a = a.sort_values('Price').reset_index(drop=True)
    # b = b.sort_values('Price').reset_index(drop=True)
    # print(time.time() - now)

    # revert_indicator_v1('HPG', "")

# pdraw = load_ticker('FPT', in_folder='../ticker_v1a')
# fa_pdraw = load_fa_ticker('FPT', in_folder='../data/fa_latest')
# now = time.time()
# pdz2 = compute_fa_indicator_v2(pdraw, fa_pdraw)
#
# pdz2.fillna(-123, inplace=True)
# print(time.time() - now)
#
# now = time.time()
# pdz1 = compute_fa_indicator_v1(pdraw, fa_pdraw)
# pdz1.fillna(-123, inplace=True)
# print(time.time() - now)
#
# print((pdz1 == pdz2).all())
