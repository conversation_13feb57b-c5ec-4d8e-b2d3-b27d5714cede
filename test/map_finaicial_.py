# -*- coding: utf-8 -*-
"""
Auto-fill b2 từ Book1 và ghi đè ra b2.csv (tạo file backup trước).
- Map 1:1 + t<PERSON>h gi<PERSON> tiếp (margins->profit, turnover, CCC, Price, DPS, leverage, FCF).
- Chỉ ghi đè vào b2 nếu ô đang NaN. Nếu muốn ép overwrite 100%, bật FORCE_OVERWRITE=True.
"""

import pandas as pd
import numpy as np
import re
from datetime import datetime
from typing import Dict, Tuple, Optional

# ====== CONFIG ======
PATH_BOOK1 = "Book1.csv"
PATH_B2    = "b2.csv"
FORCE_OVERWRITE = False  # đổi True nếu muốn luôn ghi đè

# ====== OUTPUT ======
timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
BACKUP_B2 = f"b2_backup_{timestamp}.csv"
REPORT    = f"b2_autofilled_report_{timestamp}.csv"

# ====== HELPERS ======
def normalize_cols(df: pd.DataFrame) -> pd.DataFrame:
    df = df.copy()
    df.columns = [str(c).strip() for c in df.columns]
    return df

def ensure_b2_periods(b2: pd.DataFrame) -> pd.DataFrame:
    b2 = b2.copy()
    def find_col(name):
        for c in b2.columns:
            if c.lower() == name.lower():
                return c
        return None
    year_col = find_col("yearReport")
    len_col  = find_col("lengthReport")
    if year_col is not None and len_col is not None:
        b2["periods"] = b2[year_col].astype("Int64").astype(str) + "-" + b2[len_col].astype("Int64").astype(str)
        return b2
    if "periods" in b2.columns:
        return b2
    b2["periods"] = np.nan
    return b2

def looks_like_ratio(series: pd.Series) -> bool:
    s = pd.to_numeric(series, errors="coerce").dropna()
    if s.empty: return False
    med = s.median()
    return (med >= 0) and (med <= 3)

def safe_mul(series: pd.Series, factor: float) -> pd.Series:
    return pd.to_numeric(series, errors="coerce") * factor

def choose_fill(src: pd.Series, dst: pd.Series) -> pd.Series:
    if FORCE_OVERWRITE:
        return src.combine_first(dst)
    else:
        return np.where(pd.isna(dst), src, dst)

def find_b2_col_soft(target: str, columns) -> Optional[str]:
    def norm(s):
        return re.sub(r"[^a-z0-9]+", "", s.lower())
    t = norm(target)
    for c in columns:
        if norm(c) == t:
            return c
    for c in columns:
        if t in norm(c):
            return c
    return None

def num(s):  # ép số an toàn
    return pd.to_numeric(s, errors="coerce")

def as_ratio(margin_series):
    s = num(margin_series)
    if looks_like_ratio(s):
        return s
    return s / 100.0

# ====== LOAD ======
book1 = pd.read_csv(PATH_BOOK1)
b2    = pd.read_csv(PATH_B2)

book1 = normalize_cols(book1)
b2    = normalize_cols(b2)
b2    = ensure_b2_periods(b2)

# backup b2 trước khi ghi đè
b2.to_csv(BACKUP_B2, index=False)

# Merge để lấy nguồn book1 (suffix _b1)
merged = b2.merge(book1.add_suffix("_b1"), left_on="periods", right_on="periods_b1", how="left")

# ====== MAPPING 1:1 ======
mapping: Dict[str, Tuple[str, bool]] = {
    "P/E": ("price_to_earning", False),
    "P/B": ("price_to_book", False),
    "EPS (VND)": ("earning_per_share", False),
    "BVPS (VND)": ("book_value_per_share", False),

    "ROE (%)": ("roe", True),
    "ROA (%)": ("roa", True),

    "Debt/Equity": ("debt_on_equity", False),
    "Equity/Assets": ("equity_on_asset", True),
    "Debt/Assets": ("debt_on_asset", True),

    "Current Ratio": ("current_ratio", False),
    "Quick Ratio": ("quick_ratio", False),
    "Asset Turnover": ("asset_turnover", False),

    # "Days Sales Outstanding": ("days_receivable", False),
    # "Days Inventory Outstanding": ("days_inventory", False),
    # ("Inventory Turnover", "days_inventory"),
    # "Days Payable Outstanding": ("days_payable", False),
    #
    # "Gross Profit Margin (%)": ("gross_margin", True),
    # "Operating Profit Margin (%)": ("operating_margin", True),
    # "Pretax Profit Margin (%)": ("pretax_profit_margin", True),
    # "Net Profit Margin (%)": ("net_margin", True),
    # "EBITDA Margin (%)": ("ebitda_on_revenue", True),
    #
    # "Dividend yield (%)": ("dividend", True),
}

resolved_mapping = {}
for b2_target, (b1_source, is_pct) in mapping.items():
    b2_real = find_b2_col_soft(b2_target, merged.columns)
    b1_real = b1_source + "_b1"
    if b2_real is not None and b1_real in merged.columns:
        resolved_mapping[b2_real] = (b1_real, is_pct)

report_rows = []

# apply direct fill
for b2_col, (b1_col, is_pct) in resolved_mapping.items():
    src = merged[b1_col]
    if is_pct:
        factor = 100.0 if looks_like_ratio(src) else 1.0
        src = safe_mul(src, factor)
    before_na = merged[b2_col].isna().sum()
    merged[b2_col] = choose_fill(src, merged[b2_col])
    after_na = merged[b2_col].isna().sum()
    filled = max(0, before_na - after_na) if not FORCE_OVERWRITE else (merged[b2_col].notna().sum() - (len(merged)-before_na))
    report_rows.append(["direct", b2_col, b1_col, is_pct, filled])

# ====== DERIVED FIELDS ======
def soft(name):
    return find_b2_col_soft(name, merged.columns)

def log_report(kind, tgt, src, scaled, filled):
    report_rows.append([kind, tgt, src, scaled, filled])

# 1) Profit amounts from margins * revenue
rev_col = soft("Revenue")
if rev_col is not None:
    rev = num(merged[rev_col])
    derived_map = [
        ("Gross Profit (VND)", "gross_margin_b1"),
        ("Operating Profit", "operating_margin_b1"),
        ("EBIT", "operating_margin_b1"),
        ("EBITDA", "ebitda_on_revenue_b1"),
        ("Pretax Profit", "pretax_profit_margin_b1"),
        ("Net Profit", "net_margin_b1"),
        ("Net Income", "net_margin_b1"),
    ]
    for target_b2, b1_margin in derived_map:
        if b1_margin in merged.columns:
            tcol = soft(target_b2)
            if tcol is None:
                continue
            src_val = as_ratio(merged[b1_margin]) * rev
            before_na = merged[tcol].isna().sum()
            merged[tcol] = choose_fill(src_val, merged[tcol])
            after_na = merged[tcol].isna().sum()
            filled = max(0, before_na - after_na) if not FORCE_OVERWRITE else (merged[tcol].notna().sum() - (len(merged)-before_na))
            log_report("derived:margin*rev", tcol, b1_margin+" & "+rev_col, False, filled)

# 2) Turnover & CCC
pairs = [
    ("Receivable Turnover", "days_receivable_b1"),
    ("Inventory Turnover", "days_inventory_b1"),
    ("Payables Turnover",  "days_payable_b1"),
]
for tgt, days_col in pairs:
    if days_col in merged.columns:
        tcol = soft(tgt)
        if tcol is not None:
            days = num(merged[days_col])
            src_val = 365.0 / days.replace(0, np.nan)
            before_na = merged[tcol].isna().sum()
            merged[tcol] = choose_fill(src_val, merged[tcol])
            after_na = merged[tcol].isna().sum()
            filled = max(0, before_na - after_na) if not FORCE_OVERWRITE else (merged[tcol].notna().sum() - (len(merged)-before_na))
            log_report("derived:turnover", tcol, days_col, False, filled)

ccc_col = soft("Cash Conversion Cycle")
if ccc_col is not None and {"days_receivable_b1","days_inventory_b1","days_payable_b1"}.issubset(merged.columns):
    src_val = num(merged["days_receivable_b1"]) + num(merged["days_inventory_b1"]) - num(merged["days_payable_b1"])
    before_na = merged[ccc_col].isna().sum()
    merged[ccc_col] = choose_fill(src_val, merged[ccc_col])
    after_na = merged[ccc_col].isna().sum()
    filled = max(0, before_na - after_na) if not FORCE_OVERWRITE else (merged[ccc_col].notna().sum() - (len(merged)-before_na))
    log_report("derived:ccc", ccc_col, "DSO+DIO-DPO", False, filled)

# 3) Price & DPS
pe_col  = soft("P/E")
pb_col  = soft("P/B")
eps_col = soft("EPS (VND)")
bvps_col= soft("BVPS (VND)")
yield_col = soft("Dividend yield (%)")

price_col = soft("Price") or soft("Market Price")
price_est = None
if pe_col is not None and eps_col is not None:
    price_est = num(merged[pe_col]) * num(merged[eps_col])
elif pb_col is not None and bvps_col is not None:
    price_est = num(merged[pb_col]) * num(merged[bvps_col])

if price_est is not None and price_col is not None:
    before_na = merged[price_col].isna().sum()
    merged[price_col] = choose_fill(price_est, merged[price_col])
    after_na = merged[price_col].isna().sum()
    filled = max(0, before_na - after_na) if not FORCE_OVERWRITE else (merged[price_col].notna().sum() - (len(merged)-before_na))
    log_report("derived:price", price_col, "P/E*EPS or P/B*BVPS", False, filled)

if yield_col is not None:
    pcol = price_col if price_col is not None else None
    if pcol is not None:
        dps_col = soft("Dividend per share") or soft("DPS")
        if dps_col is not None:
            src_val = num(merged[yield_col]) * num(merged[pcol]) / 100.0
            before_na = merged[dps_col].isna().sum()
            merged[dps_col] = choose_fill(src_val, merged[dps_col])
            after_na = merged[dps_col].isna().sum()
            filled = max(0, before_na - after_na) if not FORCE_OVERWRITE else (merged[dps_col].notna().sum() - (len(merged)-before_na))
            log_report("derived:dps", dps_col, f"{yield_col} & {pcol}", True, filled)

# 4) Leverage transforms
dea_col = soft("Debt/Assets")
ea_col  = soft("Equity/Assets")
de_col  = soft("Debt/Equity")

if ea_col is not None and "debt_on_asset_b1" in merged.columns:
    ea_src = 1.0 - num(merged["debt_on_asset_b1"])
    before_na = merged[ea_col].isna().sum()
    merged[ea_col] = choose_fill(ea_src, merged[ea_col])
    after_na = merged[ea_col].isna().sum()
    filled = max(0, before_na - after_na) if not FORCE_OVERWRITE else (merged[ea_col].notna().sum() - (len(merged)-before_na))
    report_rows.append(["derived:EA", ea_col, "1 - debt_on_asset_b1", False, filled])

if dea_col is not None and "equity_on_asset_b1" in merged.columns:
    da_src = 1.0 - num(merged["equity_on_asset_b1"])
    before_na = merged[dea_col].isna().sum()
    merged[dea_col] = choose_fill(da_src, merged[dea_col])
    after_na = merged[dea_col].isna().sum()
    filled = max(0, before_na - after_na) if not FORCE_OVERWRITE else (merged[dea_col].notna().sum() - (len(merged)-before_na))
    report_rows.append(["derived:DA", dea_col, "1 - equity_on_asset_b1", False, filled])

if de_col is not None:
    da = None; ea = None
    if dea_col is not None:
        da = num(merged[dea_col])
    elif "debt_on_asset_b1" in merged.columns:
        da = num(merged["debt_on_asset_b1"])
    if ea_col is not None:
        ea = num(merged[ea_col])
    elif "equity_on_asset_b1" in merged.columns:
        ea = num(merged["equity_on_asset_b1"])
    if da is not None and ea is not None:
        de_src = da / ea.replace(0, np.nan)
        before_na = merged[de_col].isna().sum()
        merged[de_col] = choose_fill(de_src, merged[de_col])
        after_na = merged[de_col].isna().sum()
        filled = max(0, before_na - after_na) if not FORCE_OVERWRITE else (merged[de_col].notna().sum() - (len(merged)-before_na))
        report_rows.append(["derived:D/E", de_col, "DA/EA", False, filled])

# 5) FCF ≈ CFO - Capex
cfo_col   = soft("Net cash flows from operating activities")
capex_col = soft("Investment in fixed assets")
fcf_col   = soft("Free Cash Flow") or soft("FCF")
if cfo_col is not None and capex_col is not None and fcf_col is not None:
    fcf_src = num(merged[cfo_col]) - num(merged[capex_col])
    before_na = merged[fcf_col].isna().sum()
    merged[fcf_col] = choose_fill(fcf_src, merged[fcf_col])
    after_na = merged[fcf_col].isna().sum()
    filled = max(0, before_na - after_na) if not FORCE_OVERWRITE else (merged[fcf_col].notna().sum() - (len(merged)-before_na))
    report_rows.append(["derived:FCF", fcf_col, f"{cfo_col} - {capex_col}", False, filled])

# ====== WRITE BACK TO b2 ======
report_df = pd.DataFrame(report_rows, columns=["type","b2_target","book1_or_formula","percent_scaled","rows_filled"])
report_df.to_csv(REPORT, index=False)

drop_cols = [c for c in merged.columns if c.endswith("_b1")] + ["periods_b1"]
final_b2 = merged.drop(columns=[c for c in drop_cols if c in merged.columns])

final_b2.to_csv('b2_fill.csv', index=False)

print(f"Đã backup b2 gốc: {BACKUP_B2}")
print(f"Đã ghi đè b2.csv với dữ liệu auto-fill.")
print(f"Báo cáo: {REPORT}")
