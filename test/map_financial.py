# -*- coding: utf-8 -*-
"""
Map Book1.csv (A) <-> b2.csv (B)
- Ưu tiên lấy từ B; nếu B thiếu -> lấy A
- Tự đoán/chuẩn hóa đơn vị: %, ratio, VND, Bn. VND, days
- Với chỉ tiêu không có trực tiếp ở B, tính "tương đương" từ các cột liên quan của B
- Xuất: merged_mapped.csv + merged_log.csv (có thể đổi đường dẫn cuối file)
"""

import pandas as pd
from typing import Optional, Dict, Any


# ---------- Helpers: unit parsing & conversion ----------

def parse_unit_from_name(col_name: str) -> Optional[str]:
    """
    Suy đoán đơn vị từ tên cột (B: vì hay có '(Bn. VND)', '(VND)', '(%)', 'Days ...').
    """
    if not isinstance(col_name, str):
        return None
    name = col_name.lower()
    if "(bn. vnd)" in name or "bn. vnd" in name or "bn vnd" in name:
        return "vnd_billion"
    if "(vnd)" in name:
        return "vnd"
    if "(%)" in name or "margin (%)" in name or "yoy (%)" in name:
        return "percent"
    if "days" in name:
        return "days"
    return None  # dimensionless/unknown


def guess_ratio_unit_from_values(series: pd.Series) -> str:
    """
    Đoán 1 cột tỷ lệ đang ở dạng ratio (0..1) hay percent (0..100).
    """
    s = pd.to_numeric(series, errors="coerce").dropna()
    if s.empty:
        return "unknown"
    # nếu đa số |x| <= ~1.2 => ratio
    return "ratio" if (s.abs() <= 1.2).mean() > 0.7 else "percent"


def normalize_value(value, src_unit: Optional[str], dst_unit: Optional[str]):
    """
    Convert đơn vị src -> dst. Giữ nguyên nếu không convert được.
    Hỗ trợ:
      - vnd_billion <-> vnd
      - ratio <-> percent
      - days/None: giữ nguyên
    """
    if value is None or pd.isna(value):
        return value
    try:
        x = float(value)
    except Exception:
        return value

    if src_unit == dst_unit or dst_unit is None or src_unit is None:
        return x

    # Tiền tệ
    if src_unit == "vnd_billion" and dst_unit == "vnd":
        return x * 1_000_000_000
    if src_unit == "vnd" and dst_unit == "vnd_billion":
        return x / 1_000_000_000

    # Tỷ lệ
    if src_unit == "ratio" and dst_unit == "percent":
        return x * 100.0
    if src_unit == "percent" and dst_unit == "ratio":
        return x / 100.0

    return x  # đơn vị khác không map -> giữ nguyên


def get_series(df: pd.DataFrame, col: Optional[str]) -> Optional[pd.Series]:
    return df[col] if (isinstance(col, str) and col in df.columns) else None


# ---------- Load dữ liệu ----------
A = pd.read_csv("Book1.csv")  # file A
B = pd.read_csv("b2.csv")  # file B

# ---------- Spec mapping (chuẩn hóa cột đích + đơn vị) ----------
# cột_đích -> {B: 'col_B', A: 'col_A', unit: 'đơn vị đích', ...derive...}
MAP: Dict[str, str] = {
    'P/E': "price_to_earning",
    "P/B": "price_to_book",
    "EV/EBITDA": "value_before_ebitda",

    "ROE (%)": "roe",
    "ROA (%)": "roa",
    "EBIT Margin (%)": "ebit_on_revenue",

    "Days Sales Outstanding": "days_receivable",
    "Days Inventory Outstanding": "days_inventory",
    "Interest Coverage": "ebit_on_interest",  # (negative)
    "Current Ratio": "current_payment",
    "Quick Ratio": "quick_payment",

    "Asset Turnover": "revenue_on_asset",
    "Debt/Equity": "payable_on_equity",
    "Gross Profit Margin (%)": "gross_profit_margin",
}

# ---------- Build output ----------
n = max(len(A), len(B))
out = pd.DataFrame(index=range(n))
logs = []

for col_b, col_a in MAP.items():
    colB = col_b
    colA = col_a[0]
    sB = get_series(B, colB)
    sA = get_series(A, colA)
    sC = sA - sB
    continue

for target, spec in MAP.items():
    desired_unit = spec.get("unit")
    colB = spec.get("B")
    colA = spec.get("A")
    sB = get_series(B, colB)
    sA = get_series(A, colA)
    sC = sA - sB
    continue

# ---------- Xuất kết quả ----------
out.to_csv("/mnt/data/merged_mapped.csv", index=False)
pd.DataFrame(logs).to_csv("/mnt/data/merged_log.csv", index=False)

print("DONE -> /mnt/data/merged_mapped.csv")
print("LOG  -> /mnt/data/merged_log.csv")
