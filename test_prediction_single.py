#!/usr/bin/env python3
"""
Test prediction pipeline với 1 ticker để debug
"""

import os
import sys
import warnings

warnings.filterwarnings('ignore')

# Setup paths
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

# Import pipeline
from deeplearning.prediction_pipeline import PredictionPipeline


def test_single_ticker(ticker='VIC'):
    """Test pipeline với 1 ticker cụ thể."""
    print(f"Testing prediction pipeline với ticker: {ticker}")

    # Khởi tạo pipeline
    pipeline = PredictionPipeline()

    # Load model
    print("Loading model...")
    pipeline.load_model()

    # Setup score mapper
    print("Setting up score mapper...")
    pipeline.setup_score_mapper()

    # Test với 1 ticker
    print(f"Processing ticker {ticker}...")
    try:
        result = pipeline.process_ticker(ticker)

        if result is not None:
            print("SUCCESS!")
            print(f"Result shape: {result.shape}")
            print(f"Score range: {result['score'].min():.3f} to {result['score'].max():.3f}")
            print("Sample results:")
            print(result.head())

            # Save kết quả
            output_file = f"test_prediction_{ticker}.csv"
            result.to_csv(output_file, index=False)
            print(f"Saved to {output_file}")

        else:
            print("FAILED: No result returned")

    except Exception as e:
        print(f"ERROR: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description='Test prediction pipeline với 1 ticker')
    parser.add_argument('--ticker', type=str, default='HPG', help='Ticker symbol to test')

    args = parser.parse_args()
    test_single_ticker(args.ticker)
