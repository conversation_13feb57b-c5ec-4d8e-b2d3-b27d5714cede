import os
from io import BytesIO
import numpy as np
import pandas as pd
from celery import Task
import glob
from celery_app import gcs_service, redis_cache, logger

FPATH = "ticker_v1a/"
FA_PATH = 'data/fa_latest/'
TEMP_PATH = "temp/"
OFFSET = 50
DICT_SHIFT = {'1W': 5, '2W': 10, '1M': 20, '3M': 60, '6M': 120, '1Y': 240, '2Y': 480}

def apply_percentile_bins(df, prefix='downside-beta', q_col='quarter', n_bins=5):
    df = df.copy()

    cols = sorted([col for col in df.columns if col.endswith(f"_{prefix}")])
    mask_cols = [value_col + '_mask' for value_col in cols]
    if mask_cols:
        df[mask_cols] = df[mask_cols].fillna(False).astype(bool)

    for value_col in cols:
        mask_col = value_col + '_mask'
        if mask_col in df.columns:
            df.loc[~df[mask_col], value_col] = np.nan

    def bin_row(row):
        values = row[cols].dropna()
        if len(values) < n_bins:
            return pd.Series({f"{col}_bin": None for col in cols})
        try:
            bins = pd.qcut(values, q=n_bins, labels=range(1, n_bins + 1))
            bin_result = {}
            for col in cols:
                val = bins.get(col, None) if col in bins else None
                bin_result[f"{col}_bin"] = int(val) if pd.notna(val) else None
            return pd.Series(bin_result)
        except ValueError:
            return pd.Series({f"{col}_bin": None for col in cols})

    bin_df = df.apply(bin_row, axis=1)
    df = pd.concat([df[q_col], bin_df], axis=1)
    return df


def load_data(fname, fpath, use_cols=None):
    file_path = fpath + fname.replace('.csv', '').replace('*', '') + '.csv'
    if not os.path.exists(file_path):
        return pd.DataFrame()

    if use_cols is not None:
        df = pd.read_csv(file_path, dtype={'time': str, 'ticker': str}, usecols=use_cols)
    else:
        df = pd.read_csv(file_path, dtype={'time': str, 'ticker': str})

    return df


def save_data(df, fname, fpath):
    if not os.path.exists(fpath):
        os.mkdir(fpath)
    path = fpath + fname.replace('.csv', '').replace('*', '') + '.csv'
    df.to_csv(path, index=False)

    return path


def check_release_quarter_report(time):
    year, month, day = time.split('-')
    time = int(month) * 100 + int(day)
    if 100 < time <= 400:  # Jan 1st - April 0th
        # key = f'{int(year) - 1}Q4'
        year = int(year) - 1
        length = 4
    elif 400 < time <= 700:  # April 1st - July 0th
        # key = f"{year}Q1"
        year = int(year)
        length = 1
    elif 700 < time <= 1000:  # July 1st -October 0th
        # key = f"{year}Q2"
        year = int(year)
        length = 2
    else:  # October 1st - End year
        # key = f"{year}Q3"
        year = int(year)
        length = 3

    return str(year), str(length)


def report_date(year, length):
    if length == '1' or length == 1:
        date = f"{year}-05-01"
    elif length == '2' or length == 2:
        date = f"{year}-08-01"
    elif length == '3' or length == 3:
        date = f"{year}-11-01"
    elif length == '4' or length == 4:
        date = f"{int(year) + 1}-02-01"
    else:
        date = np.nan
    return date
    # return pd.to_datetime(date).date()


def before_key(key):
    year, q = key.split('Q')
    q = int(q) - 1
    if q == 0:
        year = int(year) - 1
        q = 4
    return f'{year}Q{q}'


def after_key(key):
    year, q = key.split('Q')
    q = int(q) + 1
    if q == 5:
        year = int(year) + 1
        q = 1
    return f'{year}Q{q}'


@redis_cache.cache_method(expiry=3600)
def get_stock_list():
    logger.info(f"Get stock_list in cloud storage")
    ticker_df = gcs_service.download_file_to_memory("rawdata/stock_meta/latest/ticker_list.csv")
    ticker_df = pd.read_csv(BytesIO(ticker_df))

    return ticker_df


@redis_cache.cache_method(expiry=3600)
def get_list_exist_files(prefix):
    logger.info(f"Get list exist files in cloud storage")
    list_obj = gcs_service.list_files(prefix=prefix)

    return list_obj


@redis_cache.cache_method(expiry=864000)
def get_list_exist_files_not_change_regular(prefix):
    logger.info(f"Get list exist files in cloud storage")
    list_obj = gcs_service.list_files(prefix=prefix)

    return list_obj

@redis_cache.cache_method(expiry=3600)
def load_data_with_cache(path):
    logger.info(f"Load data from {path}")
    df = load_file_if_existed(path)
    return df


### Using for sync with GCS
def check_exist_file(path):
    """
        Checks if a file exists in the local file system or in Google Cloud Storage.
        The file must sync to local first
        Using with upload_and_rsync function, download_and_rsync, delete_and_rsync
        Args:
            path (str): The path to the file in Google Cloud Storage.

        Returns:
            bool: True if the file exists, False otherwise.
    """
    # Chẹck exist file in local
    local_path = os.path.join(os.environ['GCS_LOCAL_PATH'], path)
    if os.path.exists(local_path):
        return True
    else:
        is_exist = gcs_service.file_exists(blob_name=path)
        if is_exist:
            # gcs_service.download_file(path, local_path)
            download_and_rsync(path)
            return True
        else:
            return False


def upload_if_not_exist(df, path):
    """
       Uploads a pandas DataFrame to Google Cloud Storage if it does not exist locally.
       The file must sync to local first
       Using with upload_and_rsync function, download_and_rsync, delete_and_rsync

       Args:
           df (pd.DataFrame): The DataFrame to upload.
           path (str): The path to the file in Google Cloud Storage.
        Returns:
            bool: True if the file does not exist and uploaded, False file exists.
       """
    local_path = os.path.join(os.environ['GCS_LOCAL_PATH'], path)
    if not os.path.exists(local_path):
        is_exist = gcs_service.file_exists(blob_name=path)
        if not is_exist:
            upload_and_rsync(df, path)
            return True
        else:
            # gcs_service.download_file(path, local_path)
            download_and_rsync(path)

    return False


def load_file_if_existed(path):
    """
        Downloads a file from Google Cloud Storage if it does not exist locally.
        The file must sync to local first
        Using with upload_and_rsync function, download_and_rsync, delete_and_rsync
        Args:
            path (str): The path to the file in Google Cloud Storage.

        Returns:
            pd.DataFrame: The contents of the file as a pandas DataFrame.
        """
    local_path = os.path.join(os.environ['GCS_LOCAL_PATH'], path)
    if os.path.exists(local_path):
        df = pd.read_csv(local_path)
    elif gcs_service.file_exists(blob_name=path):
        df = download_and_rsync(path)
    else:
        df = pd.DataFrame()

    return df


def upload_and_rsync(df, path):
    """
        Force upload a pandas DataFrame to Google Cloud Storage and syncs it with the local file system.
        Args:
            df (pd.DataFrame): The DataFrame to upload.
            path (str): The path to the file in Google Cloud Storage.

        Returns:
            None
        """
    local_path = os.path.join(os.environ['GCS_LOCAL_PATH'], path)
    gcs_service.upload_from_memory(df, path)

    parent_dir = os.path.dirname(local_path)
    os.makedirs(parent_dir, exist_ok=True)
    df.to_csv(local_path, index=False)


def download_and_rsync(path):
    """
        Force download a file from Google Cloud Storage to the local file system and syncs it.
        Args:
            path (str): The path to the file in Google Cloud Storage.

        Returns:
            pd.DataFrame: The contents of the file as a pandas DataFrame.
    """
    local_path = os.path.join(os.environ['GCS_LOCAL_PATH'], path)
    df = gcs_service.download_file_to_memory(path)
    df = pd.read_csv(BytesIO(df))

    parent_dir = os.path.dirname(local_path)
    os.makedirs(parent_dir, exist_ok=True)
    df.to_csv(local_path, index=False)
    return df


def delete_and_rsync(path):
    """
        Force delete a file from Google Cloud Storage and removes the local copy.
        Args:
            path (str): The path to the file in Google Cloud Storage.

        Returns:
            None
    """
    local_path = os.path.join(os.environ['GCS_LOCAL_PATH'], path)
    gcs_service.delete_file(path)
    if os.path.exists(local_path):
        os.remove(local_path)


###

class BaseTask(Task):
    def run(self, *args, **kwargs):
        if kwargs.get('fail'):
            raise ValueError("Simulated failure!")
        return "Success!"

    def on_failure(self, exc, task_id, args, kwargs, einfo):
        print(f"Task {task_id} failed with exception: {exc}")

    def on_success(self, retval, task_id, args, kwargs):
        print(f"Task {task_id} succeeded with result: {retval}")
